"use client"

import React, { create<PERSON>ontext, use<PERSON>ontext, useReducer, use<PERSON>allback, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { getCurrentMemoryUserId, getDisplayName } from '../auth/userMapping';

// Types
export interface Memory {
  id: string;
  user_id: string;
  title: string;
  content: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface ProfileState {
  userId: string;
  userEmail: string | null;
  displayName: string;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface MemoriesState {
  memories: Memory[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: {
    search: string;
    category: string;
    dateRange: {
      start: string | null;
      end: string | null;
    };
  };
}

interface EvolutionState {
  operations: any[];
  loading: boolean;
  error: string | null;
  analytics: {
    totalOperations: number;
    avgConfidence: number;
    operationTypes: Record<string, number>;
  };
}

interface AppState {
  profile: ProfileState;
  memories: MemoriesState;
  evolution: EvolutionState;
}

// Initial state
const initialState: AppState = {
  profile: {
    userId: '',
    userEmail: null,
    displayName: '',
    isAuthenticated: false,
    isLoading: true,
  },
  memories: {
    memories: [],
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {
      search: '',
      category: 'all',
      dateRange: {
        start: null,
        end: null
      }
    }
  },
  evolution: {
    operations: [],
    loading: false,
    error: null,
    analytics: {
      totalOperations: 0,
      avgConfidence: 0,
      operationTypes: {}
    }
  }
};

// Action types
type Action =
  | { type: 'SET_USER_FROM_AUTH'; payload: User | null }
  | { type: 'SET_PROFILE_LOADING'; payload: boolean }
  | { type: 'CLEAR_USER' }
  | { type: 'SET_MEMORIES'; payload: { memories: Memory[], totalCount: number } }
  | { type: 'ADD_MEMORY'; payload: Memory }
  | { type: 'UPDATE_MEMORY'; payload: Memory }
  | { type: 'REMOVE_MEMORY'; payload: string }
  | { type: 'SET_MEMORIES_LOADING'; payload: boolean }
  | { type: 'SET_MEMORIES_ERROR'; payload: string | null }
  | { type: 'SET_FILTERS'; payload: Partial<MemoriesState['filters']> }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_PAGE_SIZE'; payload: number }
  | { type: 'SET_EVOLUTION_OPERATIONS'; payload: any[] }
  | { type: 'SET_EVOLUTION_LOADING'; payload: boolean }
  | { type: 'SET_EVOLUTION_ERROR'; payload: string | null };

// Reducer
function appReducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'SET_USER_FROM_AUTH': {
      const supabaseUser = action.payload;
      if (supabaseUser) {
        const memoryUserId = getCurrentMemoryUserId(supabaseUser);
        const displayName = getDisplayName(memoryUserId);
        return {
          ...state,
          profile: {
            ...state.profile,
            userId: memoryUserId,
            userEmail: supabaseUser.email || null,
            displayName,
            isAuthenticated: true,
            isLoading: false,
          }
        };
      } else {
        return {
          ...state,
          profile: {
            userId: '',
            userEmail: null,
            displayName: '',
            isAuthenticated: false,
            isLoading: false,
          }
        };
      }
    }
    case 'SET_PROFILE_LOADING':
      return {
        ...state,
        profile: { ...state.profile, isLoading: action.payload }
      };
    case 'CLEAR_USER':
      return {
        ...state,
        profile: {
          userId: '',
          userEmail: null,
          displayName: '',
          isAuthenticated: false,
          isLoading: false,
        }
      };
    case 'SET_MEMORIES':
      return {
        ...state,
        memories: {
          ...state.memories,
          memories: action.payload.memories,
          totalCount: action.payload.totalCount,
          loading: false,
          error: null,
        }
      };
    case 'ADD_MEMORY':
      return {
        ...state,
        memories: {
          ...state.memories,
          memories: [action.payload, ...state.memories.memories],
          totalCount: state.memories.totalCount + 1,
        }
      };
    case 'UPDATE_MEMORY': {
      const index = state.memories.memories.findIndex(m => m.id === action.payload.id);
      if (index !== -1) {
        const updatedMemories = [...state.memories.memories];
        updatedMemories[index] = action.payload;
        return {
          ...state,
          memories: { ...state.memories, memories: updatedMemories }
        };
      }
      return state;
    }
    case 'REMOVE_MEMORY':
      return {
        ...state,
        memories: {
          ...state.memories,
          memories: state.memories.memories.filter(m => m.id !== action.payload),
          totalCount: state.memories.totalCount - 1,
        }
      };
    case 'SET_MEMORIES_LOADING':
      return {
        ...state,
        memories: { ...state.memories, loading: action.payload }
      };
    case 'SET_MEMORIES_ERROR':
      return {
        ...state,
        memories: { ...state.memories, error: action.payload, loading: false }
      };
    case 'SET_FILTERS':
      return {
        ...state,
        memories: {
          ...state.memories,
          filters: { ...state.memories.filters, ...action.payload }
        }
      };
    case 'SET_PAGE':
      return {
        ...state,
        memories: { ...state.memories, currentPage: action.payload }
      };
    case 'SET_PAGE_SIZE':
      return {
        ...state,
        memories: { ...state.memories, pageSize: action.payload, currentPage: 1 }
      };
    case 'SET_EVOLUTION_OPERATIONS':
      return {
        ...state,
        evolution: { ...state.evolution, operations: action.payload }
      };
    case 'SET_EVOLUTION_LOADING':
      return {
        ...state,
        evolution: { ...state.evolution, loading: action.payload }
      };
    case 'SET_EVOLUTION_ERROR':
      return {
        ...state,
        evolution: { ...state.evolution, error: action.payload, loading: false }
      };
    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<Action>;
} | undefined>(undefined);

// Provider
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the context
export function useAppState() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppProvider');
  }
  return context;
}

// Selectors and action creators
export function useAppSelector<T>(selector: (state: AppState) => T): T {
  const { state } = useAppState();
  return selector(state);
}

export function useAppDispatch() {
  const { dispatch } = useAppState();
  return dispatch;
}

// Action creators
export const profileActions = {
  setUserFromAuth: (user: User | null) => ({
    type: 'SET_USER_FROM_AUTH' as const,
    payload: user
  }),
  setLoading: (loading: boolean) => ({
    type: 'SET_PROFILE_LOADING' as const,
    payload: loading
  }),
  clearUser: () => ({ type: 'CLEAR_USER' as const })
};

export const memoriesActions = {
  setMemories: (memories: Memory[], totalCount: number) => ({
    type: 'SET_MEMORIES' as const,
    payload: { memories, totalCount }
  }),
  addMemory: (memory: Memory) => ({
    type: 'ADD_MEMORY' as const,
    payload: memory
  }),
  updateMemory: (memory: Memory) => ({
    type: 'UPDATE_MEMORY' as const,
    payload: memory
  }),
  removeMemory: (id: string) => ({
    type: 'REMOVE_MEMORY' as const,
    payload: id
  }),
  setLoading: (loading: boolean) => ({
    type: 'SET_MEMORIES_LOADING' as const,
    payload: loading
  }),
  setError: (error: string | null) => ({
    type: 'SET_MEMORIES_ERROR' as const,
    payload: error
  }),
  setFilters: (filters: Partial<MemoriesState['filters']>) => ({
    type: 'SET_FILTERS' as const,
    payload: filters
  }),
  setPage: (page: number) => ({
    type: 'SET_PAGE' as const,
    payload: page
  }),
  setPageSize: (pageSize: number) => ({
    type: 'SET_PAGE_SIZE' as const,
    payload: pageSize
  })
};

export const evolutionActions = {
  setOperations: (operations: any[]) => ({
    type: 'SET_EVOLUTION_OPERATIONS' as const,
    payload: operations
  }),
  setLoading: (loading: boolean) => ({
    type: 'SET_EVOLUTION_LOADING' as const,
    payload: loading
  }),
  setError: (error: string | null) => ({
    type: 'SET_EVOLUTION_ERROR' as const,
    payload: error
  })
};