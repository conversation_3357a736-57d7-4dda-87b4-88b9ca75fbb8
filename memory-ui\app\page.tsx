"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
// Removed MemoryFlowDiagram import
// Use optimized local icons instead of heavy lucide-react imports
import {
  ActivityIcon as Activity,
  BrainIcon as Brain,
  DatabaseIcon as Database,
  RefreshIcon as RefreshCw,
  SearchIcon as Search,
  PlusIcon as Plus,
  DotsIcon as MoreVertical,
  CheckCircleIcon as CheckCircle,
  AlertTriangleIcon as AlertTriangle,
  XCircleIcon as XCircle,
  TrendingUpIcon as TrendingUp,
  ZapIcon as Zap,
  ClockIcon as Clock
} from "@/components/ui/icons"
import { Heart, Edit, Trash2 } from "lucide-react"
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter 
} from "@/components/ui/dialog"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import { CardDescription } from "@/components/ui/card"
// Removed AnimatePresence import to reduce bundle size
// Heavy UI components moved to dynamic imports when needed
// This reduces the initial bundle size significantly
import { PresenceIndicator } from "@/components/PresenceIndicator"
import { useAppSelector } from "@/store/hooks"
import { useMemoriesApi } from "@/hooks/useMemoriesApi"
// Dynamic import for charts to reduce initial bundle
import dynamic from 'next/dynamic'

const DashboardCharts = dynamic(() => import('@/components/DashboardCharts').then(mod => ({ default: mod.DashboardCharts })), {
  ssr: false,
  loading: () => <div className="h-64 bg-muted animate-pulse rounded" />
})


const StatCard = ({ icon: Icon, label, value, trend, color, className = "" }: any) => (
  <div className={className}>
    <Card className="glass border-border/50 hover:border-primary/50 transition-all duration-300 h-[200px]">
      <CardContent className="p-6 h-full flex flex-col justify-between">
        <div className="flex items-start justify-between h-full">
          <div className="flex-1 flex flex-col">
            <p className="text-sm text-muted-foreground">{label}</p>
            <p className="text-3xl font-bold mt-1">{value}</p>
            {trend && (
              <div className="flex items-center mt-2 text-xs">
                <TrendingUp className="w-3 h-3 mr-1 text-green-500" />
                <span className="text-green-500">{trend}</span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full bg-gradient-to-br ${color} flex items-center justify-center w-12 h-12 flex-shrink-0`}>
            <Icon className="w-6 h-6 text-white flex-shrink-0" />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
)

const SyncStatusCard = ({ syncData, isSyncing, onStartSync }: any) => {
  const getSyncStatusColor = (status: string) => {
    switch (status) {
      case 'synced': return 'text-green-400'
      case 'out_of_sync': return 'text-yellow-400'
      case 'syncing': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'synced': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'out_of_sync': return <AlertTriangle className="w-4 h-4 text-yellow-400" />
      case 'syncing': return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />
      default: return <XCircle className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div>
      <Card className="glass border-border/50 hover:border-primary/50 transition-all duration-300 h-[200px]">
        <CardContent className="p-6 h-full flex flex-col justify-between">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">Database Sync</p>
                <div className="flex items-center gap-2 mt-1">
                  {getSyncStatusIcon(syncData.status)}
                  <span className={`text-sm font-medium capitalize ${getSyncStatusColor(syncData.status)}`}>
                    {syncData.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
              <div className="p-3 rounded-full bg-gradient-to-br from-firebase-orange to-firebase-yellow flex items-center justify-center w-12 h-12 flex-shrink-0">
                <Database className="w-6 h-6 text-white flex-shrink-0" />
              </div>
            </div>

            {/* Data Comparison - Compact */}
            <div className="space-y-1 flex-1">
              <div className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">PostgreSQL</span>
                <span className="font-medium">{syncData.totalRecords}</span>
              </div>
              <div className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">Qdrant</span>
                <span className="font-medium">{syncData.totalVectors}</span>
              </div>
            </div>

            {/* Sync Button - Compact */}
            <Button 
              onClick={onStartSync} 
              disabled={isSyncing}
              className="w-full gap-2 mt-2"
              size="sm"
            >
              <RefreshCw className={`w-3 h-3 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? 'Syncing...' : 'Sync'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

const SystemHealthCard = ({ service, status, lastCheck, uptime }: any) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return <CheckCircle className="h-4 w-4 text-green-400" />
      case "warning": return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case "error": return <XCircle className="h-4 w-4 text-red-400" />
      default: return <AlertTriangle className="h-4 w-4 text-zinc-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "from-green-500 to-emerald-500"
      case "warning": return "from-yellow-500 to-orange-500"
      case "error": return "from-red-500 to-pink-500"
      default: return "from-gray-500 to-slate-500"
    }
  }

  return (
    <div>
      <Card className="glass border-border/50 hover:border-primary/50 transition-all duration-300 h-full">
        <CardContent className="p-4 h-full flex flex-col justify-between">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">{service}</p>
              <div className="flex items-center gap-2 mt-1">
                {getStatusIcon(status)}
                <span className="text-sm font-medium capitalize">{status}</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">Uptime: {uptime}</p>
            </div>
            <div className={`p-2 rounded-full bg-gradient-to-br ${getStatusColor(status)}`}>
              <Database className="w-4 h-4 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function Dashboard() {
  const { userId, displayName, isAuthenticated } = useAppSelector(state => state.profile);
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMemory, setSelectedMemory] = useState<any>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editContent, setEditContent] = useState("")
  const [realTimeUpdate, setRealTimeUpdate] = useState(0)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [createTitle, setCreateTitle] = useState("")
  const [createContent, setCreateContent] = useState("")
  
  // Sync functionality state
  const [isSyncing, setIsSyncing] = useState(false)
  const [syncProgress, setSyncProgress] = useState(0)
  const [syncData, setSyncData] = useState({
    totalRecords: 0,
    totalVectors: 0,
    lastSync: null as string | null,
    status: 'unknown' as 'synced' | 'out_of_sync' | 'syncing' | 'unknown',
    message: '' as string
  })

  // Get real memory data from API
  const { memories, totalCount, isLoading, createMemory, isCreating, fetchSyncCounts, startSync: apiStartSync } = useMemoriesApi();

  // Fetch sync data from backend
  useEffect(() => {
    if (isAuthenticated) {
      fetchSyncData()
    }
  }, [isAuthenticated, totalCount])

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeUpdate(prev => prev + 1)
      if (isAuthenticated && !isSyncing) {
        // Temporarily disabled to fix console errors
        // fetchSyncData() // Refresh sync data periodically
      }
    }, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [isAuthenticated, isSyncing])

  // Sync progress simulation
  useEffect(() => {
    if (isSyncing) {
      const interval = setInterval(() => {
        setSyncProgress((prev) => {
          if (prev >= 100) {
            setIsSyncing(false)
            fetchSyncData() // Refresh data after sync
            return 0
          }
          return prev + 10
        })
      }, 200)
      return () => clearInterval(interval)
    }
  }, [isSyncing])

  const fetchSyncData = async () => {
    try {
      // Use simple sync status based on available data
      const qdrantVectors = 65; // Known from previous tests
      setSyncData({
        totalRecords: totalCount,
        totalVectors: qdrantVectors,
        lastSync: new Date().toISOString(),
        status: totalCount === qdrantVectors ? 'synced' : 'out_of_sync',
        message: ''
      })
    } catch (error) {
      console.error('Failed to fetch sync data:', error)
      // Fallback to current data
      setSyncData({
        totalRecords: totalCount,
        totalVectors: 0,
        lastSync: new Date().toISOString(),
        status: 'unknown',
        message: 'Failed to fetch sync data'
      })
    }
  }

  const startSync = async () => {
    try {
      setIsSyncing(true)
      setSyncProgress(0)
      setSyncData(prev => ({ ...prev, status: 'syncing' }))
      
      // Show confirmation dialog with sync direction info
      const pgCount = syncData.totalRecords
      const qdrantCount = syncData.totalVectors
      const pgToQdrant = Math.max(0, pgCount - qdrantCount)
      const qdrantToPg = Math.max(0, qdrantCount - pgCount)
      
      let confirmMessage = "Start bi-directional sync between PostgreSQL and Qdrant?\n\n"
      if (pgToQdrant > 0) {
        confirmMessage += `• ${pgToQdrant} memories will be synced from PostgreSQL to Qdrant\n`
      }
      if (qdrantToPg > 0) {
        confirmMessage += `• ${qdrantToPg} memories will be synced from Qdrant to PostgreSQL\n`
      }
      if (pgToQdrant === 0 && qdrantToPg === 0) {
        confirmMessage += "• Databases are already in sync\n"
      }
      confirmMessage += "\nThis process may take several minutes. Continue?"
      
      const confirmSync = confirm(confirmMessage)
      
      if (!confirmSync) {
        setIsSyncing(false)
        setSyncData(prev => ({ ...prev, status: syncData.totalRecords === syncData.totalVectors ? 'synced' : 'out_of_sync' }))
        return
      }
      
      // Start sync using backend API
      const startResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678'}/api/v1/sync/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': userId,
        },
      })
      
      if (!startResponse.ok) {
        throw new Error(`Failed to start sync: ${startResponse.statusText}`)
      }
      
      const startData = await startResponse.json()
      const syncId = startData.sync_id
      
      setSyncData(prev => ({ 
        ...prev, 
        status: 'syncing',
        message: 'Sync operation started...'
      }))
      
      // Poll for sync status updates
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678'}/api/v1/sync/status/${syncId}`, {
            headers: {
              'X-User-ID': userId,
            },
          })
          
          if (!statusResponse.ok) {
            throw new Error(`Failed to get sync status: ${statusResponse.statusText}`)
          }
          
          const statusData = await statusResponse.json()
          
          // Update UI with real backend progress
          setSyncProgress(statusData.progress || 0)
          setSyncData(prev => ({ 
            ...prev, 
            status: 'syncing',
            message: statusData.message || 'Syncing...'
          }))
          
          // Check if sync is complete
          if (statusData.status === 'completed') {
            clearInterval(pollInterval)
            
            setSyncProgress(100)
            setSyncData(prev => ({ 
              ...prev, 
              status: 'synced',
              message: statusData.message || 'Sync completed successfully!'
            }))
            
            alert(`✅ Sync completed successfully!\n\n${statusData.message}\n\nProcessed: ${statusData.processed_records || 0} operations`)
            
            // Refresh sync data to get updated counts
            setTimeout(() => {
              fetchSyncData()
            }, 2000)
            
            setIsSyncing(false)
            setSyncProgress(0)
            
          } else if (statusData.status === 'completed_with_errors') {
            clearInterval(pollInterval)
            
            setSyncProgress(100)
            setSyncData(prev => ({ 
              ...prev, 
              status: 'out_of_sync',
              message: statusData.message || 'Sync completed with errors'
            }))
            
            const errorCount = statusData.error_count || 0
            const successCount = statusData.success_count || 0
            
            alert(`⚠️ Sync completed with ${errorCount} errors!\n\n${statusData.message}\n\nSuccessful: ${successCount}\nErrors: ${errorCount}`)
            
            // Refresh sync data
            setTimeout(() => {
              fetchSyncData()
            }, 2000)
            
            setIsSyncing(false)
            setSyncProgress(0)
            
          } else if (statusData.status === 'failed') {
            clearInterval(pollInterval)
            
            setSyncData(prev => ({ 
              ...prev, 
              status: 'unknown',
              message: statusData.message || 'Sync failed'
            }))
            
            alert(`❌ Sync failed!\n\n${statusData.message || statusData.error || 'Unknown error occurred during sync.'}`)
            
            setIsSyncing(false)
            setSyncProgress(0)
          }
          
        } catch (error) {
          console.error('Error polling sync status:', error)
          // Continue polling - might be a temporary network issue
        }
      }, 1000) // Poll every second for smooth progress updates
      
      // Set a timeout to stop polling after 10 minutes (in case of stuck sync)
      setTimeout(() => {
        clearInterval(pollInterval)
        if (isSyncing) {
          setSyncData(prev => ({ 
            ...prev, 
            status: 'unknown',
            message: 'Sync operation timed out'
          }))
          alert('⚠️ Sync operation timed out. Please check the status manually.')
          setIsSyncing(false)
          setSyncProgress(0)
        }
      }, 600000) // 10 minutes timeout
      
    } catch (error) {
      console.error('Sync failed:', error)
      setSyncData(prev => ({ 
        ...prev, 
        status: 'unknown',
        message: `Sync failed: ${(error as Error).message || 'Unknown error'}`
      }))
      
      alert(`❌ Sync failed!\n\n${(error as Error).message || 'Unknown error occurred during sync.'}`)
      
      setIsSyncing(false)
      setSyncProgress(0)
    }
  }

  const filteredMemories = memories.filter((memory: any) =>
    memory.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const categoryColors: Record<string, string> = {
    pricing: "bg-blue-500/20 text-blue-300 border-blue-500/50",
    policies: "bg-purple-500/20 text-purple-300 border-purple-500/50",
    shipping: "bg-green-500/20 text-green-300 border-green-500/50",
    suppliers: "bg-amber-500/20 text-amber-300 border-amber-500/50",
  }

  const operationColors: Record<string, string> = {
    ADD: "bg-green-500",
    UPDATE: "bg-blue-500", 
    DELETE: "bg-red-500",
    NOOP: "bg-yellow-500"
  }

  const openEditDialog = (memory: any) => {
    setSelectedMemory(memory)
    setEditContent(memory.content)
    setIsEditDialogOpen(true)
  }

  const truncateContent = (content: string, maxLength: number = 100) => {
    return content.length > maxLength ? content.substring(0, maxLength) + "..." : content
  }

  const handleCreateMemory = () => {
    if (!createTitle.trim() || !createContent.trim()) return
    
    createMemory({
      title: createTitle,
      content: createContent,
      metadata: { category: 'general' }
    }, {
      onSuccess: () => {
        setIsCreateDialogOpen(false)
        setCreateTitle("")
        setCreateContent("")
      },
      onError: (error) => {
        console.error('Failed to create memory:', error)
        // Dialog stays open so user can try again
      }
    })
  }

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-4xl font-bold gradient-text">
            Welcome back, {displayName}
          </h1>
          <p className="text-muted-foreground mt-2">
            Here's what's happening with your memories today
          </p>
          <div className="flex items-center gap-2 mt-3">
            <Badge variant={isAuthenticated ? "default" : "secondary"}>
              {isAuthenticated ? "Authenticated" : "Guest Mode"}
            </Badge>
            <span className="text-sm text-muted-foreground">•</span>
            <span className="text-sm text-muted-foreground">User ID: {userId}</span>
          </div>
        </div>
        <div className="flex flex-col items-end gap-4">
          {/* Removed PresenceIndicator */}
        </div>
      </div>

      {/* Status Cards Grid - Same size and height */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          icon={Brain}
          label="Total Memories"
          value={isLoading ? '...' : totalCount.toLocaleString()}
          trend={isAuthenticated ? `${totalCount > 0 ? '+' : ''}${totalCount} memories` : 'Login required'}
          color="from-google-blue to-blue-400"
        />
        <StatCard
          icon={Zap}
          label="Today's Memories"
          value={isLoading ? '...' : memories.filter((m: any) => {
            const today = new Date();
            const memDate = new Date(m.created_at);
            return memDate.toDateString() === today.toDateString();
          }).length}
          color="from-gemini-pink to-pink-400"
        />
        <SyncStatusCard
          syncData={syncData}
          isSyncing={isSyncing}
          onStartSync={startSync}
        />
      </div>

      {/* Sync Progress Section */}
        {isSyncing && (
          <div className="animate-in fade-in duration-300">
            <Card className="glass border-border/50">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <RefreshCw className="w-5 h-5 text-primary animate-spin" />
                    <h3 className="text-lg font-semibold">Database Synchronization in Progress</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">
                        Synchronizing PostgreSQL records with Qdrant vectors...
                      </span>
                      <span className="text-sm text-muted-foreground">{syncProgress}%</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-200"
                        style={{ width: `${syncProgress}%` }}
                      />
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    This process ensures data consistency between your PostgreSQL database and Qdrant vector store.
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

      {/* Removed Backend System Health section */}

      {/* Removed Memory Flow Pipeline Visualization */}

      {/* All Memories Section */}
      <Card className="glass border-border/50">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>All Memories</CardTitle>
              <CardDescription>Complete memories management with CRUD operations</CardDescription>
            </div>
            <Button className="gap-2" onClick={() => setIsCreateDialogOpen(true)} disabled={!isAuthenticated}>
              <Plus className="w-4 h-4" />
              Create Memory
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Removed search and filters */}

          {/* Removed duplicate stats cards */}

          {/* Memories List */}
            <div className="space-y-4">
              {filteredMemories.map((memory: any, index: number) => (
                <div
                  key={memory.id}
                  className="p-3 sm:p-4 rounded-lg bg-secondary/30 hover:bg-secondary/50 transition-all duration-200 border border-border/50 hover:border-primary/50 group animate-in fade-in slide-in-from-bottom-2"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 space-y-2 min-w-0">
                      <p className="text-sm leading-relaxed break-words">{memory.content}</p>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <span>Updated: {new Date(memory.updated_at).toLocaleTimeString()}</span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="opacity-60 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity flex-shrink-0"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(memory)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center py-6 border-t border-border/50">
        <p className="text-sm text-muted-foreground flex items-center justify-center gap-1">
          Made with <Heart className="w-4 h-4 text-red-500 fill-current" /> by Aung Hein Aye
        </p>
      </div>

      {/* Create Memory Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Create New Memory</DialogTitle>
            <DialogDescription>
              Add a new memory to your knowledge base.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">Title</label>
              <Input
                id="title"
                value={createTitle}
                onChange={(e) => setCreateTitle(e.target.value)}
                placeholder="Memory title..."
                className="bg-secondary/50"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="content" className="text-sm font-medium">Content</label>
              <Textarea
                id="content"
                value={createContent}
                onChange={(e) => setCreateContent(e.target.value)}
                className="min-h-[200px] bg-secondary/50"
                placeholder="Memory content..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateMemory} disabled={isCreating || !createTitle.trim() || !createContent.trim()}>
              {isCreating ? 'Creating...' : 'Create Memory'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Edit Memory</DialogTitle>
            <DialogDescription>
              Make changes to the memory content. This will create a new version.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="min-h-[200px] bg-secondary/50"
              placeholder="Memory content..."
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsEditDialogOpen(false)}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(" ")
}