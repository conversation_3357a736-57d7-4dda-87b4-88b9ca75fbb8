"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
// Removed framer-motion for performance - using CSS transitions instead
import {
  Wrench,
  Database,
  HardDrive,
  Trash2,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  Terminal,
  FileText,
  Zap,
  Activity,
  List, // Queue was renamed to List in lucide-react
  Loader2,
  X,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useAppSelector } from "@/store/hooks"

export default function MaintenancePage() {
  const [isRunningTask, setIsRunningTask] = useState<string | null>(null)
  const [queueData, setQueueData] = useState<any>(null)
  const [isLoadingQueue, setIsLoadingQueue] = useState(false)
  const [isClearingQueue, setIsClearingQueue] = useState(false)
  const { isAuthenticated, userId } = useAppSelector(state => state.profile)
  
  // Real system data based on authentication
  const systemHealth = {
    database: { status: isAuthenticated ? "healthy" : "offline", usage: isAuthenticated ? 45 : 0, max: "10 GB" },
    vectorDb: { status: isAuthenticated ? "healthy" : "offline", usage: isAuthenticated ? 62 : 0, max: "2 GB" },
    api: { status: isAuthenticated ? "healthy" : "offline", uptime: isAuthenticated ? "99.9%" : "0%", responseTime: isAuthenticated ? "145ms" : "--" },
    lastBackup: isAuthenticated ? new Date().toISOString() : null,
    nextBackup: isAuthenticated ? new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() : null,
  }

  const maintenanceTasks = isAuthenticated ? [
    { id: 1, name: "Database Vacuum", lastRun: "3 days ago", status: "success", nextRun: "in 4 days" },
    { id: 2, name: "Vector Index Optimization", lastRun: "1 week ago", status: "success", nextRun: "in 3 days" },
    { id: 3, name: "Log Rotation", lastRun: "1 day ago", status: "success", nextRun: "in 6 days" },
    { id: 4, name: "Memory Deduplication", lastRun: "2 weeks ago", status: "warning", nextRun: "overdue" },
  ] : []

  const recentLogs = isAuthenticated ? [
    { id: 1, level: "info", message: `User ${userId} authenticated successfully`, time: new Date().toLocaleTimeString() },
    { id: 2, level: "info", message: "System maintenance dashboard accessed", time: new Date().toLocaleTimeString() },
    { id: 3, level: "info", message: "API endpoints responding normally", time: new Date().toLocaleTimeString() },
  ] : []

  const runTask = (taskName: string) => {
    setIsRunningTask(taskName)
    setTimeout(() => setIsRunningTask(null), 3000)
  }

  const loadQueueStatus = async () => {
    if (!isAuthenticated) return
    
    setIsLoadingQueue(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/health/queue-status`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        const queueData = {
          operationQueue: {
            size: data.operationQueue.size,
            workerRunning: data.operationQueue.workerRunning,
            workerAlive: data.operationQueue.workerAlive,
            operationCounter: data.operationQueue.operationCounter
          },
          degradationBacklog: {
            size: data.degradationBacklog.size,
            maxSize: data.degradationBacklog.maxSize,
            degradedMode: data.degradationBacklog.degradedMode,
            recoveryAttempts: data.degradationBacklog.recoveryAttempts
          },
          activeOperations: {
            count: data.activeOperations.count,
            operations: data.activeOperations.operations
          },
          lastUpdated: data.timestamp
        }
        setQueueData(queueData)
      } else {
        throw new Error(data.error || 'Failed to get queue status')
      }
    } catch (error) {
      console.error('Failed to load queue status:', error)
      setQueueData(null)
    } finally {
      setIsLoadingQueue(false)
    }
  }

  const clearQueue = async () => {
    if (!isAuthenticated) return
    
    setIsClearingQueue(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/health/clear-queue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        console.log('Queue cleared:', data.cleared)
        // Reload queue status after clearing
        await loadQueueStatus()
      } else {
        throw new Error(data.error || 'Failed to clear queue')
      }
    } catch (error) {
      console.error('Failed to clear queue:', error)
    } finally {
      setIsClearingQueue(false)
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case "error":
        return "text-red-400"
      case "warning":
        return "text-yellow-400"
      case "info":
        return "text-blue-400"
      default:
        return "text-gray-400"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "bg-green-500/20 text-green-300 border-green-500/50"
      case "warning":
        return "bg-yellow-500/20 text-yellow-300 border-yellow-500/50"
      case "error":
        return "bg-red-500/20 text-red-300 border-red-500/50"
      default:
        return "bg-gray-500/20 text-gray-300 border-gray-500/50"
    }
  }

  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-4xl font-bold gradient-text">Maintenance</h1>
        <p className="text-muted-foreground mt-2">System health monitoring and maintenance tasks</p>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="glass border-border/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Database className="w-5 h-5" />
              Database Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Badge variant="outline" className={getStatusColor(systemHealth.database.status)}>
                  {systemHealth.database.status}
                </Badge>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Storage Usage</span>
                  <span>{systemHealth.database.usage}%</span>
                </div>
                <Progress value={systemHealth.database.usage} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {(systemHealth.database.usage * 0.1).toFixed(1)} GB / {systemHealth.database.max}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-border/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Vector DB Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Badge variant="outline" className={getStatusColor(systemHealth.vectorDb.status)}>
                  {systemHealth.vectorDb.status}
                </Badge>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Memory Usage</span>
                  <span>{systemHealth.vectorDb.usage}%</span>
                </div>
                <Progress value={systemHealth.vectorDb.usage} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {(systemHealth.vectorDb.usage * 0.02).toFixed(1)} GB / {systemHealth.vectorDb.max}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-border/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="w-5 h-5" />
              API Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Badge variant="outline" className={getStatusColor(systemHealth.api.status)}>
                  {systemHealth.api.status}
                </Badge>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Uptime</span>
                  <span className="font-medium">{systemHealth.api.uptime}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Avg Response</span>
                  <span className="font-medium">{systemHealth.api.responseTime}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different maintenance sections */}
      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="tasks">Maintenance Tasks</TabsTrigger>
          <TabsTrigger value="queue">Queue Management</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
          <TabsTrigger value="backup">Backup & Restore</TabsTrigger>
        </TabsList>

        {/* Maintenance Tasks */}
        <TabsContent value="tasks" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>Scheduled Maintenance Tasks</CardTitle>
              <CardDescription>Automated tasks to keep the system running smoothly</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-4 rounded-lg bg-secondary/30 hover:bg-secondary/50 transition-all duration-200 hover:scale-[1.02]"
                  >
                    <div className="space-y-1">
                      <p className="font-medium">{task.name}</p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>Last run: {task.lastRun}</span>
                        <Badge
                          variant="outline"
                          className={cn(
                            "text-xs",
                            task.status === "success" && "bg-green-500/20 text-green-300 border-green-500/50",
                            task.status === "warning" && "bg-yellow-500/20 text-yellow-300 border-yellow-500/50"
                          )}
                        >
                          {task.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className={cn(
                        "text-sm",
                        task.nextRun === "overdue" ? "text-red-400" : "text-muted-foreground"
                      )}>
                        Next run: {task.nextRun}
                      </span>
                      <Button
                        size="sm"
                        onClick={() => runTask(task.name)}
                        disabled={isRunningTask === task.name}
                      >
                        {isRunningTask === task.name ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Running...
                          </>
                        ) : (
                          <>
                            <Wrench className="w-4 h-4 mr-2" />
                            Run Now
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Queue Management */}
        <TabsContent value="queue" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Memory Operation Queue</CardTitle>
                  <CardDescription>Monitor and manage pending memory operations</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadQueueStatus}
                    disabled={isLoadingQueue}
                    className="gap-2"
                  >
                    {isLoadingQueue ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <RefreshCw className="w-4 h-4" />
                    )}
                    Refresh
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={clearQueue}
                    disabled={isClearingQueue || !queueData || (queueData.operationQueue.size === 0 && queueData.degradationBacklog.size === 0)}
                    className="gap-2"
                  >
                    {isClearingQueue ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                    Clear Queue
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {!queueData ? (
                <div className="text-center py-8">
                  <List className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Click "Refresh" to load queue status</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Queue Status Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="bg-secondary/20">
                      <CardContent className="pt-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">
                            {queueData.operationQueue.size}
                          </div>
                          <p className="text-sm text-muted-foreground">Pending Operations</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-secondary/20">
                      <CardContent className="pt-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-400">
                            {queueData.degradationBacklog.size}
                          </div>
                          <p className="text-sm text-muted-foreground">Backlog Operations</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="bg-secondary/20">
                      <CardContent className="pt-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-400">
                            {queueData.operationQueue.operationCounter}
                          </div>
                          <p className="text-sm text-muted-foreground">Total Processed</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Queue Worker Status */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Queue Worker Status</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center justify-between p-3 rounded-lg bg-secondary/30">
                        <span className="text-sm text-muted-foreground">Worker Running</span>
                        <Badge variant="outline" className={
                          queueData.operationQueue.workerRunning 
                            ? "bg-green-500/20 text-green-300 border-green-500/50"
                            : "bg-red-500/20 text-red-300 border-red-500/50"
                        }>
                          {queueData.operationQueue.workerRunning ? "Active" : "Stopped"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-3 rounded-lg bg-secondary/30">
                        <span className="text-sm text-muted-foreground">Worker Health</span>
                        <Badge variant="outline" className={
                          queueData.operationQueue.workerAlive 
                            ? "bg-green-500/20 text-green-300 border-green-500/50"
                            : "bg-red-500/20 text-red-300 border-red-500/50"
                        }>
                          {queueData.operationQueue.workerAlive ? "Healthy" : "Error"}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Degradation Mode Status */}
                  {queueData.degradationBacklog.degradedMode && (
                    <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/50">
                      <div className="flex gap-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-500 flex-shrink-0" />
                        <div className="space-y-1">
                          <p className="text-sm font-medium">Degraded Mode Active</p>
                          <p className="text-sm text-muted-foreground">
                            Vector store unavailable. Operations are being queued for later processing.
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Backlog: {queueData.degradationBacklog.size} / {queueData.degradationBacklog.maxSize} operations
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Active Operations */}
                  {queueData.activeOperations.count > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-medium">Active Operations</h4>
                      <div className="space-y-2">
                        {queueData.activeOperations.operations.map((op: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-blue-500/10 border border-blue-500/50">
                            <div className="flex items-center gap-3">
                              <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                              <span className="text-sm">{op.type}: {op.description}</span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {new Date(op.startTime).toLocaleTimeString()}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Last Updated */}
                  <div className="text-xs text-muted-foreground text-right">
                    Last updated: {new Date(queueData.lastUpdated).toLocaleString()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Logs */}
        <TabsContent value="logs" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>System Logs</CardTitle>
                  <CardDescription>Recent system events and notifications</CardDescription>
                </div>
                <Button variant="outline" size="sm" className="gap-2">
                  <Download className="w-4 h-4" />
                  Export Logs
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 font-mono text-sm">
                {recentLogs.map((log) => (
                  <div
                    key={log.id}
                    className="flex items-start gap-3 p-3 rounded bg-secondary/20 hover:bg-secondary/30 transition-colors"
                  >
                    <span className="text-muted-foreground">{log.time}</span>
                    <span className={cn("uppercase font-bold", getLogLevelColor(log.level))}>
                      [{log.level}]
                    </span>
                    <span className="flex-1">{log.message}</span>
                  </div>
                ))}
              </div>
              <div className="mt-4 flex justify-center">
                <Button variant="outline" size="sm">
                  Load More Logs
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Backup & Restore */}
        <TabsContent value="backup" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
              <CardDescription>Manage system backups and recovery options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Last Backup</p>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">
                      {systemHealth.lastBackup ? new Date(systemHealth.lastBackup).toLocaleString() : 'Never'}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Next Scheduled Backup</p>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-sm">
                      {systemHealth.nextBackup ? new Date(systemHealth.nextBackup).toLocaleString() : 'Not scheduled'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <Button className="flex-1 gap-2">
                  <Download className="w-4 h-4" />
                  Create Backup Now
                </Button>
                <Button variant="outline" className="flex-1 gap-2">
                  <Upload className="w-4 h-4" />
                  Restore from Backup
                </Button>
              </div>

              <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/50">
                <div className="flex gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-500 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Backup Storage</p>
                    <p className="text-sm text-muted-foreground">
                      Current backup location: Supabase Storage
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Retention policy: Keep last 7 daily backups
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}