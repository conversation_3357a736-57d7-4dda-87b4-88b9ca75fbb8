// Test user mapping functions
const { getCurrentMemoryUserId, getDisplayName } = require('../lib/auth/userMapping')

// Mock Supabase user objects
const aungUser = { email: '<EMAIL>', id: '123' }
const yohannaUser = { email: '<EMAIL>', id: '456' }

console.log('Testing user mapping:')
console.log('Aung email:', aungUser.email)
console.log('Aung memory ID:', getCurrentMemoryUserId(aungUser))
console.log('Aung display name:', getDisplayName(getCurrentMemoryUserId(aungUser)))
console.log()
console.log('Yohanna email:', yohannaUser.email)
console.log('Yohanna memory ID:', getCurrentMemoryUserId(yohannaUser))
console.log('Yohanna display name:', getDisplayName(getCurrentMemoryUserId(yohannaUser)))