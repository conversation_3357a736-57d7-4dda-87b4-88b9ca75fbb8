#!/bin/bash

# Memory Master Deployment Script for Ubuntu/Linux
# Requires: <PERSON><PERSON>, <PERSON><PERSON> Compose

set -e  # Exit on any error

# Default values
ENVIRONMENT="dev"
CLEAN=false
LOGS=false
STOP=false

# Color output functions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Help function
show_help() {
    echo "Memory Master Deployment Script for Ubuntu/Linux"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Set environment (dev|prod) [default: dev]"
    echo "  -c, --clean             Clean up containers and volumes before deploy"
    echo "  -l, --logs              Show logs and exit"
    echo "  -s, --stop              Stop services and exit"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Deploy in development mode"
    echo "  $0 -e prod             # Deploy in production mode"
    echo "  $0 -c -e prod          # Clean deploy in production mode"
    echo "  $0 -l                  # Show logs"
    echo "  $0 -s                  # Stop services"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -l|--logs)
            LOGS=true
            shift
            ;;
        -s|--stop)
            STOP=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'prod'"
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    info "Checking prerequisites..."
    
    # Check if running as root (not recommended)
    if [[ $EUID -eq 0 ]]; then
        warning "Running as root is not recommended for security reasons"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker not found. Please install Docker first."
        info "Install with: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
        exit 1
    fi
    
    # Check if user is in docker group
    if ! groups | grep -q docker; then
        warning "User not in docker group. You may need to use sudo or add user to docker group:"
        info "sudo usermod -aG docker \$USER && newgrp docker"
    fi
    
    # Check Docker Compose
    if ! docker compose version &> /dev/null; then
        error "Docker Compose not found. Please install Docker Compose."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running. Please start Docker service:"
        info "sudo systemctl start docker"
        exit 1
    fi
    
    success "All prerequisites met"
}

# Clean up function
cleanup() {
    info "Cleaning up existing containers and volumes..."
    
    if docker compose -f docker-compose.$ENVIRONMENT.yml down --volumes --remove-orphans; then
        success "Containers and volumes cleaned up"
    else
        warning "Cleanup had some issues, but continuing..."
    fi
    
    if [[ "$CLEAN" == true ]]; then
        info "Performing system cleanup..."
        docker system prune -f
        success "System cleanup completed"
    fi
}

# Deploy function
deploy() {
    info "Deploying Memory Master in $ENVIRONMENT mode..."
    
    # Check environment file
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        if [[ ! -f ".env.production" ]]; then
            error "Production environment file not found."
            info "Please copy .env.production.template to .env.production and configure it:"
            info "cp .env.production.template .env.production"
            exit 1
        fi
        ENV_FILE=".env.production"
    else
        ENV_FILE=".env"
    fi
    
    info "Using environment file: $ENV_FILE"
    
    # Build and start services
    info "Building and starting services..."
    if docker compose -f docker-compose.$ENVIRONMENT.yml --env-file "$ENV_FILE" up --build -d; then
        success "Services started successfully"
    else
        error "Failed to start services"
        info "Checking logs for more information..."
        docker compose -f docker-compose.$ENVIRONMENT.yml logs --tail=50
        exit 1
    fi
    
    # Wait for services to be healthy
    info "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    info "Checking service status..."
    if docker compose -f docker-compose.$ENVIRONMENT.yml ps | grep -q "Up"; then
        success "Services are running"
    else
        warning "Some services might have issues"
        docker compose -f docker-compose.$ENVIRONMENT.yml ps
    fi
    
    success "Deployment completed!"
    echo ""
    info "Services available at:"
    echo -e "  ${YELLOW}🌐 Web UI: http://localhost:3210${NC}"
    echo -e "  ${YELLOW}🔗 API: http://localhost:8765${NC}"
    echo ""
}

# Show logs function
show_logs() {
    info "Showing recent logs..."
    docker compose -f docker-compose.$ENVIRONMENT.yml logs --tail=100 -f
}

# Stop services function
stop_services() {
    info "Stopping services..."
    if docker compose -f docker-compose.$ENVIRONMENT.yml down; then
        success "Services stopped"
    else
        error "Failed to stop services"
        exit 1
    fi
}

# Main execution
main() {
    info "Memory Master Deployment Script for Ubuntu/Linux"
    info "Environment: $ENVIRONMENT"
    echo ""
    
    if [[ "$STOP" == true ]]; then
        stop_services
        exit 0
    fi
    
    if [[ "$LOGS" == true ]]; then
        show_logs
        exit 0
    fi
    
    check_prerequisites
    
    if [[ "$CLEAN" == true ]]; then
        cleanup
    fi
    
    deploy
    
    success "Deployment script completed successfully!"
    echo ""
    info "Useful commands:"
    echo "  📋 Follow logs: docker compose -f docker-compose.$ENVIRONMENT.yml logs -f"
    echo "  🛑 Stop services: $0 -s"
    echo "  🔄 Restart: $0 -c -e $ENVIRONMENT"
    echo "  📊 Service status: docker compose -f docker-compose.$ENVIRONMENT.yml ps"
}

# Run main function
main "$@"