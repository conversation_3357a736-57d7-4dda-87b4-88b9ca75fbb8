// Reset Yohanna's password
const { createClient } = require('@supabase/supabase-js')

const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://192.168.1.218:8000',
  SUPABASE_SERVICE_KEY
)

async function resetYohannaPassword() {
  console.log('Resetting Yohanna password...')
  
  try {
    // Get Yohanna's user ID
    const { data: users, error: listError } = await supabase.auth.admin.listUsers()
    if (listError) {
      console.error('Error listing users:', listError)
      return
    }
    
    const yohannaUser = users.users.find(u => u.email === '<EMAIL>')
    if (!yohannaUser) {
      console.error('Yohanna user not found')
      return
    }
    
    console.log('Found Yohanna:', yohannaUser.id)
    
    // Reset password
    const { data, error } = await supabase.auth.admin.updateUserById(
      yohannaUser.id,
      {
        password: 'password123'
      }
    )
    
    if (error) {
      console.error('Error:', error)
      return
    }
    
    console.log('Password updated successfully!')
    console.log('Yohanna can now login with:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

resetYohannaPassword()