services:
  memory-api:
    build: 
      context: api/
      dockerfile: Dockerfile.prod
    container_name: memory-api-prod
    restart: unless-stopped
    environment:
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - SUPABASE_DATABASE_URL=${SUPABASE_DATABASE_URL}
      
      # Vector Database
      - QDRANT_HOST=${QDRANT_HOST:-*************}
      - QDRANT_PORT=${QDRANT_PORT:-6333}
      
      # API Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - API_KEY=${API_KEY}
      - USER=${USER:-aung<PERSON>aye}
      - MCP_USER_ID=${MCP_USER_ID:-aungheinaye}
      
      # Security & Features
      - AUTH_ENABLED=${AUTH_ENABLED:-true}
      - MIGRATION_MODE=${MIGRATION_MODE:-supabase_only}
      - EXPECTED_SUPABASE_ANON_KEY=${EXPECTED_SUPABASE_ANON_KEY}
      
      # MinIO Configuration
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_CONSOLE=${MINIO_CONSOLE}
    ports:
      - "${API_PORT:-8765}:8765"
    volumes:
      # Data persistence for logs and cache
      - memory_api_data:/usr/src/app/data
      - memory_api_logs:/usr/src/app/logs
    networks:
      - memory-network
    # No dependencies needed - using remote Qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  memory-ui:
    build:
      context: memory-ui/
      dockerfile: Dockerfile.prod
    container_name: memory-ui-prod
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8765}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    ports:
      - "${UI_PORT:-3210}:3210"
    networks:
      - memory-network
    depends_on:
      memory-api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3210"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Qdrant service removed - using remote instance at *************:6333

volumes:
  memory_api_data:
    driver: local
  memory_api_logs:
    driver: local

networks:
  memory-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16