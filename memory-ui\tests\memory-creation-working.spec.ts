import { test, expect } from '@playwright/test';

test.describe('Memory Creation Test - Working Version', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Wait for the main dashboard content to be visible
    await expect(page.getByText('Memory Master')).toBeVisible();
  });

  test('should create memory successfully with proper form fields', async ({ page }) => {
    // Step 1: Navigate to memories page
    console.log('Step 1: Navigating to memories page...');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of memories page
    await page.screenshot({ path: 'test-results/working-01-memories-page.png' });
    
    // Step 2: Click the "Create Memory" button
    console.log('Step 2: Clicking Create Memory button...');
    await page.click('button:has-text("Create Memory")');
    
    // Wait for the dialog to open
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Take screenshot of the create memory dialog
    await page.screenshot({ path: 'test-results/working-02-create-memory-dialog.png' });
    
    // Step 3: Fill in the form with test data using the correct selectors
    console.log('Step 3: Filling in the form...');
    
    // Fill in the title field using the id selector
    await page.fill('#title', 'Test Memory Creation Fix');
    
    // Fill in the content field using the id selector
    await page.fill('#content', 'This memory was created to test the UUID to string conversion fix in the MemoryResponse model.');
    
    // Take screenshot of filled form
    await page.screenshot({ path: 'test-results/working-03-form-filled.png' });
    
    // Step 4: Submit the form
    console.log('Step 4: Submitting the form...');
    await page.click('button:has-text("Create Memory")');
    
    // Step 5: Wait for the memory creation to complete
    console.log('Step 5: Waiting for memory creation to complete...');
    
    // Wait for the dialog to close (indicating successful creation)
    await page.waitForSelector('[role="dialog"]', { state: 'detached', timeout: 15000 });
    
    // Wait for any loading states to complete
    await page.waitForLoadState('networkidle');
    
    // Take screenshot after submission
    await page.screenshot({ path: 'test-results/working-04-after-submission.png' });
    
    // Step 6: Verify the memory was created successfully
    console.log('Step 6: Verifying memory creation...');
    
    // Look for the memory in the list
    await expect(page.locator('text="Test Memory Creation Fix"')).toBeVisible({ timeout: 10000 });
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/working-05-final-verification.png' });
    
    console.log('✅ Memory creation test completed successfully');
  });

  test('should handle API connection and show appropriate status', async ({ page }) => {
    // Navigate to memories page
    await page.goto('/');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Check for connection status indicators
    const connectionBadge = page.locator('text="Real-time", text="Offline"');
    await expect(connectionBadge).toBeVisible({ timeout: 5000 });
    
    // Take screenshot of connection status
    await page.screenshot({ path: 'test-results/working-connection-status.png' });
    
    console.log('✅ Connection status indicator is visible');
  });

  test('should validate form fields properly', async ({ page }) => {
    // Navigate to memories page
    await page.goto('/');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Click Create Memory button
    await page.click('button:has-text("Create Memory")');
    
    // Wait for dialog
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Try to submit with empty form
    await page.click('button:has-text("Create Memory")');
    
    // Wait for potential toast/error message
    await page.waitForTimeout(2000);
    
    // Take screenshot of validation state
    await page.screenshot({ path: 'test-results/working-validation-test.png' });
    
    console.log('✅ Form validation test completed');
  });
});