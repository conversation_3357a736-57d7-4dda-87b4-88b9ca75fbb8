// Simple JavaScript test to verify memory creation
const puppeteer = require('puppeteer');

async function testMemoryCreation() {
  console.log('Starting memory creation test...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the application
    console.log('1. Navigating to memories page...');
    await page.goto('http://localhost:3001/');
    await page.waitForLoadState('networkidle');
    
    // Click on Memories
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/puppeteer-01-memories-page.png' });
    
    // Click Create Memory button
    console.log('2. Clicking Create Memory button...');
    await page.click('button:has-text("Create Memory")');
    
    // Wait for dialog
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/puppeteer-02-dialog.png' });
    
    // Fill form
    console.log('3. Filling form...');
    await page.fill('#title', 'Test Memory Creation Fix');
    await page.fill('#content', 'This memory was created to test the UUID to string conversion fix in the MemoryResponse model.');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/puppeteer-03-form-filled.png' });
    
    // Submit form
    console.log('4. Submitting form...');
    await page.click('button:has-text("Create Memory")');
    
    // Wait for dialog to close
    await page.waitForSelector('[role="dialog"]', { state: 'detached', timeout: 15000 });
    
    // Wait for completion
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/puppeteer-04-after-submit.png' });
    
    // Check for success
    const successToast = await page.locator('text="Memory created successfully"').count();
    const memoryInList = await page.locator('text="Test Memory Creation Fix"').count();
    const errorToast = await page.locator('text="Failed to create memory"').count();
    
    console.log('Results:');
    console.log('- Success toast:', successToast > 0);
    console.log('- Memory in list:', memoryInList > 0);
    console.log('- Error toast:', errorToast > 0);
    
    if (successToast > 0 || memoryInList > 0) {
      console.log('✅ Memory creation test PASSED');
    } else if (errorToast > 0) {
      console.log('❌ Memory creation test FAILED');
    } else {
      console.log('⚠️ Memory creation test UNCLEAR');
    }
    
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await browser.close();
  }
}

testMemoryCreation();