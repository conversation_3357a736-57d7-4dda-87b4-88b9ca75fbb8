const { chromium } = require('playwright');

async function takeScreenshots() {
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Set viewport size
  await page.setViewportSize({ width: 1920, height: 1080 });
  
  try {
    // Screenshot 1: Main dashboard
    console.log('Taking screenshot of main dashboard...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle' });
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    await page.screenshot({ 
      path: 'dashboard-final.png', 
      fullPage: true 
    });
    console.log('✓ Dashboard screenshot saved as dashboard-final.png');
    
    // Screenshot 2: Memories page
    console.log('Taking screenshot of memories page...');
    await page.goto('http://localhost:3001/memories', { waitUntil: 'networkidle' });
    
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    await page.screenshot({ 
      path: 'memories-final.png', 
      fullPage: true 
    });
    console.log('✓ Memories page screenshot saved as memories-final.png');
    
  } catch (error) {
    console.error('Error taking screenshots:', error);
  } finally {
    await browser.close();
  }
}

takeScreenshots().catch(console.error);