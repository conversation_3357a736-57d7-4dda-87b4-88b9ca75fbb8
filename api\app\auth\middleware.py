from fastapi import Depends, HTTPException, status, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Union
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User
from app.auth.supabase import supabase_client
import logging
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

security = HTTPBearer(auto_error=False)

class AuthenticatedUser:
    """Represents an authenticated user"""
    def __init__(self, user_id: str, email: str, supabase_user_id: str, is_authenticated: bool = True):
        self.user_id = user_id
        self.email = email
        self.supabase_user_id = supabase_user_id
        self.is_authenticated = is_authenticated

class DefaultUser:
    """Represents the default user when authentication is disabled"""
    def __init__(self):
        self.user_id = "default-user"
        self.email = "<EMAIL>"
        self.supabase_user_id = None
        self.is_authenticated = False

def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    x_user_id: Optional[str] = Header(None, alias="X-User-ID"),
    db: Session = Depends(get_db)
) -> Union[AuthenticatedUser, DefaultUser]:
    """
    Get the current user from JWT token, X-User-ID header, or return default user.
    Supports both authenticated and non-authenticated modes.
    """
    # Check for X-User-ID header first (for backward compatibility)
    if x_user_id:
        # Look up user by user_id string
        db_user = db.query(User).filter(User.user_id == x_user_id).first()
        if db_user:
            return AuthenticatedUser(
                user_id=db_user.user_id,
                email=db_user.email or "<EMAIL>",
                supabase_user_id=str(db_user.supabase_user_id) if db_user.supabase_user_id else None,
                is_authenticated=True
            )
        else:
            logger.warning(f"User not found for X-User-ID: {x_user_id}")
    
    # If authentication is disabled, return default user
    if not supabase_client.auth_enabled:
        return DefaultUser()
    
    # If no credentials provided, return default user (backward compatibility)
    if not credentials:
        return DefaultUser()
    
    # Verify JWT token
    token = credentials.credentials
    payload = supabase_client.verify_jwt_token(token)
    
    if not payload:
        # Invalid token - in backward compatibility mode, return default user
        logger.warning("Invalid JWT token provided, falling back to default user")
        return DefaultUser()
    
    try:
        # Extract user information from JWT payload
        supabase_user_id = payload.get("sub")
        email = payload.get("email")
        
        if not supabase_user_id or not email:
            logger.warning("Missing user information in JWT payload")
            return DefaultUser()
        
        # Get or create user in our database
        user = get_or_create_user(db, supabase_user_id, email, payload)
        
        if not user:
            logger.error("Failed to get or create user")
            return DefaultUser()
        
        return AuthenticatedUser(
            user_id=user.user_id,
            email=user.email,
            supabase_user_id=supabase_user_id
        )
        
    except Exception as e:
        logger.error(f"Error processing authenticated user: {e}")
        return DefaultUser()

def get_or_create_user(db: Session, supabase_user_id: str, email: str, jwt_payload: dict) -> Optional[User]:
    """
    Get existing user or create new user in our database based on Supabase user ID.
    """
    try:
        # First, try to find user by supabase_user_id
        user = db.query(User).filter(User.supabase_user_id == supabase_user_id).first()
        
        if user:
            # Update user information if needed
            if user.email != email:
                user.email = email
            user.last_sign_in_at = datetime.utcnow()
            user.email_verified = jwt_payload.get("email_confirmed_at") is not None
            db.commit()
            db.refresh(user)
            return user
        
        # If not found by supabase_user_id, try to find by email (for migration)
        user = db.query(User).filter(User.email == email).first()
        
        if user:
            # Link existing user with Supabase
            user.supabase_user_id = uuid.UUID(supabase_user_id)
            user.last_sign_in_at = datetime.utcnow()
            user.email_verified = jwt_payload.get("email_confirmed_at") is not None
            db.commit()
            db.refresh(user)
            return user
        
        # Create new user
        new_user = User(
            user_id=f"user_{uuid.uuid4().hex[:8]}",
            email=email,
            supabase_user_id=uuid.UUID(supabase_user_id),
            email_verified=jwt_payload.get("email_confirmed_at") is not None,
            last_sign_in_at=datetime.utcnow(),
            name=jwt_payload.get("user_metadata", {}).get("full_name") or email.split("@")[0]
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        logger.info(f"Created new user: {new_user.user_id} ({email})")
        return new_user
        
    except Exception as e:
        logger.error(f"Error in get_or_create_user: {e}")
        db.rollback()
        return None

def require_authentication(
    user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user)
) -> AuthenticatedUser:
    """
    Dependency that requires authentication. Raises HTTPException if user is not authenticated.
    Use this for endpoints that absolutely require authentication.
    """
    if not user.is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

# Backward compatibility - these functions maintain the existing API signature
def get_user_id_from_auth(user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user)) -> str:
    """
    Extract user_id for backward compatibility with existing endpoints.
    """
    return user.user_id

def get_authenticated_user_context(
    user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> tuple[Union[AuthenticatedUser, DefaultUser], User]:
    """
    Get authenticated user context and corresponding User model.
    Returns (auth_user, db_user) tuple.
    """
    # Get the User model from database
    if user.is_authenticated:
        db_user = db.query(User).filter(User.user_id == user.user_id).first()
    else:
        # For default user, get or create default user entry
        db_user = db.query(User).filter(User.user_id == "default-user").first()
        if not db_user:
            db_user = User(
                user_id="default-user",
                email="<EMAIL>",
                name="Default User"
            )
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
    
    return user, db_user