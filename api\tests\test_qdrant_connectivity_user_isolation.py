"""
Comprehensive Qdrant Connectivity and User Isolation Tests

This module provides comprehensive testing for:
- Remote Qdrant connectivity to *************:6333
- User-specific collection creation and isolation
- Data isolation between different users (aung<PERSON><PERSON><PERSON>, yohanna)
- Error handling when Qdrant is unavailable
- Collection naming validation and security
- Memory operations with user-specific collections
- Degraded mode functionality
- Recovery mechanisms

Test Categories:
1. Remote Connectivity Tests
2. User Isolation Tests  
3. Collection Management Tests
4. Error Handling and Degraded Mode Tests
5. Security and Validation Tests
6. Integration Tests
"""

import unittest
import os
import time
import uuid
import threading
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager
from typing import Dict, List, Optional

# Import system under test
from app.utils.memory import (
    MemoryClientSingleton, 
    reset_memory_client, 
    get_memory_client,
    get_user_collection_name,
    get_default_memory_config
)
from app.memory_service import MemoryService
from app.config import USER_ID

# Import test helpers
from tests.utils.test_helpers import (
    MockVectorStore, 
    TestConfigManager,
    ConcurrencyTestHelper,
    wait_for_condition
)


class TestQdrantConnectivity(unittest.TestCase):
    """Test suite for remote Qdrant connectivity."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Reset singleton for clean test state
        reset_memory_client()
        self.original_env = os.environ.copy()
        
        # Set test environment variables for remote Qdrant
        os.environ['QDRANT_HOST'] = '*************'
        os.environ['QDRANT_PORT'] = '6333'
        
    def tearDown(self):
        """Clean up after tests."""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
        reset_memory_client()
        
    def test_remote_qdrant_host_configuration(self):
        """Test that remote Qdrant host is properly configured."""
        config = get_default_memory_config()
        
        # Verify remote Qdrant configuration
        self.assertEqual(config['vector_store']['config']['host'], '*************')
        self.assertEqual(config['vector_store']['config']['port'], 6333)
        
        print(f"✅ Remote Qdrant configured: {config['vector_store']['config']['host']}:{config['vector_store']['config']['port']}")
        
    def test_user_specific_collection_naming(self):
        """Test user-specific collection name generation."""
        # Test with default user
        default_collection = get_user_collection_name()
        expected_default = f"{os.environ.get('USER', USER_ID)}_memories"
        self.assertEqual(default_collection, expected_default)
        
        # Test with specific users
        aungheinaye_collection = get_user_collection_name("aungheinaye")
        self.assertEqual(aungheinaye_collection, "aungheinaye_memories")
        
        yohanna_collection = get_user_collection_name("yohanna")
        self.assertEqual(yohanna_collection, "yohanna_memories")
        
        # Verify different users get different collections
        self.assertNotEqual(aungheinaye_collection, yohanna_collection)
        
        print(f"✅ User collections: {aungheinaye_collection}, {yohanna_collection}")
        
    @patch('qdrant_client.QdrantClient')
    def test_remote_qdrant_connectivity_success(self, mock_qdrant_client):
        """Test successful connection to remote Qdrant."""
        # Mock successful Qdrant connection
        mock_client_instance = Mock()
        mock_collections = Mock()
        mock_collections.collections = [
            Mock(name="aungheinaye_memories"),
            Mock(name="yohanna_memories")
        ]
        mock_client_instance.get_collections.return_value = mock_collections
        mock_qdrant_client.return_value = mock_client_instance
        
        # Test connectivity through memory client
        with patch('mem0.Memory') as mock_memory:
            mock_memory_instance = Mock()
            mock_memory_instance.vector_store.client = mock_client_instance
            mock_memory.from_config.return_value = mock_memory_instance
            
            client = get_memory_client()
            
            # Verify client was created
            self.assertIsNotNone(client)
            mock_memory.from_config.assert_called_once()
            
            print("✅ Remote Qdrant connectivity test passed")
            
    @patch('qdrant_client.QdrantClient')
    def test_remote_qdrant_connectivity_failure(self, mock_qdrant_client):
        """Test handling of remote Qdrant connection failures."""
        # Mock connection failure
        mock_qdrant_client.side_effect = ConnectionError("Failed to connect to *************:6333")
        
        # Test that degraded mode is entered
        singleton = MemoryClientSingleton()
        
        with patch.object(singleton, '_enter_degraded_mode') as mock_degraded:
            # Attempt to get client - should trigger degraded mode
            config = get_default_memory_config()
            client = singleton.get_client(config)
            
            # Verify degraded mode was entered
            mock_degraded.assert_called()
            
            print("✅ Remote Qdrant connection failure handled correctly")
            
    def test_connectivity_monitoring_setup(self):
        """Test that connectivity monitoring is properly configured."""
        singleton = MemoryClientSingleton()
        
        # Test monitoring interval configuration
        singleton.set_monitoring_interval(60)  # 1 minute for testing
        self.assertEqual(singleton._monitoring_interval, 60)
        
        # Test alerts configuration
        singleton.enable_connectivity_alerts(True)
        self.assertTrue(singleton._connectivity_alerts_enabled)
        
        # Get connectivity status
        status = singleton.get_connectivity_status()
        
        # Verify status structure
        self.assertIn('status', status)
        self.assertIn('degraded_mode', status)
        self.assertIn('monitoring_running', status)
        
        print("✅ Connectivity monitoring configured correctly")


class TestUserIsolation(unittest.TestCase):
    """Test suite for user data isolation."""
    
    def setUp(self):
        """Set up test fixtures."""
        reset_memory_client()
        self.memory_service = MemoryService()
        
        # Test users
        self.user_aungheinaye = "aungheinaye"
        self.user_yohanna = "yohanna"
        
    def tearDown(self):
        """Clean up after tests."""
        reset_memory_client()
        
    def test_user_collection_isolation(self):
        """Test that users get isolated collections."""
        # Get collection names for different users
        collection_aungheinaye = get_user_collection_name(self.user_aungheinaye)
        collection_yohanna = get_user_collection_name(self.user_yohanna)
        
        # Verify collections are different
        self.assertNotEqual(collection_aungheinaye, collection_yohanna)
        
        # Verify naming convention
        self.assertEqual(collection_aungheinaye, "aungheinaye_memories")
        self.assertEqual(collection_yohanna, "yohanna_memories")
        
        print(f"✅ User isolation verified: {collection_aungheinaye} != {collection_yohanna}")
        
    @patch('app.utils.memory.Memory')
    def test_memory_operations_user_isolation(self, mock_memory_class):
        """Test that memory operations respect user isolation."""
        # Create separate mock clients for each user
        mock_client_aungheinaye = Mock()
        mock_client_yohanna = Mock()
        
        # Track which collections are accessed
        accessed_collections = []
        
        def track_collection_access(*args, **kwargs):
            config_dict = args[0] if args else kwargs.get('config_dict', {})
            if 'vector_store' in config_dict:
                collection = config_dict['vector_store']['config'].get('collection_name')
                if collection:
                    accessed_collections.append(collection)
            return mock_client_aungheinaye if 'aungheinaye' in str(config_dict) else mock_client_yohanna
            
        mock_memory_class.from_config.side_effect = track_collection_access
        
        # Get clients for different users
        config_aungheinaye = get_default_memory_config()
        config_aungheinaye['vector_store']['config']['collection_name'] = get_user_collection_name(self.user_aungheinaye)
        
        config_yohanna = get_default_memory_config()
        config_yohanna['vector_store']['config']['collection_name'] = get_user_collection_name(self.user_yohanna)
        
        # Get clients (simulating different user sessions)
        singleton = MemoryClientSingleton()
        client1 = singleton.get_client(config_aungheinaye)
        
        # Reset singleton to simulate new user session
        reset_memory_client()
        singleton2 = MemoryClientSingleton()
        client2 = singleton2.get_client(config_yohanna)
        
        # Verify different collections were accessed
        expected_collections = [
            "aungheinaye_memories",
            "yohanna_memories"
        ]
        
        for expected in expected_collections:
            self.assertIn(expected, accessed_collections, 
                         f"Collection {expected} should have been accessed")
            
        print(f"✅ Memory operations isolated by user: {accessed_collections}")
        
    def test_concurrent_user_operations(self):
        """Test concurrent operations by different users."""
        helper = ConcurrencyTestHelper()
        
        def user_operation(user_id):
            """Simulate user-specific operation."""
            collection_name = get_user_collection_name(user_id)
            
            # Simulate memory operation
            with patch('app.utils.memory.Memory') as mock_memory:
                mock_client = Mock()
                mock_client.add.return_value = {"id": str(uuid.uuid4()), "status": "success"}
                mock_memory.from_config.return_value = mock_client
                
                config = get_default_memory_config()
                config['vector_store']['config']['collection_name'] = collection_name
                
                singleton = MemoryClientSingleton()
                client = singleton.get_client(config)
                
                if client:
                    result = client.add(f"Test memory for {user_id}")
                    return {"user_id": user_id, "collection": collection_name, "result": result}
                    
                return {"user_id": user_id, "error": "Failed to get client"}
        
        # Run concurrent operations for both users
        users = [self.user_aungheinaye, self.user_yohanna] * 10  # 20 operations total
        args_list = [(user,) for user in users]
        
        results = helper.run_concurrent_operations(user_operation, args_list, max_workers=10)
        
        # Verify no errors occurred
        self.assertEqual(results["error_count"], 0, f"Errors occurred: {results['errors']}")
        
        # Verify users accessed different collections
        collections_used = set()
        for result in results["results"]:
            if "collection" in result:
                collections_used.add(result["collection"])
                
        expected_collections = {"aungheinaye_memories", "yohanna_memories"}
        self.assertEqual(collections_used, expected_collections)
        
        print(f"✅ Concurrent user operations isolated: {collections_used}")


class TestCollectionManagement(unittest.TestCase):
    """Test suite for collection management and validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        reset_memory_client()
        
    def tearDown(self):
        """Clean up after tests."""
        reset_memory_client()
        
    def test_collection_name_validation(self):
        """Test collection name validation and security."""
        # Test valid user IDs
        valid_users = ["aungheinaye", "yohanna", "user123", "test_user"]
        
        for user_id in valid_users:
            collection_name = get_user_collection_name(user_id)
            self.assertTrue(collection_name.endswith("_memories"))
            self.assertTrue(collection_name.startswith(user_id))
            
        # Test edge cases
        edge_cases = {
            "": f"{USER_ID}_memories",  # Empty string should use default
            None: f"{USER_ID}_memories",  # None should use default
        }
        
        for user_id, expected in edge_cases.items():
            collection_name = get_user_collection_name(user_id)
            self.assertEqual(collection_name, expected)
            
        print("✅ Collection name validation passed")
        
    def test_collection_security_isolation(self):
        """Test that users cannot access other users' collections."""
        # This test verifies the naming convention prevents cross-user access
        
        users = ["aungheinaye", "yohanna", "malicious_user"]
        collections = {}
        
        for user in users:
            collection = get_user_collection_name(user)
            collections[user] = collection
            
            # Verify collection name contains user ID
            self.assertIn(user, collection)
            
        # Verify all collections are unique
        collection_names = list(collections.values())
        unique_names = set(collection_names)
        self.assertEqual(len(collection_names), len(unique_names), 
                        "All collection names should be unique")
        
        # Verify no user can guess another user's collection name easily
        for user1, collection1 in collections.items():
            for user2, collection2 in collections.items():
                if user1 != user2:
                    self.assertNotEqual(collection1, collection2)
                    # Verify user1's ID is not in user2's collection name
                    self.assertNotIn(user1, collection2)
                    
        print(f"✅ Collection security verified: {collections}")
        
    @patch('qdrant_client.QdrantClient')
    def test_collection_creation_on_demand(self, mock_qdrant_client):
        """Test that collections are created on-demand for new users."""
        # Mock Qdrant client
        mock_client_instance = Mock()
        mock_collections = Mock()
        mock_collections.collections = []  # No existing collections
        mock_client_instance.get_collections.return_value = mock_collections
        mock_client_instance.create_collection.return_value = True
        mock_qdrant_client.return_value = mock_client_instance
        
        # Test collection creation
        with patch('mem0.Memory') as mock_memory:
            mock_memory_instance = Mock()
            mock_memory_instance.vector_store.client = mock_client_instance
            mock_memory.from_config.return_value = mock_memory_instance
            
            # Get client for new user
            config = get_default_memory_config()
            config['vector_store']['config']['collection_name'] = get_user_collection_name("new_user")
            
            singleton = MemoryClientSingleton()
            client = singleton.get_client(config)
            
            # Verify client was created (collection would be created on first use)
            self.assertIsNotNone(client)
            
        print("✅ Collection creation on-demand tested")


class TestErrorHandlingAndDegradedMode(unittest.TestCase):
    """Test suite for error handling and degraded mode functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        reset_memory_client()
        self.memory_service = MemoryService()
        
    def tearDown(self):
        """Clean up after tests."""
        reset_memory_client()
        
    def test_qdrant_unavailable_degraded_mode(self):
        """Test degraded mode when Qdrant is unavailable."""
        singleton = MemoryClientSingleton()
        
        # Simulate Qdrant unavailable
        with patch('mem0.Memory') as mock_memory:
            mock_memory.from_config.side_effect = ConnectionError("Cannot connect to Qdrant")
            
            # Attempt to get client
            config = get_default_memory_config()
            client = singleton.get_client(config)
            
            # Client should be None, but degraded mode should be active
            self.assertIsNone(client)
            self.assertTrue(singleton._degraded_mode)
            self.assertIn("Qdrant", singleton._degradation_reason)
            
        print("✅ Degraded mode activated when Qdrant unavailable")
        
    def test_memory_operations_in_degraded_mode(self):
        """Test memory operations continue in degraded mode."""
        singleton = MemoryClientSingleton()
        
        # Force degraded mode
        singleton._enter_degraded_mode("Test degraded mode")
        
        # Mock database-only storage
        with patch.object(singleton, '_store_in_database_only') as mock_db_store:
            mock_db_store.return_value = {
                'results': [{
                    'id': str(uuid.uuid4()),
                    'memory': 'Test content',
                    'event': 'ADD',
                    'fallback_mode': True
                }]
            }
            
            # Test memory addition in degraded mode
            result = singleton.add_memory_with_degradation(
                "Test memory content",
                metadata={"test": True},
                user_id="aungheinaye"
            )
            
            # Verify operation completed in degraded mode
            self.assertIsNotNone(result)
            self.assertIn('results', result)
            self.assertTrue(result['results'][0]['fallback_mode'])
            
            # Verify operation was added to backlog
            self.assertGreater(len(singleton._operation_backlog), 0)
            
        print("✅ Memory operations work in degraded mode")
        
    def test_recovery_from_degraded_mode(self):
        """Test recovery from degraded mode when Qdrant becomes available."""
        singleton = MemoryClientSingleton()
        
        # Start in degraded mode
        singleton._enter_degraded_mode("Test failure")
        self.assertTrue(singleton._degraded_mode)
        
        # Add some operations to backlog
        singleton._add_to_backlog('add_memory', 'Test content 1')
        singleton._add_to_backlog('add_memory', 'Test content 2')
        backlog_size = len(singleton._operation_backlog)
        self.assertGreater(backlog_size, 0)
        
        # Mock successful recovery
        with patch('mem0.Memory') as mock_memory:
            mock_client = Mock()
            mock_client.search.return_value = {"results": []}
            mock_client.add.return_value = {"id": "test", "status": "success"}
            mock_memory.from_config.return_value = mock_client
            singleton._client = mock_client
            
            # Mock backlog processing
            with patch.object(singleton, '_process_backlog') as mock_process:
                # Attempt recovery
                recovery_success = singleton._attempt_vector_store_recovery()
                
                # Verify recovery
                if recovery_success:
                    self.assertFalse(singleton._degraded_mode)
                    mock_process.assert_called_once()
                    
        print("✅ Recovery from degraded mode tested")
        
    def test_connectivity_monitoring_alerts(self):
        """Test connectivity monitoring and alerting."""
        singleton = MemoryClientSingleton()
        
        # Enable alerts
        singleton.enable_connectivity_alerts(True)
        
        # Test alert sending
        with patch('builtins.print') as mock_print:
            with patch('logging.warning') as mock_log:
                # Send connectivity alert
                singleton._send_connectivity_alert("disconnected", "Test disconnection")
                
                # Verify alert was logged and printed
                mock_log.assert_called_once()
                mock_print.assert_called_once()
                
                # Test alert cooldown
                alert_time = singleton._last_connectivity_alert
                self.assertIsNotNone(alert_time)
                
                # Try to send another alert immediately (should be blocked)
                singleton._send_connectivity_alert("disconnected", "Another alert")
                
                # Should still be only one call due to cooldown
                self.assertEqual(mock_log.call_count, 1)
                
        print("✅ Connectivity monitoring alerts tested")
        
    def test_connectivity_metrics_collection(self):
        """Test connectivity metrics collection and reporting."""
        singleton = MemoryClientSingleton()
        
        # Add some mock connectivity history
        current_time = time.time()
        test_history = [
            (current_time - 300, True),   # 5 min ago: connected
            (current_time - 240, True),   # 4 min ago: connected  
            (current_time - 180, False),  # 3 min ago: disconnected
            (current_time - 120, False),  # 2 min ago: disconnected
            (current_time - 60, True),    # 1 min ago: connected
            (current_time, True)          # now: connected
        ]
        
        singleton._connectivity_history.clear()
        for timestamp, connected in test_history:
            singleton._connectivity_history.append((timestamp, connected))
            
        # Get connectivity metrics
        metrics = singleton.get_connectivity_metrics()
        
        # Verify metrics calculation
        self.assertEqual(metrics['total_checks'], 6)
        self.assertEqual(metrics['successful_checks'], 4)
        self.assertEqual(metrics['failed_checks'], 2)
        self.assertAlmostEqual(metrics['uptime_percentage'], 4/6, places=2)
        
        # Get connectivity status
        status = singleton.get_connectivity_status()
        self.assertIn('status', status)
        self.assertIn('uptime_percentage', status)
        
        print("✅ Connectivity metrics collection tested")


class TestSecurityAndValidation(unittest.TestCase):
    """Test suite for security and validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        reset_memory_client()
        
    def tearDown(self):
        """Clean up after tests."""
        reset_memory_client()
        
    def test_user_id_sanitization(self):
        """Test that user IDs are properly sanitized for collection names."""
        # Test potentially problematic user IDs
        test_cases = {
            "user-with-dashes": "user-with-dashes_memories",
            "user.with.dots": "user.with.dots_memories",
            "user_with_underscores": "user_with_underscores_memories",
            "123numeric": "123numeric_memories",
        }
        
        for user_id, expected in test_cases.items():
            collection_name = get_user_collection_name(user_id)
            self.assertEqual(collection_name, expected)
            
        print("✅ User ID sanitization tested")
        
    def test_collection_name_length_limits(self):
        """Test collection name length constraints."""
        # Test very long user ID
        long_user_id = "a" * 100
        collection_name = get_user_collection_name(long_user_id)
        
        # Collection name should be reasonable length
        self.assertLess(len(collection_name), 200)  # Qdrant has limits
        self.assertTrue(collection_name.endswith("_memories"))
        
        print(f"✅ Collection name length tested: {len(collection_name)} chars")
        
    def test_concurrent_user_isolation_security(self):
        """Test that concurrent operations maintain security isolation."""
        helper = ConcurrencyTestHelper()
        
        # Track which collections are accessed by which threads
        access_log = []
        access_lock = threading.Lock()
        
        def isolated_operation(user_id):
            """Test operation that should be isolated per user."""
            collection_name = get_user_collection_name(user_id)
            thread_id = threading.current_thread().ident
            
            with access_lock:
                access_log.append({
                    'thread_id': thread_id,
                    'user_id': user_id,
                    'collection': collection_name,
                    'timestamp': time.time()
                })
                
            # Simulate some processing time
            time.sleep(0.01)
            
            return {'user_id': user_id, 'collection': collection_name}
        
        # Run concurrent operations with different users
        users = ['aungheinaye', 'yohanna'] * 25  # 50 operations
        args_list = [(user,) for user in users]
        
        results = helper.run_concurrent_operations(
            isolated_operation, args_list, max_workers=20
        )
        
        # Verify no errors
        self.assertEqual(results["error_count"], 0)
        
        # Analyze access patterns
        aungheinaye_accesses = [log for log in access_log if log['user_id'] == 'aungheinaye']
        yohanna_accesses = [log for log in access_log if log['user_id'] == 'yohanna']
        
        # Verify each user only accessed their own collection
        for access in aungheinaye_accesses:
            self.assertEqual(access['collection'], 'aungheinaye_memories')
            
        for access in yohanna_accesses:
            self.assertEqual(access['collection'], 'yohanna_memories')
            
        print(f"✅ Concurrent isolation security: {len(aungheinaye_accesses)} + {len(yohanna_accesses)} operations")


class TestIntegrationScenarios(unittest.TestCase):
    """Integration tests for real-world scenarios."""
    
    def setUp(self):
        """Set up test fixtures."""
        reset_memory_client()
        self.memory_service = MemoryService()
        
        # Set remote Qdrant environment
        os.environ['QDRANT_HOST'] = '*************'
        os.environ['QDRANT_PORT'] = '6333'
        
    def tearDown(self):
        """Clean up after tests."""
        reset_memory_client()
        
    def test_full_user_session_simulation(self):
        """Test complete user session with memory operations."""
        # Simulate aungheinaye user session
        user_id = "aungheinaye"
        app_name = "test_app"
        
        with patch('app.utils.memory.Memory') as mock_memory:
            # Mock successful operations
            mock_client = Mock()
            mock_client.add.return_value = {
                'results': [{
                    'id': str(uuid.uuid4()),
                    'memory': 'Test memory',
                    'event': 'ADD'
                }]
            }
            mock_client.search.return_value = {'results': []}
            mock_client.get_all.return_value = {'results': []}
            mock_memory.from_config.return_value = mock_client
            
            # Test memory addition
            success, message, result = self.memory_service.add_memory(
                "Test memory for aungheinaye", user_id, app_name
            )
            
            if success:
                self.assertTrue(success)
                self.assertIn("Successfully added", message)
                
                # Test memory search
                success, message, results = self.memory_service.search_memory(
                    "test", user_id, app_name, limit=10
                )
                
                if success:
                    self.assertTrue(success)
                    
                # Test memory listing
                success, message, memories = self.memory_service.list_memories(
                    user_id, app_name
                )
                
                if success:
                    self.assertTrue(success)
                    
        print("✅ Full user session simulation completed")
        
    def test_multi_user_concurrent_session(self):
        """Test concurrent sessions by multiple users."""
        users = ["aungheinaye", "yohanna"]
        app_name = "concurrent_test_app"
        
        helper = ConcurrencyTestHelper()
        
        def user_session(user_id):
            """Simulate a complete user session."""
            operations_completed = []
            
            with patch('app.utils.memory.Memory') as mock_memory:
                mock_client = Mock()
                mock_client.add.return_value = {
                    'results': [{
                        'id': str(uuid.uuid4()),
                        'memory': f'Memory for {user_id}',
                        'event': 'ADD'
                    }]
                }
                mock_client.search.return_value = {'results': []}
                mock_memory.from_config.return_value = mock_client
                
                # Add memory
                success, message, result = self.memory_service.add_memory(
                    f"Test memory for {user_id}", user_id, app_name
                )
                if success:
                    operations_completed.append("add")
                    
                # Search memory
                success, message, results = self.memory_service.search_memory(
                    "test", user_id, app_name
                )
                if success:
                    operations_completed.append("search")
                    
                # List memories
                success, message, memories = self.memory_service.list_memories(
                    user_id, app_name
                )
                if success:
                    operations_completed.append("list")
                    
            return {
                'user_id': user_id,
                'operations': operations_completed,
                'collection': get_user_collection_name(user_id)
            }
        
        # Run concurrent user sessions
        args_list = [(user,) for user in users * 10]  # 20 sessions total
        results = helper.run_concurrent_operations(
            user_session, args_list, max_workers=10
        )
        
        # Verify no errors
        self.assertEqual(results["error_count"], 0, f"Errors: {results['errors']}")
        
        # Verify users maintained isolation
        collections_used = set()
        for result in results["results"]:
            collections_used.add(result['collection'])
            
        expected_collections = {"aungheinaye_memories", "yohanna_memories"}
        self.assertEqual(collections_used, expected_collections)
        
        print(f"✅ Multi-user concurrent sessions: {results['success_count']} operations")
        
    def test_degraded_mode_recovery_scenario(self):
        """Test complete degraded mode and recovery scenario."""
        singleton = MemoryClientSingleton()
        
        # Phase 1: Normal operation
        with patch('mem0.Memory') as mock_memory:
            mock_client = Mock()
            mock_client.search.return_value = {"results": []}
            mock_memory.from_config.return_value = mock_client
            singleton._client = mock_client
            
            self.assertFalse(singleton._degraded_mode)
            
        # Phase 2: Qdrant becomes unavailable
        singleton._enter_degraded_mode("Qdrant connectivity lost")
        self.assertTrue(singleton._degraded_mode)
        
        # Phase 3: Operations continue in degraded mode
        with patch.object(singleton, '_store_in_database_only') as mock_db_store:
            mock_db_store.return_value = {
                'results': [{
                    'id': str(uuid.uuid4()),
                    'memory': 'Degraded mode memory',
                    'event': 'ADD',
                    'fallback_mode': True
                }]
            }
            
            # Add memories during outage
            for i in range(5):
                result = singleton.add_memory_with_degradation(
                    f"Emergency memory {i}",
                    user_id="aungheinaye"
                )
                self.assertIsNotNone(result)
                
            # Verify backlog accumulation
            self.assertGreater(len(singleton._operation_backlog), 0)
            
        # Phase 4: Qdrant recovers
        with patch('mem0.Memory') as mock_memory:
            mock_client = Mock()
            mock_client.search.return_value = {"results": []}
            mock_client.add.return_value = {"id": "recovered", "status": "success"}
            mock_memory.from_config.return_value = mock_client
            singleton._client = mock_client
            
            # Mock successful recovery
            with patch.object(singleton, '_process_backlog') as mock_process:
                singleton._exit_degraded_mode()
                
                # Verify recovery
                self.assertFalse(singleton._degraded_mode)
                mock_process.assert_called_once()
                
        print("✅ Complete degraded mode recovery scenario tested")


# Additional utility tests
class TestUtilityFunctions(unittest.TestCase):
    """Test suite for utility functions."""
    
    def setUp(self):
        """Setup for each test."""
        reset_memory_client()
        
    def tearDown(self):
        """Teardown for each test."""
        reset_memory_client()
        
    def test_remote_qdrant_configuration_env(self):
        """Test remote Qdrant configuration with environment variables."""
        # Set environment for remote Qdrant
        original_env = os.environ.copy()
        try:
            os.environ['QDRANT_HOST'] = '*************'
            os.environ['QDRANT_PORT'] = '6333'
            
            config = get_default_memory_config()
            
            self.assertEqual(config['vector_store']['config']['host'], '*************')
            self.assertEqual(config['vector_store']['config']['port'], 6333)
            
        finally:
            # Restore original environment
            os.environ.clear()
            os.environ.update(original_env)
            
    def test_user_isolation_naming(self):
        """Test user isolation through collection naming."""
        collection1 = get_user_collection_name("aungheinaye")
        collection2 = get_user_collection_name("yohanna")
        
        self.assertEqual(collection1, "aungheinaye_memories")
        self.assertEqual(collection2, "yohanna_memories")
        self.assertNotEqual(collection1, collection2)
        
    def test_connectivity_monitoring_basic(self):
        """Test basic connectivity monitoring functionality."""
        singleton = MemoryClientSingleton()
        
        # Test monitoring configuration
        singleton.set_monitoring_interval(30)
        self.assertEqual(singleton._monitoring_interval, 30)
        
        # Test status retrieval
        status = singleton.get_connectivity_status()
        self.assertIn('status', status)
        self.assertIn('degraded_mode', status)


if __name__ == '__main__':
    # Run unittest suite
    unittest.main(verbosity=2)