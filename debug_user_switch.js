// Debug script to understand the user switching flow
const http = require('http');

// Mock data similar to what the UserSwitcher creates
const mockAungUser = {
    id: 'aungheinaye',
    email: '<EMAIL>',
    user_metadata: { display_name: 'Aung Hein Aye' },
    app_metadata: {},
    aud: 'authenticated',
    confirmation_sent_at: null,
    confirmed_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    email_confirmed_at: new Date().toISOString(),
    identities: [],
    last_sign_in_at: new Date().toISOString(),
    phone: null,
    recovery_sent_at: null,
    role: 'authenticated',
    updated_at: new Date().toISOString()
};

console.log('Mock user object for Aung Hein Aye:');
console.log('====================================');
console.log(JSON.stringify(mockAungUser, null, 2));

// Test the user mapping logic
function getCurrentMemoryUserId(supabaseUser) {
    const SUPABASE_TO_MEMORY_USER_MAP = {
        '<EMAIL>': 'aungheinaye',
        '<EMAIL>': 'yohanna'
    };
    
    if (!supabaseUser?.email) return 'guest';
    return SUPABASE_TO_MEMORY_USER_MAP[supabaseUser.email] || 'guest';
}

function getDisplayName(memoryUserId) {
    switch (memoryUserId) {
        case 'aungheinaye': return 'Aung Hein Aye';
        case 'yohanna': return 'Yohanna';
        default: return 'Guest User';
    }
}

const mappedUserId = getCurrentMemoryUserId(mockAungUser);
const displayName = getDisplayName(mappedUserId);

console.log('\nAfter user mapping:');
console.log('===================');
console.log('mappedUserId:', mappedUserId);
console.log('displayName:', displayName);
console.log('isAuthenticated:', mappedUserId !== 'guest');

// Test API call with mapped user ID
function testAPICall(userId) {
    const options = {
        hostname: 'localhost',
        port: 5678,
        path: '/api/v1/memories?limit=1',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-User-ID': userId
        }
    };

    const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                console.log('\nAPI Response for user:', userId);
                console.log('==============================');
                console.log('Total memories:', response.totalCount);
                console.log('API call successful:', res.statusCode === 200);
            } catch (e) {
                console.log('API error:', e.message);
            }
        });
    });

    req.on('error', (e) => { console.log('Request error:', e.message); });
    req.end();
}

testAPICall(mappedUserId);