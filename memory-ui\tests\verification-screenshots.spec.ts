import { test, expect } from '@playwright/test';

test.describe('Memory Master v2 - UI Verification Screenshots', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the main dashboard
    await page.goto('http://localhost:3001');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Wait a bit more for any animations or async content
    await page.waitForTimeout(2000);
  });

  test('1. Take screenshot of main dashboard page', async ({ page }) => {
    console.log('Taking screenshot of main dashboard...');
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'screenshots/verification-main-dashboard.png',
      fullPage: true 
    });
    
    // Verify key elements are present
    await expect(page.locator('text=Memory Master')).toBeVisible();
    
    console.log('Main dashboard screenshot saved');
  });

  test('2. Take screenshot of memories page', async ({ page }) => {
    console.log('Navigating to memories page...');
    
    // Navigate to memories page
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    // Take screenshot of memories page
    await page.screenshot({ 
      path: 'screenshots/verification-memories-page.png',
      fullPage: true 
    });
    
    console.log('Memories page screenshot saved');
  });

  test('3. Test Create Memory button functionality', async ({ page }) => {
    console.log('Testing Create Memory button...');
    
    // Navigate to memories page first
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Look for Create Memory button
    const createButton = page.locator('button', { hasText: 'Create Memory' });
    await expect(createButton).toBeVisible();
    
    // Click the Create Memory button
    await createButton.click();
    
    // Wait for dialog to appear
    await page.waitForTimeout(1000);
    
    // Take screenshot of the create memory dialog
    await page.screenshot({ 
      path: 'screenshots/verification-create-memory-dialog.png',
      fullPage: true 
    });
    
    console.log('Create Memory dialog screenshot saved');
  });

  test('4. Verify UI changes - Main Dashboard', async ({ page }) => {
    console.log('Verifying UI changes on main dashboard...');
    
    // Check for absence of red disconnection sign in left nav bar
    const disconnectionSign = page.locator('[data-testid="disconnection-sign"]');
    await expect(disconnectionSign).not.toBeVisible();
    
    // Check for absence of active user status display
    const activeUserStatus = page.locator('[data-testid="active-user-status"]');
    await expect(activeUserStatus).not.toBeVisible();
    
    // Check for absence of Demo User Switcher
    const userSwitcher = page.locator('[data-testid="user-switcher"]');
    await expect(userSwitcher).not.toBeVisible();
    
    // Check for absence of Active Users counter block
    const activeUsersCounter = page.locator('[data-testid="active-users-counter"]');
    await expect(activeUsersCounter).not.toBeVisible();
    
    // Check for absence of Backend System Health status cards container
    const healthCards = page.locator('[data-testid="health-cards"]');
    await expect(healthCards).not.toBeVisible();
    
    // Take screenshot showing these verifications
    await page.screenshot({ 
      path: 'screenshots/verification-ui-changes-main.png',
      fullPage: true 
    });
    
    console.log('UI changes verification completed for main dashboard');
  });

  test('5. Verify UI changes - Memories Page', async ({ page }) => {
    console.log('Verifying UI changes on memories page...');
    
    // Navigate to memories page
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    // Check for absence of duplicate stats cards under All Memories section
    const statsCards = page.locator('[data-testid="stats-cards"]');
    const statsCardsCount = await statsCards.count();
    console.log(`Found ${statsCardsCount} stats card sections`);
    
    // Check for absence of Filters and Export buttons
    const filtersButton = page.locator('button', { hasText: 'Filters' });
    await expect(filtersButton).not.toBeVisible();
    
    const exportButton = page.locator('button', { hasText: 'Export' });
    await expect(exportButton).not.toBeVisible();
    
    // Check that Create Memory button is present and working
    const createButton = page.locator('button', { hasText: 'Create Memory' });
    await expect(createButton).toBeVisible();
    
    // Take screenshot showing these verifications
    await page.screenshot({ 
      path: 'screenshots/verification-ui-changes-memories.png',
      fullPage: true 
    });
    
    console.log('UI changes verification completed for memories page');
  });

  test('6. Verify Data Flow Animation', async ({ page }) => {
    console.log('Verifying data flow animation...');
    
    // Look for data flow animation elements
    const dataFlowAnimation = page.locator('[data-testid="data-flow-animation"]');
    
    // Take screenshot of data flow area
    await page.screenshot({ 
      path: 'screenshots/verification-data-flow.png',
      fullPage: true 
    });
    
    console.log('Data flow animation verification completed');
  });

  test('7. Verify Memory Representation', async ({ page }) => {
    console.log('Verifying memory representation...');
    
    // Navigate to memories page
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    // Look for memory representation elements
    const memoryRows = page.locator('[data-testid="memory-row"]');
    
    // Take screenshot of memory representation
    await page.screenshot({ 
      path: 'screenshots/verification-memory-representation.png',
      fullPage: true 
    });
    
    console.log('Memory representation verification completed');
  });

  test('8. Complete verification summary', async ({ page }) => {
    console.log('Creating complete verification summary...');
    
    // Take final comprehensive screenshot of main dashboard
    await page.screenshot({ 
      path: 'screenshots/verification-final-dashboard.png',
      fullPage: true 
    });
    
    // Navigate to memories and take final screenshot
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    await page.screenshot({ 
      path: 'screenshots/verification-final-memories.png',
      fullPage: true 
    });
    
    console.log('Complete verification summary created');
  });
});