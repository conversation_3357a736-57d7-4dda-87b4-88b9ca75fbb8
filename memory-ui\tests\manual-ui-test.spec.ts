import { test, expect } from '@playwright/test';

test.describe('Manual UI Test', () => {
  test('should manually test the Create Memory dialog', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Take initial screenshot
    await page.screenshot({ path: 'test-results/manual-01-initial.png' });
    
    // Click on Memories in the sidebar
    await page.click('text=Memories');
    await page.waitForTimeout(2000);
    
    // Take screenshot of memories page
    await page.screenshot({ path: 'test-results/manual-02-memories-page.png' });
    
    // Print the page content to see what's happening
    const bodyText = await page.locator('body').textContent();
    console.log('Page content:', bodyText?.substring(0, 500));
    
    // Check if we can see the Create Memory button
    const createButton = page.locator('button:has-text("Create Memory")');
    const buttonExists = await createButton.count();
    console.log('Create Memory button count:', buttonExists);
    
    if (buttonExists > 0) {
      // Try to click the first Create Memory button
      await createButton.first().click();
      await page.waitForTimeout(3000);
      
      // Take screenshot after click
      await page.screenshot({ path: 'test-results/manual-03-after-click.png' });
      
      // Check if dialog exists
      const dialogExists = await page.locator('[role="dialog"]').count();
      console.log('Dialog count:', dialogExists);
      
      if (dialogExists > 0) {
        // Dialog opened successfully
        console.log('✅ Dialog opened successfully');
        
        // Check for form fields
        const titleField = page.locator('#title');
        const contentField = page.locator('#content');
        
        const titleExists = await titleField.count();
        const contentExists = await contentField.count();
        
        console.log('Title field exists:', titleExists > 0);
        console.log('Content field exists:', contentExists > 0);
        
        if (titleExists > 0 && contentExists > 0) {
          // Fill the form
          await titleField.fill('Test Memory Creation Fix');
          await contentField.fill('This memory was created to test the UUID to string conversion fix in the MemoryResponse model.');
          
          // Take screenshot of filled form
          await page.screenshot({ path: 'test-results/manual-04-form-filled.png' });
          
          // Try to submit
          const submitButton = page.locator('button:has-text("Create Memory")').last();
          await submitButton.click();
          
          // Wait for response
          await page.waitForTimeout(5000);
          
          // Take final screenshot
          await page.screenshot({ path: 'test-results/manual-05-after-submit.png' });
          
          console.log('✅ Form submitted');
        } else {
          console.log('❌ Form fields not found');
        }
      } else {
        console.log('❌ Dialog did not open');
      }
    } else {
      console.log('❌ Create Memory button not found');
    }
  });
});