import { test, expect } from '@playwright/test';

test.describe('Memory Creation Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Wait for the main dashboard content to be visible
    await expect(page.getByText('Memory Master')).toBeVisible();
  });

  test('should create memory successfully with UUID to string conversion fix', async ({ page }) => {
    // Step 1: Navigate to the dashboard
    console.log('Step 1: Navigating to the dashboard...');
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of initial dashboard
    await page.screenshot({ path: 'test-results/01-initial-dashboard.png' });
    
    // Step 2: Navigate to memories page
    console.log('Step 2: Navigating to memories page...');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of memories page
    await page.screenshot({ path: 'test-results/02-memories-page.png' });
    
    // Step 3: Click the "Create Memory" button
    console.log('Step 3: Clicking Create Memory button...');
    
    // Look for the Create Memory button with multiple selectors
    const createMemoryButton = page.locator('button:has-text("Create Memory")').first();
    await expect(createMemoryButton).toBeVisible({ timeout: 10000 });
    await createMemoryButton.click();
    
    // Wait for the dialog to open
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Take screenshot of the create memory dialog
    await page.screenshot({ path: 'test-results/03-create-memory-dialog.png' });
    
    // Step 4: Fill in the form with test data
    console.log('Step 4: Filling in the form...');
    
    // Fill in the title field
    const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
    await expect(titleInput).toBeVisible({ timeout: 5000 });
    await titleInput.fill('Test Memory Creation Fix');
    
    // Fill in the content field
    const contentTextarea = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
    await expect(contentTextarea).toBeVisible({ timeout: 5000 });
    await contentTextarea.fill('This memory was created to test the UUID to string conversion fix in the MemoryResponse model.');
    
    // Take screenshot of filled form
    await page.screenshot({ path: 'test-results/04-form-filled.png' });
    
    // Step 5: Submit the form
    console.log('Step 5: Submitting the form...');
    
    // Look for submit button with various possible text options
    const submitButton = page.locator('button:has-text("Create"), button:has-text("Submit"), button:has-text("Add"), button[type="submit"]').first();
    await expect(submitButton).toBeVisible({ timeout: 5000 });
    await submitButton.click();
    
    // Step 6: Wait for the memory creation to complete
    console.log('Step 6: Waiting for memory creation to complete...');
    
    // Wait for the dialog to close (indicating successful creation)
    await page.waitForSelector('[role="dialog"]', { state: 'detached', timeout: 15000 });
    
    // Wait for any loading states to complete
    await page.waitForLoadState('networkidle');
    
    // Take screenshot after submission
    await page.screenshot({ path: 'test-results/05-after-submission.png' });
    
    // Step 7: Verify the memory was created successfully
    console.log('Step 7: Verifying memory creation...');
    
    // Look for success indicators
    const successElements = [
      page.locator('text="Test Memory Creation Fix"'),
      page.locator('text="Memory created successfully"'),
      page.locator('text="successfully created"'),
      page.locator('[role="alert"]:has-text("success")'),
      page.locator('.toast:has-text("success")'),
    ];
    
    let successFound = false;
    for (const element of successElements) {
      try {
        await expect(element).toBeVisible({ timeout: 3000 });
        successFound = true;
        console.log('✅ Success indicator found:', await element.textContent());
        break;
      } catch (e) {
        // Continue checking other elements
      }
    }
    
    // Also check if we can see the memory in the list
    try {
      await expect(page.locator('text="Test Memory Creation Fix"')).toBeVisible({ timeout: 5000 });
      console.log('✅ Memory found in the list');
      successFound = true;
    } catch (e) {
      console.log('⚠️ Memory not immediately visible in list');
    }
    
    // Step 8: Take final screenshot
    await page.screenshot({ path: 'test-results/06-final-verification.png' });
    
    // Check for any error messages
    const errorElements = [
      page.locator('text="error"'),
      page.locator('text="Error"'),
      page.locator('text="failed"'),
      page.locator('text="Failed"'),
      page.locator('[role="alert"]:has-text("error")'),
      page.locator('.toast:has-text("error")'),
    ];
    
    let errorFound = false;
    for (const element of errorElements) {
      try {
        await expect(element).toBeVisible({ timeout: 1000 });
        errorFound = true;
        console.log('❌ Error found:', await element.textContent());
        break;
      } catch (e) {
        // No error found, which is good
      }
    }
    
    // Final assertion
    if (errorFound) {
      throw new Error('Memory creation failed - error message detected');
    }
    
    if (!successFound) {
      console.log('⚠️ No clear success indicator found, but no errors detected either');
      // This might still be successful if the system doesn't show explicit success messages
    }
    
    console.log('✅ Memory creation test completed');
  });
  
  test('should handle memory creation errors gracefully', async ({ page }) => {
    // Navigate to memories page
    await page.goto('/');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Click Create Memory button
    const createMemoryButton = page.locator('button:has-text("Create Memory")').first();
    await expect(createMemoryButton).toBeVisible({ timeout: 10000 });
    await createMemoryButton.click();
    
    // Wait for dialog
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Try to submit with empty form to test validation
    const submitButton = page.locator('button:has-text("Create"), button:has-text("Submit"), button:has-text("Add"), button[type="submit"]').first();
    await expect(submitButton).toBeVisible({ timeout: 5000 });
    await submitButton.click();
    
    // Check if validation messages appear
    const validationMessages = [
      page.locator('text="required"'),
      page.locator('text="Required"'),
      page.locator('text="cannot be empty"'),
      page.locator('text="This field is required"'),
    ];
    
    let validationFound = false;
    for (const element of validationMessages) {
      try {
        await expect(element).toBeVisible({ timeout: 3000 });
        validationFound = true;
        break;
      } catch (e) {
        // Continue checking
      }
    }
    
    // Take screenshot of validation state
    await page.screenshot({ path: 'test-results/validation-test.png' });
    
    console.log(validationFound ? '✅ Form validation working' : '⚠️ No validation messages found');
  });
});