"""
Unit tests for MCP Authentication Module
"""

import os
import pytest
from unittest.mock import Mock, patch
from fastapi import Request
from app.auth.mcp_auth import (
    validate_supabase_key,
    is_localhost,
    extract_user_from_header,
    authenticate_mcp_request,
    MCPAuthenticationError,
    get_mcp_auth_error_response,
    validate_user_permissions
)


class TestValidateSupabaseKey:
    """Test Supabase key validation"""
    
    def test_valid_key(self):
        """Test with valid anon key"""
        test_key = "test_anon_key_123"
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": test_key}):
            assert validate_supabase_key(test_key) is True
    
    def test_invalid_key(self):
        """Test with invalid anon key"""
        expected_key = "test_anon_key_123"
        invalid_key = "wrong_key_456"
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": expected_key}):
            assert validate_supabase_key(invalid_key) is False
    
    def test_empty_key(self):
        """Test with empty key"""
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": "test_key"}):
            assert validate_supabase_key("") is False
            assert validate_supabase_key(None) is False
    
    def test_no_expected_key_configured(self):
        """Test when EXPECTED_SUPABASE_ANON_KEY is not configured"""
        with patch.dict(os.environ, {}, clear=True):
            assert validate_supabase_key("any_key") is False
    
    def test_key_with_whitespace(self):
        """Test key validation with whitespace"""
        test_key = "test_anon_key_123"
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": test_key}):
            assert validate_supabase_key(f"  {test_key}  ") is True


class TestIsLocalhost:
    """Test localhost detection"""
    
    def test_localhost_addresses(self):
        """Test various localhost addresses"""
        localhost_addresses = ["127.0.0.1", "localhost", "::1", "0.0.0.0"]
        
        for addr in localhost_addresses:
            request = Mock()
            request.client = Mock()
            request.client.host = addr
            assert is_localhost(request) is True
    
    def test_remote_addresses(self):
        """Test remote addresses"""
        remote_addresses = ["*************", "********", "*******", "example.com"]
        
        for addr in remote_addresses:
            request = Mock()
            request.client = Mock()
            request.client.host = addr
            assert is_localhost(request) is False
    
    def test_no_client(self):
        """Test request without client"""
        request = Mock()
        request.client = None
        assert is_localhost(request) is False
        
        # Test request without client attribute
        request = Mock(spec=[])
        assert is_localhost(request) is False


class TestExtractUserFromHeader:
    """Test user extraction from header"""
    
    def test_valid_bearer_token(self):
        """Test with valid Bearer token"""
        with patch.dict(os.environ, {"MCP_USER_ID": "testuser"}):
            result = extract_user_from_header("Bearer test_token_123")
            assert result == "testuser"
    
    def test_invalid_header_format(self):
        """Test with invalid header format"""
        result = extract_user_from_header("Invalid format")
        assert result is None
        
        result = extract_user_from_header("Basic username:password")
        assert result is None
    
    def test_no_mcp_user_id(self):
        """Test when MCP_USER_ID is not set"""
        with patch.dict(os.environ, {}, clear=True):
            result = extract_user_from_header("Bearer test_token_123")
            assert result is None


class TestValidateUserPermissions:
    """Test user permission validation"""
    
    def test_authorized_users(self):
        """Test authorized users"""
        assert validate_user_permissions("aungheinaye") is True
        assert validate_user_permissions("yohanna") is True
    
    def test_unauthorized_users(self):
        """Test unauthorized users"""
        assert validate_user_permissions("hacker") is False
        assert validate_user_permissions("admin") is False
        assert validate_user_permissions("") is False
        assert validate_user_permissions(None) is False


class TestAuthenticateMcpRequest:
    """Test main authentication function"""
    
    @pytest.fixture
    def mock_request(self):
        """Create a mock request object"""
        request = Mock()
        request.headers = {}
        request.query_params = {}
        request.client = Mock()
        request.client.host = "*************"  # Remote by default
        return request
    
    @pytest.mark.asyncio
    async def test_header_authentication_success(self, mock_request):
        """Test successful authentication via header"""
        test_key = "test_anon_key_123"
        mock_request.headers = {"Authorization": f"Bearer {test_key}"}
        
        with patch.dict(os.environ, {
            "EXPECTED_SUPABASE_ANON_KEY": test_key,
            "MCP_USER_ID": "aungheinaye"
        }):
            user_id, is_authenticated = await authenticate_mcp_request(mock_request, "fallback_user")
            assert user_id == "aungheinaye"
            assert is_authenticated is True
    
    @pytest.mark.asyncio
    async def test_environment_authentication_success(self, mock_request):
        """Test successful authentication via environment variables"""
        test_key = "test_anon_key_123"
        
        with patch.dict(os.environ, {
            "EXPECTED_SUPABASE_ANON_KEY": test_key,
            "SUPABASE_ANON_KEY": test_key,
            "MCP_USER_ID": "yohanna"
        }):
            user_id, is_authenticated = await authenticate_mcp_request(mock_request, "fallback_user")
            assert user_id == "yohanna"
            assert is_authenticated is True
    
    @pytest.mark.asyncio
    async def test_query_params_authentication_success(self, mock_request):
        """Test successful authentication via query parameters"""
        test_key = "test_anon_key_123"
        mock_request.query_params = {"api_key": test_key, "user_id": "aungheinaye"}
        
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": test_key}):
            user_id, is_authenticated = await authenticate_mcp_request(mock_request, "fallback_user")
            assert user_id == "aungheinaye"
            assert is_authenticated is True
    
    @pytest.mark.asyncio
    async def test_localhost_bypass(self, mock_request):
        """Test localhost bypass for development"""
        mock_request.client.host = "127.0.0.1"
        
        user_id, is_authenticated = await authenticate_mcp_request(mock_request, "aungheinaye")
        assert user_id == "aungheinaye"
        assert is_authenticated is False  # Not authenticated but allowed
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, mock_request):
        """Test authentication failure"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(MCPAuthenticationError) as exc_info:
                await authenticate_mcp_request(mock_request, "fallback_user")
            
            assert "Authentication required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_invalid_key_in_header(self, mock_request):
        """Test invalid key in header"""
        mock_request.headers = {"Authorization": "Bearer invalid_key"}
        
        with patch.dict(os.environ, {"EXPECTED_SUPABASE_ANON_KEY": "valid_key"}):
            with pytest.raises(MCPAuthenticationError):
                await authenticate_mcp_request(mock_request, "fallback_user")
    
    @pytest.mark.asyncio
    async def test_priority_order(self, mock_request):
        """Test that header auth has priority over environment auth"""
        header_key = "header_key"
        env_key = "env_key"
        
        mock_request.headers = {"Authorization": f"Bearer {header_key}"}
        
        with patch.dict(os.environ, {
            "EXPECTED_SUPABASE_ANON_KEY": header_key,  # Both keys are valid
            "SUPABASE_ANON_KEY": env_key,
            "MCP_USER_ID": "header_user"
        }):
            user_id, is_authenticated = await authenticate_mcp_request(mock_request, "fallback_user")
            assert user_id == "header_user"  # Should use header auth
            assert is_authenticated is True


class TestMCPAuthenticationError:
    """Test custom exception"""
    
    def test_default_error(self):
        """Test default error creation"""
        error = MCPAuthenticationError("Test message")
        assert error.message == "Test message"
        assert "Authorization header" in error.supported_methods
    
    def test_custom_supported_methods(self):
        """Test error with custom supported methods"""
        methods = ["Method 1", "Method 2"]
        error = MCPAuthenticationError("Test message", methods)
        assert error.supported_methods == methods


class TestGetMcpAuthErrorResponse:
    """Test error response generation"""
    
    def test_error_response_format(self):
        """Test error response format"""
        error = MCPAuthenticationError("Test auth error", ["Method 1", "Method 2"])
        response = get_mcp_auth_error_response(error)
        
        assert response["error"] == "MCP Authentication Failed"
        assert response["code"] == "MCP_AUTH_REQUIRED"
        assert response["message"] == "Test auth error"
        assert response["supported_methods"] == ["Method 1", "Method 2"]
        assert "documentation" in response


if __name__ == "__main__":
    pytest.main([__file__])