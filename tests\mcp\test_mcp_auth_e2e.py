#!/usr/bin/env python3
"""
End-to-End Testing for MCP Authentication
Tests all authentication methods and both users
"""

import os
import sys
import time
import subprocess
import requests
import json
from typing import Dict, Any, Optional


class MCPAuthTester:
    def __init__(self, base_url: str = "http://localhost:5678"):
        self.base_url = base_url
        self.valid_anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
        self.invalid_anon_key = "invalid_key_123"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "status": status
        }
        self.test_results.append(result)
        print(f"{status}: {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def test_server_health(self) -> bool:
        """Test if server is running"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log_test("Server Health Check", True, f"Status: {response.status_code}")
                return True
            else:
                self.log_test("Server Health Check", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Server Health Check", False, f"Error: {str(e)}")
            return False
    
    def test_header_authentication(self, user_id: str, valid_key: bool = True) -> bool:
        """Test authentication via Authorization header"""
        key = self.valid_anon_key if valid_key else self.invalid_anon_key
        
        # Set environment variable for user mapping
        old_env = os.environ.copy()
        os.environ["MCP_USER_ID"] = user_id
        
        headers = {"Authorization": f"Bearer {key}"}
        
        try:
            # Test the SSE endpoint (it will return 404 or connection error, but we check auth first)
            response = requests.get(
                f"{self.base_url}/mcp/claude/sse/{user_id}",
                headers=headers,
                timeout=5
            )
            
            if valid_key:
                # For valid key, we expect either success or a connection-related error (not auth error)
                success = response.status_code != 401
                self.log_test(
                    f"Header Auth - {user_id} (Valid Key)", 
                    success, 
                    f"Status: {response.status_code}, Response: {response.text[:100]}"
                )
            else:
                # For invalid key, we expect 401
                success = response.status_code == 401
                self.log_test(
                    f"Header Auth - {user_id} (Invalid Key)", 
                    success, 
                    f"Status: {response.status_code}, Expected 401"
                )
            
            return success
            
        except Exception as e:
            self.log_test(f"Header Auth - {user_id}", False, f"Error: {str(e)}")
            return False
        finally:
            # Restore environment
            os.environ.clear()
            os.environ.update(old_env)
    
    def test_query_params_authentication(self, user_id: str, valid_key: bool = True) -> bool:
        """Test authentication via query parameters"""
        key = self.valid_anon_key if valid_key else self.invalid_anon_key
        
        params = {
            "api_key": key,
            "user_id": user_id
        }
        
        try:
            response = requests.get(
                f"{self.base_url}/mcp/claude/sse/{user_id}",
                params=params,
                timeout=5
            )
            
            if valid_key:
                success = response.status_code != 401
                self.log_test(
                    f"Query Params Auth - {user_id} (Valid Key)", 
                    success, 
                    f"Status: {response.status_code}"
                )
            else:
                success = response.status_code == 401
                self.log_test(
                    f"Query Params Auth - {user_id} (Invalid Key)", 
                    success, 
                    f"Status: {response.status_code}, Expected 401"
                )
            
            return success
            
        except Exception as e:
            self.log_test(f"Query Params Auth - {user_id}", False, f"Error: {str(e)}")
            return False
    
    def test_environment_authentication(self, user_id: str, valid_key: bool = True) -> bool:
        """Test authentication via environment variables"""
        # This test simulates how the server would see environment variables
        # We can't actually test this externally, so we'll test the auth logic directly
        try:
            from api.app.auth.mcp_auth import authenticate_mcp_request, MCPAuthenticationError
            from unittest.mock import Mock
            import asyncio
            
            # Create mock request
            request = Mock()
            request.headers = {}
            request.query_params = {}
            request.client = Mock()
            request.client.host = "127.0.0.1"  # Localhost
            
            # Set environment variables
            old_env = os.environ.copy()
            if valid_key:
                os.environ["SUPABASE_ANON_KEY"] = self.valid_anon_key
                os.environ["EXPECTED_SUPABASE_ANON_KEY"] = self.valid_anon_key
            else:
                os.environ["SUPABASE_ANON_KEY"] = self.invalid_anon_key
                os.environ["EXPECTED_SUPABASE_ANON_KEY"] = self.valid_anon_key
            
            os.environ["MCP_USER_ID"] = user_id
            
            async def test_auth():
                try:
                    result_user, is_authenticated = await authenticate_mcp_request(request, user_id)
                    return result_user == user_id and (is_authenticated if valid_key else not is_authenticated)
                except MCPAuthenticationError:
                    return not valid_key  # Should fail for invalid key
            
            success = asyncio.run(test_auth())
            self.log_test(
                f"Environment Auth - {user_id} ({'Valid' if valid_key else 'Invalid'} Key)", 
                success, 
                f"Expected {'authenticated' if valid_key else 'failed'}"
            )
            
            return success
            
        except Exception as e:
            self.log_test(f"Environment Auth - {user_id}", False, f"Error: {str(e)}")
            return False
        finally:
            # Restore environment
            os.environ.clear()
            os.environ.update(old_env)
    
    def test_unauthorized_user(self) -> bool:
        """Test that unauthorized users are rejected"""
        headers = {"Authorization": f"Bearer {self.valid_anon_key}"}
        
        # Set environment for unauthorized user
        old_env = os.environ.copy()
        os.environ["MCP_USER_ID"] = "hacker"
        
        try:
            response = requests.get(
                f"{self.base_url}/mcp/claude/sse/hacker",
                headers=headers,
                timeout=5
            )
            
            # Should get 401 for unauthorized user
            success = response.status_code == 401
            self.log_test(
                "Unauthorized User Rejection", 
                success, 
                f"Status: {response.status_code}, Expected 401"
            )
            
            return success
            
        except Exception as e:
            self.log_test("Unauthorized User Rejection", False, f"Error: {str(e)}")
            return False
        finally:
            os.environ.clear()
            os.environ.update(old_env)
    
    def test_localhost_bypass(self) -> bool:
        """Test localhost bypass for development"""
        try:
            # Test without any authentication from localhost
            # The server should allow this for development
            response = requests.get(
                f"{self.base_url}/mcp/claude/sse/aungheinaye",
                timeout=5
            )
            
            # Should not get 401 (might get other errors due to SSE setup)
            success = response.status_code != 401
            self.log_test(
                "Localhost Bypass", 
                success, 
                f"Status: {response.status_code}, Should not be 401"
            )
            
            return success
            
        except Exception as e:
            self.log_test("Localhost Bypass", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all authentication tests"""
        print("🧪 Starting MCP Authentication End-to-End Tests")
        print("=" * 60)
        
        # Test server health first
        if not self.test_server_health():
            print("❌ Server is not running. Please start the API server first.")
            return {"success": False, "results": self.test_results}
        
        print("\n📋 Testing Authentication Methods")
        print("-" * 40)
        
        # Test all authentication methods for both users
        users = ["aungheinaye", "yohanna"]
        
        for user in users:
            print(f"\n👤 Testing user: {user}")
            
            # Test valid authentication
            self.test_header_authentication(user, valid_key=True)
            self.test_query_params_authentication(user, valid_key=True)
            self.test_environment_authentication(user, valid_key=True)
            
            # Test invalid authentication
            self.test_header_authentication(user, valid_key=False)
            self.test_query_params_authentication(user, valid_key=False)
            self.test_environment_authentication(user, valid_key=False)
        
        print(f"\n🔒 Testing Security Features")
        print("-" * 40)
        
        # Test security features
        self.test_unauthorized_user()
        self.test_localhost_bypass()
        
        # Calculate results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 Test Results Summary")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        overall_success = failed_tests == 0
        print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
        
        return {
            "success": overall_success,
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "results": self.test_results
        }


def main():
    """Main test execution"""
    # Change to API directory to import modules
    api_dir = "/home/<USER>/wsl_dev/memory-master-v2/api"
    if api_dir not in sys.path:
        sys.path.append(api_dir)
    
    tester = MCPAuthTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if results["success"] else 1)


if __name__ == "__main__":
    main()