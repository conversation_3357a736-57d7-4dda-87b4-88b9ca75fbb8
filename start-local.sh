#!/bin/bash

# Kill any existing processes on the ports
echo "Killing existing processes on ports 5678 and 3001..."
lsof -ti :5678 | xargs kill -9 2>/dev/null || true
lsof -ti :3001 | xargs kill -9 2>/dev/null || true

# Wait a moment for ports to be freed
sleep 2

echo "Starting Memory Master v2 locally..."

# Check if we're in the right directory
if [ ! -d "api" ] || [ ! -d "mock-ui" ]; then
    echo "Error: Please run this script from the memory-master-v2 root directory"
    exit 1
fi

# Start API server using Docker in background
echo "Starting API server on port 5678 using Docker..."
cd api
docker build -t memory-api . >/dev/null 2>&1
docker run -d --name memory-api-local -p 5678:8765 --env-file .env memory-api >/dev/null 2>&1
API_PID=$!

# Wait a bit for API to start
sleep 5

# Start mock-ui
echo "Starting mock-ui on port 3001..."
cd ../mock-ui && npm run dev &
UI_PID=$!

echo "Started processes:"
echo "API PID: $API_PID (http://localhost:5678)"
echo "UI PID: $UI_PID (http://localhost:3001)"

# Function to kill background processes on script exit
cleanup() {
    echo "Shutting down..."
    docker stop memory-api-local >/dev/null 2>&1 || true
    docker rm memory-api-local >/dev/null 2>&1 || true
    kill $UI_PID 2>/dev/null || true
    exit
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Wait for user input to stop
echo "Press Ctrl+C to stop all services"
wait