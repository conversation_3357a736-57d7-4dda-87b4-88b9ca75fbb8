import datetime
from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
from app.database import engine, Base, SessionLocal
# Temporarily disabled memories_router, stats_router, health_router due to USER_ID dependency
from app.routers import apps_router, config_router
from app.routers.auth import router as auth_router
from app.routers.evolution_config import router as evolution_config_router
from app.routers.health import router as health_router
from app.routers.memories import router as memories_router
from app.routers.memories_v2 import router as memories_v2_router
from app.routers.sync import router as sync_router
from fastapi_pagination import add_pagination
from fastapi.middleware.cors import CORSMiddleware
from app.models import User, App
from uuid import uuid4
from app.config import DEFAULT_APP_ID

origins = [
    "http://0.0.0.0:3210",
    "http://************:3210",
    "http://*************:3210",  # Remote host
    "http://127.0.0.1:3210",
    "http://localhost:3210",  # Keep for local dev
    "*"
]

# Create all tables
Base.metadata.create_all(bind=engine)

# Create default users and apps for known users
def create_default_users():
    db = SessionLocal()
    try:
        # Known users for the system
        known_users = [
            {"user_id": "aungheinaye", "name": "Aung Hein Aye"},
            {"user_id": "yohanna", "name": "Yohanna"}
        ]
        
        for user_info in known_users:
            # Check if user exists
            user = db.query(User).filter(User.user_id == user_info["user_id"]).first()
            if not user:
                # Create user
                user = User(
                    id=uuid4(),
                    user_id=user_info["user_id"],
                    name=user_info["name"],
                    created_at=datetime.datetime.now(datetime.UTC)
                )
                db.add(user)
                db.commit()
                print(f"Created user: {user_info['user_id']}")
                
                # Create default app for this user
                app = App(
                    id=uuid4(),
                    name=DEFAULT_APP_ID,
                    owner_id=user.id,
                    created_at=datetime.datetime.now(datetime.UTC),
                    updated_at=datetime.datetime.now(datetime.UTC),
                )
                db.add(app)
                db.commit()
                print(f"Created default app for user: {user_info['user_id']}")
    finally:
        db.close()

# Create default users on startup
create_default_users()

# Initialize configuration hot-reload system
from app.utils.config_init import initialize_config_system
initialize_config_system()

# Setup background memory client initialization
from app.utils.async_memory import get_async_memory_manager
import asyncio

async def startup_background_tasks():
    """Initialize background tasks on startup"""
    try:
        # Start eager memory client initialization during startup
        print("🚀 Starting eager memory client initialization during startup...")
        manager = get_async_memory_manager()
        await manager.start_initialization("aungheinaye")
        print("🚀 Background memory client initialization started successfully")
    except Exception as e:
        print(f"⚠️  Background initialization warning: {e}")

# Create FastAPI app  
app = FastAPI(title="OpenMemory API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define lifespan event handler for startup and shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await startup_background_tasks()
    yield
    # Shutdown (if needed)
    pass

# Add startup event (temporary - will migrate to lifespan later)
@app.on_event("startup")
async def startup_event():
    await startup_background_tasks()

# Setup MCP server - simplified approach matching original mem0ai implementation
from app.mcp_server import mcp_router

# Include the MCP router which now handles all SSE endpoints directly
app.include_router(mcp_router)

# Include routers
app.include_router(auth_router)
app.include_router(memories_router)  # Fixed USER_ID dependencies
app.include_router(memories_v2_router)  # New user-aware API
app.include_router(sync_router)  # Sync endpoints
app.include_router(apps_router)
# Temporarily disabled stats_router due to USER_ID dependency
# app.include_router(stats_router)
app.include_router(config_router)
app.include_router(health_router, prefix="/api/v1")
app.include_router(evolution_config_router, prefix="/api/v1")

# Add pagination support
add_pagination(app)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.datetime.now(datetime.UTC)}
