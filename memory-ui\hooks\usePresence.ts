import { useState, useEffect } from 'react';
import { useAppSelector } from '../store/hooks';

export interface PresenceUser {
  userId: string;
  displayName: string;
  activity: string;
  lastSeen: string;
  location?: string;
}

export const usePresence = () => {
  const { userId, displayName, isAuthenticated } = useAppSelector(state => state.profile);
  const [activeUsers, setActiveUsers] = useState<PresenceUser[]>([]);
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  useEffect(() => {
    if (!isAuthenticated || userId === 'guest') {
      setConnectionState('disconnected');
      setActiveUsers([]);
      return;
    }

    // Real presence implementation - connect to backend WebSocket or realtime system
    const setupPresence = async () => {
      try {
        setConnectionState('connecting');
        
        // TODO: Implement real presence system when backend WebSocket is available
        // For now, just set connected state without active users
        setConnectionState('connected');
        setActiveUsers([]);

      } catch (error) {
        console.error('Presence setup failed:', error);
        setConnectionState('disconnected');
      }
    };

    setupPresence();

    return () => {
      // TODO: Cleanup real presence connections
    };
  }, [userId, displayName, isAuthenticated]);

  return { activeUsers, connectionState };
};

function getActivityFromPath(path: string): string {
  switch (path) {
    case '/':
      return 'viewing_dashboard';
    case '/memories':
      return 'managing_memories';
    case '/sync':
      return 'monitoring_sync';
    case '/maintenance':
      return 'in_maintenance';
    case '/settings':
      return 'configuring_settings';
    default:
      if (path.startsWith('/memories/')) {
        return 'viewing_memory';
      }
      return 'browsing';
  }
}