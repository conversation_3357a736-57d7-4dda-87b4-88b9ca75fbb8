from fastapi import Request, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import get_db
from app.models import User

async def get_current_user_id(request: Request) -> str:
    """
    Extract user ID from request headers or query parameters.
    Priority:
    1. X-User-ID header
    2. user_id query parameter
    3. Default to 'guest' if neither is present
    """
    # Check X-User-ID header first
    user_id = request.headers.get("X-User-ID")
    
    if not user_id:
        # Check query parameters
        user_id = request.query_params.get("user_id")
    
    if not user_id:
        # Default to guest for backwards compatibility
        user_id = "guest"
    
    logging.info(f"Extracted user_id: {user_id}")
    return user_id

async def get_current_user(
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current user from the database based on user_id.
    Creates user if it doesn't exist.
    """
    if user_id == 'guest':
        raise HTTPException(status_code=401, detail="Authentication required")
    
    # Try to find existing user
    user = db.query(User).filter(User.user_id == user_id).first()
    
    if not user:
        # Create new user if not exists
        display_name = "Aung Hein Aye" if user_id == "aungheinaye" else "Yohanna" if user_id == "yohanna" else user_id.title()
        user = User(
            user_id=user_id,
            display_name=display_name,
            is_active=True
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        logging.info(f"Created new user: {user_id}")
    
    return user

async def get_current_user_optional(
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get the current user from the database, allowing guest access.
    Returns None for guest users.
    """
    if user_id == 'guest':
        return None
    
    try:
        return await get_current_user(user_id, db)
    except HTTPException:
        return None