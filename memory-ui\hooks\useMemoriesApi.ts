import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../lib/api';
import { useAppSelector, useAppDispatch, memoriesActions } from '../lib/context/AppContext';
import { toast } from 'sonner';

export const useMemoriesApi = () => {
  const dispatch = useAppDispatch();
  const { userId } = useAppSelector(state => state.profile);
  const { memories, totalCount, loading, currentPage, pageSize, filters } = useAppSelector(state => state.memories);
  
  // Local state for mutations
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch memories
  const fetchMemories = useCallback(async () => {
    if (userId === 'guest' || !userId) {
      dispatch(memoriesActions.setMemories([], 0));
      return;
    }

    dispatch(memoriesActions.setLoading(true));
    try {
      const result = await apiClient.getMemories(userId, {
        page: currentPage,
        limit: pageSize,
        search: filters.search,
        category: filters.category
      }) as any;
      dispatch(memoriesActions.setMemories(result.memories, result.totalCount));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to fetch memories';
      dispatch(memoriesActions.setError(message));
    }
  }, [userId, currentPage, pageSize, filters, dispatch]);

  // Load memories when dependencies change
  useEffect(() => {
    fetchMemories();
  }, [fetchMemories]);

  // Create memory
  const createMemory = useCallback(async (
    memory: { title: string; content: string; metadata?: Record<string, any> },
    options?: { onSuccess?: () => void; onError?: (error: Error) => void }
  ) => {
    if (userId === 'guest' || !userId) {
      const error = new Error('Authentication required');
      options?.onError?.(error);
      toast.error('Authentication required');
      return;
    }

    setIsCreating(true);
    try {
      const newMemory = await apiClient.createMemory(userId, memory);
      dispatch(memoriesActions.addMemory(newMemory as any));
      toast.success('Memory created successfully');
      options?.onSuccess?.();
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to create memory');
      dispatch(memoriesActions.setError(err.message));
      toast.error(err.message);
      options?.onError?.(err);
    } finally {
      setIsCreating(false);
    }
  }, [userId, dispatch]);

  // Update memory
  const updateMemory = useCallback(async (
    data: { id: string; title?: string; content?: string; metadata?: Record<string, any> },
    options?: { onSuccess?: () => void; onError?: (error: Error) => void }
  ) => {
    if (userId === 'guest' || !userId) {
      const error = new Error('Authentication required');
      options?.onError?.(error);
      toast.error('Authentication required');
      return;
    }

    setIsUpdating(true);
    try {
      const { id, ...memory } = data;
      const updatedMemory = await apiClient.updateMemory(userId, id, memory);
      dispatch(memoriesActions.updateMemory(updatedMemory as any));
      toast.success('Memory updated successfully');
      options?.onSuccess?.();
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to update memory');
      dispatch(memoriesActions.setError(err.message));
      toast.error(err.message);
      options?.onError?.(err);
    } finally {
      setIsUpdating(false);
    }
  }, [userId, dispatch]);

  // Delete memory
  const deleteMemory = useCallback(async (
    memoryId: string,
    options?: { onSuccess?: () => void; onError?: (error: Error) => void }
  ) => {
    if (userId === 'guest' || !userId) {
      const error = new Error('Authentication required');
      options?.onError?.(error);
      toast.error('Authentication required');
      return;
    }

    setIsDeleting(true);
    try {
      await apiClient.deleteMemory(userId, memoryId);
      dispatch(memoriesActions.removeMemory(memoryId));
      toast.success('Memory deleted successfully');
      options?.onSuccess?.();
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Failed to delete memory');
      dispatch(memoriesActions.setError(err.message));
      toast.error(err.message);
      options?.onError?.(err);
    } finally {
      setIsDeleting(false);
    }
  }, [userId, dispatch]);

  // Sync functions
  const fetchSyncCounts = async () => {
    if (userId === 'guest') {
      return { total_records: 0, total_vectors: 0, sync_status: 'unknown', timestamp: new Date().toISOString() };
    }
    
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/counts`, {
        headers: {
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch sync counts:', error);
      throw error;
    }
  };

  const startSync = async () => {
    if (userId === 'guest') {
      throw new Error('Authentication required');
    }
    
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to start sync:', error);
      throw error;
    }
  };

  const getSyncStatus = async (syncId: string) => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
      const response = await fetch(`${apiUrl}/api/v1/sync/status/${syncId}`, {
        headers: {
          'X-User-ID': userId,
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to get sync status:', error);
      throw error;
    }
  };

  return {
    // Data
    memories,
    totalCount,
    isLoading: loading,
    error: null, // TODO: Add error state if needed
    
    // Mutations
    createMemory,
    updateMemory,
    deleteMemory,
    
    // Mutation states
    isCreating,
    isUpdating,
    isDeleting,
    
    // Sync functions
    fetchSyncCounts,
    startSync,
    getSyncStatus,
    
    // Refetch
    refetch: fetchMemories
  };
};