// Automated authentication testing script
const { chromium } = require('playwright')

const APP_URL = 'http://localhost:3001'

const USERS = {
  aung: {
    email: 'aung<PERSON><PERSON><EMAIL>',
    password: 'password123',
    expectedName: 'Aung Hein Aye',
    expectedUserId: 'aungheinaye',
    expectedRole: 'Developer'
  },
  yohanna: {
    email: '<EMAIL>', 
    password: 'password123',
    expectedName: 'Yohanna',
    expectedUserId: 'yohanna',
    expectedRole: 'Operations'
  }
}

async function clearBrowserStorage(page) {
  console.log('🧹 Clearing browser storage...')
  await page.evaluate(() => {
    localStorage.clear()
    sessionStorage.clear()
    // Clear all cookies
    document.cookie.split(';').forEach(c => {
      const eqPos = c.indexOf('=')
      const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim()
      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/'
      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + location.hostname
    })
  })
}

async function waitForLoginPage(page) {
  console.log('⏳ Waiting for login page...')
  await page.waitForURL('**/login', { timeout: 10000 })
  await page.waitForSelector('input[name="email"]', { timeout: 5000 })
  console.log('✅ Login page loaded')
}

async function waitForDashboard(page) {
  console.log('⏳ Waiting for dashboard...')
  await page.waitForURL('**/', { timeout: 10000 })
  await page.waitForSelector('h1', { timeout: 5000 })
  console.log('✅ Dashboard loaded')
}

async function performLogin(page, user) {
  console.log(`🔐 Logging in as ${user.email}...`)
  
  // Fill login form
  await page.fill('input[name="email"]', user.email)
  await page.fill('input[name="password"]', user.password)
  
  // Submit form
  await page.click('button[type="submit"]')
  
  // Wait for redirect to dashboard
  await waitForDashboard(page)
  
  console.log(`✅ Login successful for ${user.email}`)
}

async function verifyAuthentication(page, user) {
  console.log(`🔍 Verifying authentication for ${user.expectedName}...`)
  
  // Get console logs from browser
  const consoleLogs = []
  page.on('console', msg => consoleLogs.push(`${msg.type()}: ${msg.text()}`))
  
  // Check URL is dashboard
  const url = page.url()
  if (!url.endsWith('/')) {
    throw new Error(`Expected dashboard URL (/), got: ${url}`)
  }
  
  // Wait a bit for React state to update
  await page.waitForTimeout(2000)
  
  // Check console logs for debugging info
  console.log('📝 Browser console logs:')
  consoleLogs.forEach(log => console.log(`  ${log}`))
  
  // Check welcome message
  const welcomeText = await page.textContent('h1')
  console.log(`📄 Welcome text found: "${welcomeText}"`)
  if (!welcomeText.includes(user.expectedName)) {
    throw new Error(`Expected welcome message with "${user.expectedName}", got: ${welcomeText}`)
  }
  
  // Check user ID display
  const userIdText = await page.textContent('text=User ID:')
  if (!userIdText.includes(user.expectedUserId)) {
    throw new Error(`Expected user ID "${user.expectedUserId}", got: ${userIdText}`)
  }
  
  // Check authentication status
  const authStatus = await page.textContent('text=Authenticated')
  if (!authStatus) {
    throw new Error('Authentication status not found or not "Authenticated"')
  }
  
  // Check user dropdown shows correct info
  const userButton = await page.locator('button').filter({ hasText: user.expectedName }).first()
  await userButton.click()
  
  // Verify dropdown content
  const dropdownText = await page.textContent('[role="menu"]')
  if (!dropdownText.includes(user.expectedName)) {
    throw new Error(`User dropdown doesn't contain "${user.expectedName}"`)
  }
  if (!dropdownText.includes(user.expectedUserId)) {
    throw new Error(`User dropdown doesn't contain user ID "${user.expectedUserId}"`)
  }
  
  // Close dropdown
  await page.keyboard.press('Escape')
  
  console.log(`✅ Authentication verified for ${user.expectedName}`)
  return true
}

async function performLogout(page) {
  console.log('🚪 Performing logout...')
  
  // Click user dropdown
  const userButton = await page.locator('button').filter({ hasText: /Aung Hein Aye|Yohanna/ }).first()
  await userButton.click()
  
  // Click logout
  await page.click('text=Log out')
  
  // Wait for redirect to login
  await waitForLoginPage(page)
  
  console.log('✅ Logout successful')
}

async function testUserAuthentication(browser, user, userLabel) {
  console.log(`\n🧪 Testing ${userLabel} Authentication`)
  console.log('=' .repeat(50))
  
  const context = await browser.newContext()
  const page = await context.newPage()
  
  // Capture console logs from the start
  page.on('console', msg => {
    if (msg.text().includes('🔧')) {
      console.log(`🔧 Browser: ${msg.text()}`)
    }
  })
  
  try {
    // Clear storage and navigate
    await page.goto(APP_URL)
    await clearBrowserStorage(page)
    
    // Navigate to app (should redirect to login)
    await page.goto(APP_URL)
    await waitForLoginPage(page)
    
    // Perform login
    await performLogin(page, user)
    
    // Verify authentication
    await verifyAuthentication(page, user)
    
    // Perform logout
    await performLogout(page)
    
    console.log(`✅ ${userLabel} authentication test PASSED`)
    return true
    
  } catch (error) {
    console.error(`❌ ${userLabel} authentication test FAILED:`, error.message)
    return false
  } finally {
    await context.close()
  }
}

async function runAuthenticationTests() {
  console.log('🚀 Starting Automated Authentication Tests')
  console.log('=' .repeat(60))
  
  const browser = await chromium.launch({ 
    headless: false, // Set to true for headless mode
    slowMo: 1000     // Slow down for visual verification
  })
  
  const results = {
    aung: false,
    yohanna: false
  }
  
  try {
    // Test Aung's authentication
    results.aung = await testUserAuthentication(browser, USERS.aung, 'Aung')
    
    // Test Yohanna's authentication  
    results.yohanna = await testUserAuthentication(browser, USERS.yohanna, 'Yohanna')
    
    // Summary
    console.log('\n📊 Test Results Summary')
    console.log('=' .repeat(60))
    console.log(`Aung Authentication: ${results.aung ? '✅ PASS' : '❌ FAIL'}`)
    console.log(`Yohanna Authentication: ${results.yohanna ? '✅ PASS' : '❌ FAIL'}`)
    
    const allPassed = results.aung && results.yohanna
    console.log(`\nOverall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)
    
    return allPassed
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message)
    return false
  } finally {
    await browser.close()
  }
}

// Run the tests
if (require.main === module) {
  runAuthenticationTests()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error)
      process.exit(1)
    })
}

module.exports = { runAuthenticationTests }