"""
Sync Router

Provides endpoints for database synchronization between PostgreSQL and Qdrant.
"""

import datetime
import logging
import uuid
from typing import Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import text
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import exceptions as qdrant_exceptions

from app.database import get_db
from app.auth.middleware import get_current_user, Authenticated<PERSON><PERSON>, Default<PERSON>ser
from app.models import User, Memory, MemoryState
from app.utils.memory import get_user_collection_name
import os

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/sync", tags=["sync"])

# Store active sync operations
active_syncs: Dict[str, Dict[str, Any]] = {}

@router.get("/counts")
async def get_sync_counts(
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current record counts from both PostgreSQL and Qdrant for comparison.
    
    Returns:
        Dict with total_records (PostgreSQL) and total_vectors (Qdrant)
    """
    try:
        # Get PostgreSQL memory count for the current user
        user_id = auth_user.user_id
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User '{user_id}' not found")
        
        total_records = db.query(Memory).filter(
            Memory.user_id == user.id,
            Memory.state != MemoryState.deleted.value
        ).count()
        
        # Get Qdrant vector count
        total_vectors = 0
        qdrant_status = "unknown"
        qdrant_error = None
        
        try:
            qdrant_host = os.environ.get('QDRANT_HOST', 'localhost')
            qdrant_port = int(os.environ.get('QDRANT_PORT', '6333'))
            client = QdrantClient(host=qdrant_host, port=qdrant_port)
            
            # Get user collection name
            user_collection_name = get_user_collection_name(user_id)
            
            # Check if collection exists and get count
            collections = client.get_collections()
            collection_exists = any(col.name == user_collection_name for col in collections.collections)
            
            if collection_exists:
                collection_info = client.get_collection(user_collection_name)
                total_vectors = collection_info.points_count or 0
                qdrant_status = "connected"
            else:
                qdrant_status = "no_collection"
                
        except Exception as e:
            logger.warning(f"Failed to get Qdrant vector count: {e}")
            qdrant_error = str(e)
            qdrant_status = "error"
        
        # Determine sync status
        sync_status = "unknown"
        if qdrant_status == "connected":
            if total_records == total_vectors:
                sync_status = "synced"
            else:
                sync_status = "out_of_sync"
        elif qdrant_status == "no_collection":
            sync_status = "needs_initial_sync"
        else:
            sync_status = "error"
        
        return {
            "total_records": total_records,
            "total_vectors": total_vectors,
            "sync_status": sync_status,
            "qdrant_status": qdrant_status,
            "qdrant_error": qdrant_error,
            "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
            "user_collection": get_user_collection_name(user_id),
            "difference": abs(total_records - total_vectors) if qdrant_status == "connected" else None
        }
        
    except Exception as e:
        logger.exception(f"Error getting sync counts: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get sync counts: {str(e)}")


@router.post("/start")
async def start_sync(
    background_tasks: BackgroundTasks,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start a synchronization operation between PostgreSQL and Qdrant.
    
    Returns:
        Dict with sync_id for tracking progress
    """
    try:
        # Generate unique sync ID
        sync_id = str(uuid.uuid4())
        
        # Initialize sync operation tracking
        active_syncs[sync_id] = {
            "sync_id": sync_id,
            "status": "starting",
            "progress": 0,
            "started_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
            "message": "Initializing synchronization...",
            "total_records": 0,
            "processed_records": 0,
            "errors": []
        }
        
        # Start background sync task
        user_id = auth_user.user_id
        background_tasks.add_task(perform_sync_operation, sync_id, db, user_id)
        
        return {
            "sync_id": sync_id,
            "status": "started",
            "message": "Synchronization operation started",
            "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.exception(f"Error starting sync: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start sync: {str(e)}")


@router.get("/status/{sync_id}")
async def get_sync_status(sync_id: str):
    """
    Get the status of a specific sync operation.
    
    Args:
        sync_id: The unique identifier for the sync operation
        
    Returns:
        Dict with current sync status and progress
    """
    if sync_id not in active_syncs:
        raise HTTPException(status_code=404, detail="Sync operation not found")
    
    return active_syncs[sync_id]


@router.get("/history")
async def get_sync_history(limit: int = 10):
    """
    Get recent sync operation history.
    
    Args:
        limit: Number of recent sync operations to return
        
    Returns:
        List of recent sync operations
    """
    try:
        # In a real implementation, this would query a sync_history table
        # For now, return active and completed syncs
        all_syncs = list(active_syncs.values())
        
        # Sort by started_at timestamp, most recent first
        all_syncs.sort(key=lambda x: x.get('started_at', ''), reverse=True)
        
        # Return limited results
        return {
            "sync_history": all_syncs[:limit],
            "total_count": len(all_syncs),
            "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.exception(f"Error getting sync history: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get sync history: {str(e)}")


async def perform_sync_operation(sync_id: str, db: Session, user_id: str):
    """
    Background task to perform bi-directional synchronization between PostgreSQL and Qdrant.
    - If PostgreSQL has extra memories, embed them into Qdrant
    - If Qdrant has extra memories, add them to PostgreSQL
    - Maintains user isolation throughout the process
    
    Args:
        sync_id: Unique identifier for this sync operation
        db: Database session  
        user_id: User ID for the sync operation
    """
    try:
        sync_info = active_syncs[sync_id]
        
        # Update status to running
        sync_info.update({
            "status": "running",
            "progress": 5,
            "message": "Connecting to databases..."
        })
        
        # Get user and memory count
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise Exception(f"User '{user_id}' not found")
        
        # Get all PostgreSQL memories for this user
        pg_memories = db.query(Memory).filter(
            Memory.user_id == user.id,
            Memory.state != MemoryState.deleted.value
        ).all()
        
        pg_memory_ids = {str(memory.mem0_id): memory for memory in pg_memories}
        total_pg_records = len(pg_memories)
        
        sync_info.update({
            "total_records": total_pg_records,
            "progress": 10,
            "message": f"Found {total_pg_records} memories in PostgreSQL..."
        })
        
        # Connect to Qdrant
        qdrant_host = os.environ.get('QDRANT_HOST', 'localhost')
        qdrant_port = int(os.environ.get('QDRANT_PORT', '6333'))
        qdrant_client = QdrantClient(host=qdrant_host, port=qdrant_port)
        
        user_collection_name = get_user_collection_name(user_id)
        
        sync_info.update({
            "progress": 15,
            "message": f"Connected to Qdrant, checking collection {user_collection_name}..."
        })
        
        # Check collection status and get all Qdrant memories
        collections = qdrant_client.get_collections()
        collection_exists = any(col.name == user_collection_name for col in collections.collections)
        
        qdrant_memories = {}  # memory_id -> point_data
        total_qdrant_vectors = 0
        
        if collection_exists:
            collection_info = qdrant_client.get_collection(user_collection_name)
            total_qdrant_vectors = collection_info.points_count or 0
            
            sync_info.update({
                "progress": 20,
                "message": f"Collection exists with {total_qdrant_vectors} vectors. Retrieving all memory data..."
            })
            
            # Get all existing memories from Qdrant with full payload
            batch_size = 100
            offset = 0
            while True:
                try:
                    results = qdrant_client.scroll(
                        collection_name=user_collection_name,
                        limit=batch_size,
                        offset=offset,
                        with_payload=True,
                        with_vectors=False
                    )
                    
                    if not results or len(results[0]) == 0:
                        break
                        
                    # Store memory data from Qdrant
                    for point in results[0]:
                        if point.payload and "id" in point.payload:
                            memory_id = point.payload["id"]
                            qdrant_memories[memory_id] = {
                                "point_id": point.id,
                                "payload": point.payload
                            }
                    
                    offset += batch_size
                    
                except Exception as e:
                    logger.warning(f"Error scrolling through Qdrant collection: {e}")
                    break
            
            sync_info.update({
                "progress": 25,
                "message": f"Retrieved {len(qdrant_memories)} memory records from Qdrant"
            })
        else:
            sync_info.update({
                "progress": 25,
                "message": "Qdrant collection does not exist. All PostgreSQL memories will be synced."
            })
        
        # Phase 1: Identify memories missing in Qdrant (PostgreSQL → Qdrant)
        pg_to_qdrant = []
        for memory_id, memory in pg_memory_ids.items():
            if memory_id not in qdrant_memories:
                pg_to_qdrant.append(memory)
        
        # Phase 2: Identify memories missing in PostgreSQL (Qdrant → PostgreSQL)  
        qdrant_to_pg = []
        for memory_id, qdrant_data in qdrant_memories.items():
            if memory_id not in pg_memory_ids:
                # Extract text from payload for adding to PostgreSQL
                payload = qdrant_data["payload"]
                if "text" in payload or "content" in payload:
                    qdrant_to_pg.append({
                        "memory_id": memory_id,
                        "text": payload.get("text", payload.get("content", "")),
                        "metadata": payload,
                        "point_id": qdrant_data["point_id"]
                    })
        
        total_operations = len(pg_to_qdrant) + len(qdrant_to_pg)
        
        sync_info.update({
            "progress": 30,
            "message": f"Analysis complete: {len(pg_to_qdrant)} to sync to Qdrant, {len(qdrant_to_pg)} to sync to PostgreSQL",
            "pg_to_qdrant_count": len(pg_to_qdrant),
            "qdrant_to_pg_count": len(qdrant_to_pg),
            "total_operations": total_operations
        })
        
        if total_operations == 0:
            sync_info.update({
                "status": "completed",
                "progress": 100,
                "processed_records": 0,
                "message": "Databases are already in sync - no operations needed",
                "completed_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
            })
            return
        
        # Get memory client for operations
        from app.utils.memory import get_memory_client
        memory_client = get_memory_client()
        if not memory_client:
            raise Exception("Failed to initialize memory client")
        
        processed = 0
        errors = []
        
        # Phase 1: Sync PostgreSQL memories to Qdrant
        if pg_to_qdrant:
            sync_info.update({
                "progress": 35,
                "message": f"Starting Phase 1: Syncing {len(pg_to_qdrant)} memories from PostgreSQL to Qdrant..."
            })
            
            batch_size = 10
            for i in range(0, len(pg_to_qdrant), batch_size):
                batch = pg_to_qdrant[i:i + batch_size]
                
                for memory in batch:
                    try:
                        memory_text = memory.text
                        metadata = {
                            "source_app": "openmemory",
                            "mcp_client": memory.app.name if memory.app else "unknown",
                            "synced_at": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                            "original_created_at": memory.created_at.isoformat() if memory.created_at else None,
                            "original_mem0_id": str(memory.mem0_id),
                            "sync_operation": sync_id,
                            "sync_direction": "pg_to_qdrant"
                        }
                        
                        # Add to Qdrant via mem0 client
                        response = memory_client.add(
                            memory_text,
                            user_id=user_id,
                            metadata=metadata
                        )
                        
                        processed += 1
                        
                    except Exception as e:
                        error_msg = f"Failed to sync PG memory {memory.mem0_id} to Qdrant: {str(e)}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                
                # Update progress for Phase 1
                phase1_progress = 35 + int((processed / total_operations) * 30)
                sync_info.update({
                    "progress": phase1_progress,
                    "processed_records": processed,
                    "message": f"Phase 1: Synced {processed - len([e for e in errors if 'PG memory' in e])}/{len(pg_to_qdrant)} memories to Qdrant...",
                    "errors": errors[-3:] if errors else []
                })
                
                import asyncio
                await asyncio.sleep(0.1)
        
        # Phase 2: Sync Qdrant memories to PostgreSQL
        if qdrant_to_pg:
            sync_info.update({
                "progress": 65,
                "message": f"Starting Phase 2: Syncing {len(qdrant_to_pg)} memories from Qdrant to PostgreSQL..."
            })
            
            for i, qdrant_memory in enumerate(qdrant_to_pg):
                try:
                    # Create new memory record in PostgreSQL
                    memory_text = qdrant_memory["text"]
                    metadata = qdrant_memory["metadata"]
                    
                    # Extract app information if available
                    app_name = metadata.get("mcp_client", "unknown")
                    from app.models import App
                    app = db.query(App).filter(App.name == app_name).first()
                    if not app:
                        # Create app if it doesn't exist
                        app = App(name=app_name, description=f"Auto-created during sync from Qdrant")
                        db.add(app)
                        db.flush()
                    
                    # Create memory record
                    new_memory = Memory(
                        mem0_id=qdrant_memory["memory_id"],
                        text=memory_text,
                        user_id=user.id,
                        app_id=app.id,
                        state=MemoryState.active.value,
                        created_at=datetime.datetime.now(datetime.timezone.utc),
                        updated_at=datetime.datetime.now(datetime.timezone.utc)
                    )
                    
                    db.add(new_memory)
                    processed += 1
                    
                except Exception as e:
                    error_msg = f"Failed to sync Qdrant memory {qdrant_memory['memory_id']} to PostgreSQL: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                
                # Update progress for Phase 2
                phase2_progress = 65 + int(((processed - len(pg_to_qdrant)) / len(qdrant_to_pg)) * 25)
                sync_info.update({
                    "progress": phase2_progress,
                    "processed_records": processed,
                    "message": f"Phase 2: Synced {i + 1}/{len(qdrant_to_pg)} memories to PostgreSQL...",
                    "errors": errors[-3:] if errors else []
                })
                
                # Commit in batches
                if (i + 1) % 10 == 0:
                    db.commit()
                
                import asyncio
                await asyncio.sleep(0.1)
            
            # Final commit for remaining records
            db.commit()
        
        # Complete the sync
        success_count = processed - len(errors)
        
        if errors:
            sync_info.update({
                "status": "completed_with_errors",
                "progress": 100,
                "processed_records": processed,
                "success_count": success_count,
                "error_count": len(errors),
                "message": f"Bi-directional sync completed with {len(errors)} errors. Successfully processed {success_count}/{total_operations} operations.",
                "errors": errors,
                "completed_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
            })
        else:
            sync_info.update({
                "status": "completed",
                "progress": 100,
                "processed_records": processed,
                "success_count": success_count,
                "message": f"Bi-directional sync completed successfully! Processed all {processed} operations.",
                "completed_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
            })
        
        logger.info(f"Bi-directional sync operation {sync_id} completed. Processed {processed} operations ({len(pg_to_qdrant)} PG→Qdrant, {len(qdrant_to_pg)} Qdrant→PG) with {len(errors)} errors")
        
    except Exception as e:
        logger.exception(f"Bi-directional sync operation {sync_id} failed: {e}")
        sync_info.update({
            "status": "failed",
            "message": f"Bi-directional synchronization failed: {str(e)}",
            "error": str(e),
            "failed_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
        })


