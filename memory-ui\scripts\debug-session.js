// Debug current session
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'http://192.168.1.218:8000',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY'
)

async function debugSession() {
  console.log('Debugging session...')
  
  try {
    // Check current session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.error('Session error:', sessionError)
    } else {
      console.log('Current session:', sessionData.session ? 'EXISTS' : 'NULL')
      if (sessionData.session) {
        console.log('User ID:', sessionData.session.user.id)
        console.log('Email:', sessionData.session.user.email)
        console.log('Session expires:', sessionData.session.expires_at)
      }
    }
    
    // Check current user
    const { data: userData, error: userError } = await supabase.auth.getUser()
    if (userError) {
      console.error('User error:', userError)
    } else {
      console.log('Current user:', userData.user ? 'EXISTS' : 'NULL')
      if (userData.user) {
        console.log('User ID:', userData.user.id)
        console.log('Email:', userData.user.email)
      }
    }
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

debugSession()