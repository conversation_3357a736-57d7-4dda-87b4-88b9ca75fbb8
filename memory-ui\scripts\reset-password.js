// Quick script to reset user password
const { createClient } = require('@supabase/supabase-js')

// You'll need the service role key for this
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://*************:8000',
  SUPABASE_SERVICE_KEY
)

async function resetUserPassword(email, newPassword) {
  console.log(`Resetting password for ${email}...`)
  
  try {
    const { data, error } = await supabase.auth.admin.updateUserById(
      '310407fb-56fa-47a2-a534-5d836cdb23df', // Aung's user ID
      {
        password: newPassword
      }
    )
    
    if (error) {
      console.error('Error:', error)
      return
    }
    
    console.log('Password updated successfully!')
    console.log('You can now login with:')
    console.log(`Email: ${email}`)
    console.log(`Password: ${newPassword}`)
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

// Reset Aung's password to a known value
resetUserPassword('<EMAIL>', 'password123')