import { test, expect } from '@playwright/test';

test.describe('Memory Master v2 - Quick Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000); // Give time for React to render
  });

  test('should load without JavaScript errors', async ({ page }) => {
    // Check that the page loads
    await expect(page).toHaveTitle(/Memory Master/);
    
    // Basic content check
    const content = page.locator('body');
    await expect(content).toBeVisible();
    
    console.log('✓ Page loads successfully');
  });

  test('should verify Memory Processing Pipeline animation is removed', async ({ page }) => {
    // Check various selectors for animation/pipeline components
    const pipelineSelectors = [
      '[data-testid="memory-processing-pipeline"]',
      '.workflow-animation',
      '[data-testid="memory-flow-diagram"]',
      '.memory-flow-diagram',
      '.processing-pipeline',
      '.pipeline-animation'
    ];

    for (const selector of pipelineSelectors) {
      const element = page.locator(selector);
      await expect(element).toHaveCount(0);
    }
    
    console.log('✓ Memory Processing Pipeline animation successfully removed');
  });

  test('should check sidebar navigation structure', async ({ page }) => {
    // Look for navigation items by text content
    const expectedNavItems = ['Dashboard', 'Sync Monitor', 'Maintenance', 'Settings'];
    
    for (const item of expectedNavItems) {
      const navItem = page.locator(`a:has-text("${item}"), button:has-text("${item}")`);
      await expect(navItem).toBeVisible();
      console.log(`✓ Found navigation item: ${item}`);
    }
    
    // Check that "Memories" is NOT present
    const memoriesNav = page.locator('a:has-text("Memories"), button:has-text("Memories")');
    await expect(memoriesNav).toHaveCount(0);
    console.log('✓ "Memories" navigation item successfully removed');
  });

  test('should take updated screenshots', async ({ page }) => {
    // Take screenshot of current state
    await page.screenshot({ 
      path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/final-verification-dashboard.png',
      fullPage: true 
    });
    
    console.log('✓ Screenshot captured');
  });

  test('should check basic functionality', async ({ page }) => {
    // Look for main content areas
    const mainContent = page.locator('main, .main-content, [role="main"]');
    const hasMainContent = await mainContent.count() > 0;
    
    if (hasMainContent) {
      await expect(mainContent.first()).toBeVisible();
    }
    
    // Look for any buttons or interactive elements
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    console.log(`Found ${buttonCount} buttons on the page`);
    
    // Look for links
    const links = page.locator('a[href]');
    const linkCount = await links.count();
    console.log(`Found ${linkCount} links on the page`);
    
    console.log('✓ Basic UI elements present');
  });
});