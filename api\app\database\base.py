"""
Database base configuration
Separated to avoid circular imports
"""
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from dotenv import load_dotenv

# load .env file
load_dotenv()

# Use Supabase PostgreSQL connection
DATABASE_URL = os.getenv("SUPABASE_DATABASE_URL")
if not DATABASE_URL:
    raise RuntimeError("SUPABASE_DATABASE_URL is not set in environment")

# SQLAlchemy engine & session for PostgreSQL (performance optimized)
engine = create_engine(
    DATABASE_URL,
    pool_size=50,           # Increased from 20 to 50 for higher concurrency
    max_overflow=50,        # Increased from 30 to 50
    pool_timeout=30,        # Reduced from 60 to 30 seconds
    pool_recycle=1800,      # Reduced from 3600 to 1800 seconds (30 min)
    pool_pre_ping=True,     # Added: Health check connections before use
    pool_reset_on_return='commit'  # Added: Reset connections properly
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Dependency for FastAPI
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()