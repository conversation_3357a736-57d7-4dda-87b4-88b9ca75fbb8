*.db
.env*
!.env.example
!.env.dev
!ui/lib
.venv/
venv/
api/venv/
__pycache__
.DS_Store
node_modules/
*.log
api/.openmemory*
**/.next
.openmemory/
# Environment variables
.env

# Added by Task Master AI
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories


# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
*:Zone.Identifier

# Test and screenshot directories
Screenshots/
test-results/
test-screenshots/
test-screenshots-complete/
playwright-report/
test-results/
*.png
*.webm

# Archive directories
archive/

# Temporary files
*.tmp
*.temp
*.bak
*.orig

# Task files
# tasks.json
# tasks/

# Added by Task Master AI
tasks.json
tasks/ 