// <PERSON>ript to migrate existing memory_master.users to Supabase auth
import { createClient } from '@supabase/supabase-js'

// Use local Supabase instance
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Get from local Supabase dashboard
)

// Current users to migrate
const existingUsers = [
  {
    user_id: 'aungheinaye',
    email: '<EMAIL>',
    name: 'Aung Hein Aye',
    password: 'password123' // Change this to actual password
  },
  {
    user_id: 'yohanna', 
    email: '<EMAIL>',
    name: 'Yohanna',
    password: 'password123' // Change this to actual password
  }
]

async function migrateUsers() {
  console.log('Starting user migration...')
  
  for (const user of existingUsers) {
    console.log(`Migrating user: ${user.user_id}`)
    
    // Create Supabase auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: user.email,
      password: user.password,
      email_confirm: true,
      user_metadata: {
        display_name: user.name
      }
    })

    if (authError) {
      console.error(`Error creating auth user ${user.email}:`, authError)
      continue
    }

    console.log(`Created auth user: ${authUser.user.id} for ${user.email}`)

    // Update existing memory_master.users record
    const { error: updateError } = await supabase
      .from('memory_master.users')
      .update({
        supabase_user_id: authUser.user.id,
        email: user.email,
        email_verified: true,
        last_sign_in_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.user_id)

    if (updateError) {
      console.error(`Error updating user ${user.user_id}:`, updateError)
    } else {
      console.log(`Updated memory_master.users for ${user.user_id}`)
    }

    console.log(`Successfully migrated: ${user.user_id} -> ${authUser.user.id}`)
  }
  
  console.log('User migration completed!')
}

// Only run if called directly
if (require.main === module) {
  migrateUsers().catch(console.error)
}

export { migrateUsers }