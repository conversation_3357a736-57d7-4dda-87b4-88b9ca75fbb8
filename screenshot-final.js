const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  // Navigate to memories page
  await page.goto('http://localhost:3001/');
  await page.click('text=Memories');
  await page.waitForTimeout(3000);
  
  // Take screenshot
  await page.screenshot({ path: 'final-verification-screenshot.png' });
  
  // Check if the test memory is visible
  const testMemoryExists = await page.$('text=Test Memory Creation Fix');
  console.log('Test memory visible in UI:', testMemoryExists !== null);
  
  await browser.close();
})();