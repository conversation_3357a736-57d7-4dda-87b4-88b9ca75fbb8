import { test, expect, Page } from '@playwright/test';

test.describe('Android Green Color Scheme Testing', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Set viewport for consistent screenshots
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Navigate to the application
    await page.goto('http://localhost:3001');
    
    // Wait for the page to fully load
    await page.waitForLoadState('networkidle');
    
    // Wait for any animations to complete
    await page.waitForTimeout(2000);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('Dashboard: Android Green gradient in Memory Master logo', async () => {
    // Take screenshot of the full dashboard
    await page.screenshot({ 
      path: 'test-results/android-green/01-dashboard-full.png',
      fullPage: true 
    });

    // Focus on the Memory Master logo/title area
    const logoElement = page.locator('h1, h2, .gradient-text').first();
    if (await logoElement.count() > 0) {
      await logoElement.screenshot({
        path: 'test-results/android-green/01a-memory-master-logo.png'
      });
    }

    // Check if gradient text class is applied
    const gradientText = page.locator('.gradient-text');
    if (await gradientText.count() > 0) {
      await expect(gradientText).toBeVisible();
      await gradientText.screenshot({
        path: 'test-results/android-green/01b-gradient-text-detail.png'
      });
    }
  });

  test('Dashboard: Android Green primary buttons', async () => {
    // Look for Create Memory button or other primary buttons
    const createButton = page.locator('button:has-text("Create Memory"), button[class*="primary"], button[class*="bg-primary"]').first();
    
    if (await createButton.count() > 0) {
      // Normal state
      await createButton.screenshot({
        path: 'test-results/android-green/02a-create-memory-button.png'
      });

      // Hover state
      await createButton.hover();
      await page.waitForTimeout(500);
      await createButton.screenshot({
        path: 'test-results/android-green/02b-create-memory-button-hover.png'
      });

      // Click to see if dialog opens with Android Green elements
      await createButton.click();
      await page.waitForTimeout(1000);
      
      // Screenshot dialog if it appears
      const dialog = page.locator('dialog, [role="dialog"], .dialog').first();
      if (await dialog.isVisible()) {
        await dialog.screenshot({
          path: 'test-results/android-green/02c-create-memory-dialog.png'
        });
        
        // Close dialog
        const closeButton = page.locator('button:has-text("Cancel"), button:has-text("Close"), [aria-label="Close"]').first();
        if (await closeButton.count() > 0) {
          await closeButton.click();
        } else {
          await page.keyboard.press('Escape');
        }
      }
    }

    // Find all primary buttons on the page
    const primaryButtons = page.locator('button[class*="bg-primary"], button[class*="primary"]');
    const buttonCount = await primaryButtons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = primaryButtons.nth(i);
      if (await button.isVisible()) {
        await button.screenshot({
          path: `test-results/android-green/02d-button-${i}-normal.png`
        });
        
        await button.hover();
        await page.waitForTimeout(300);
        await button.screenshot({
          path: `test-results/android-green/02e-button-${i}-hover.png`
        });
      }
    }
  });

  test('Dashboard: Android Green connection status', async () => {
    // Look for connection status indicators
    const statusIndicators = page.locator('.status, [class*="status"], [class*="connected"], [class*="online"]');
    const indicatorCount = await statusIndicators.count();
    
    for (let i = 0; i < indicatorCount; i++) {
      const indicator = statusIndicators.nth(i);
      if (await indicator.isVisible()) {
        await indicator.screenshot({
          path: `test-results/android-green/03a-status-indicator-${i}.png`
        });
      }
    }

    // Look for specific connection status in stats area
    const statsArea = page.locator('[class*="stats"], [class*="metric"], .card').first();
    if (await statsArea.count() > 0) {
      await statsArea.screenshot({
        path: 'test-results/android-green/03b-stats-area.png'
      });
    }
  });

  test('Navigation: Android Green consistency across all pages', async () => {
    const pages = [
      { name: 'Dashboard', path: '/' },
      { name: 'Sync Monitor', path: '/sync' },
      { name: 'Maintenance', path: '/maintenance' },
      { name: 'Settings', path: '/settings' }
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3001${pageInfo.path}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1500);

      // Full page screenshot
      await page.screenshot({
        path: `test-results/android-green/04-${pageInfo.name.toLowerCase().replace(' ', '-')}-full.png`,
        fullPage: true
      });

      // Look for Android Green elements on each page
      const primaryElements = page.locator('button[class*="bg-primary"], [class*="text-primary"], .gradient-text');
      const elementCount = await primaryElements.count();
      
      for (let i = 0; i < Math.min(elementCount, 3); i++) {
        const element = primaryElements.nth(i);
        if (await element.isVisible()) {
          await element.screenshot({
            path: `test-results/android-green/04-${pageInfo.name.toLowerCase()}-green-element-${i}.png`
          });
        }
      }

      // Check navigation active states
      const navLinks = page.locator('nav a, [role="navigation"] a');
      const navCount = await navLinks.count();
      
      for (let i = 0; i < navCount; i++) {
        const navLink = navLinks.nth(i);
        if (await navLink.isVisible()) {
          const isActive = await navLink.evaluate(el => 
            el.classList.contains('active') || 
            el.getAttribute('aria-current') === 'page' ||
            el.classList.contains('bg-primary') ||
            el.classList.contains('text-primary')
          );
          
          if (isActive) {
            await navLink.screenshot({
              path: `test-results/android-green/04-nav-active-${pageInfo.name.toLowerCase()}.png`
            });
          }
        }
      }
    }
  });

  test('Focus States: Android Green focus rings and states', async () => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // Test focus states on interactive elements
    const focusableElements = page.locator('button, input, select, textarea, a[href]');
    const elementCount = await focusableElements.count();
    
    for (let i = 0; i < Math.min(elementCount, 8); i++) {
      const element = focusableElements.nth(i);
      if (await element.isVisible()) {
        // Focus the element
        await element.focus();
        await page.waitForTimeout(500);
        
        await element.screenshot({
          path: `test-results/android-green/05-focus-state-${i}.png`
        });
      }
    }

    // Test form inputs specifically
    const inputs = page.locator('input, textarea');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < Math.min(inputCount, 3); i++) {
      const input = inputs.nth(i);
      if (await input.isVisible()) {
        await input.focus();
        await page.waitForTimeout(300);
        
        await input.screenshot({
          path: `test-results/android-green/05-input-focus-${i}.png`
        });
      }
    }
  });

  test('Hover Effects: Android Green hover interactions', async () => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // Test hover effects on buttons
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        // Normal state
        await button.screenshot({
          path: `test-results/android-green/06-button-${i}-normal.png`
        });
        
        // Hover state
        await button.hover();
        await page.waitForTimeout(300);
        await button.screenshot({
          path: `test-results/android-green/06-button-${i}-hover.png`
        });
      }
    }

    // Test hover on navigation elements
    const navItems = page.locator('nav a, [role="navigation"] a');
    const navCount = await navItems.count();
    
    for (let i = 0; i < navCount; i++) {
      const navItem = navItems.nth(i);
      if (await navItem.isVisible()) {
        await navItem.hover();
        await page.waitForTimeout(300);
        await navItem.screenshot({
          path: `test-results/android-green/06-nav-hover-${i}.png`
        });
      }
    }
  });

  test('Color Consistency: Android Green usage patterns', async () => {
    const pages = ['/', '/sync', '/maintenance', '/settings'];
    
    for (const pagePath of pages) {
      await page.goto(`http://localhost:3001${pagePath}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);

      // Check for Android Green consistency
      await page.screenshot({
        path: `test-results/android-green/07-consistency-${pagePath.replace('/', 'dashboard') || 'dashboard'}.png`,
        fullPage: true
      });
    }
  });

  test('Android Green vs Firebase Red: Visual comparison setup', async () => {
    // Take reference screenshots for comparison
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Main dashboard for comparison
    await page.screenshot({
      path: 'test-results/android-green/08-android-green-reference.png',
      fullPage: true
    });

    // Capture specific UI elements for detailed comparison
    const keyElements = [
      { selector: 'h1, h2, .gradient-text', name: 'logo-text' },
      { selector: 'button[class*="primary"]', name: 'primary-button' },
      { selector: 'nav', name: 'navigation' },
      { selector: '.card', name: 'card-component' }
    ];

    for (const element of keyElements) {
      const el = page.locator(element.selector).first();
      if (await el.count() > 0 && await el.isVisible()) {
        await el.screenshot({
          path: `test-results/android-green/08-${element.name}-android-green.png`
        });
      }
    }
  });
});