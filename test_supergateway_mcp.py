#!/usr/bin/env python3
"""
Test script to verify MCP server works through supergateway
"""
import subprocess
import time
import requests
import json
import sys
import logging
from threading import Thread
import signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupergatewayTester:
    def __init__(self):
        self.supergateway_process = None
        self.test_port = 8001
        
    def start_supergateway(self):
        """Start supergateway process"""
        try:
            cmd = [
                "npx", "-y", "supergateway@latest",
                "--sse", 
                "http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY",
                "--timeout", "120000",
                "--port", str(self.test_port),
                "--logLevel", "info"
            ]
            
            logger.info(f"Starting supergateway with command: {' '.join(cmd)}")
            self.supergateway_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give it a moment to start
            time.sleep(3)
            
            # Check if process is still running
            if self.supergateway_process.poll() is None:
                logger.info("✅ Supergateway started successfully")
                return True
            else:
                stdout, stderr = self.supergateway_process.communicate()
                logger.error(f"❌ Supergateway failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting supergateway: {e}")
            return False
    
    def stop_supergateway(self):
        """Stop supergateway process"""
        if self.supergateway_process:
            logger.info("Stopping supergateway...")
            self.supergateway_process.terminate()
            try:
                self.supergateway_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.supergateway_process.kill()
                self.supergateway_process.wait()
            logger.info("✅ Supergateway stopped")
    
    def test_mcp_connection(self):
        """Test MCP connection through supergateway"""
        try:
            # Test health endpoint
            health_url = f"http://localhost:{self.test_port}/healthz"
            logger.info(f"Testing health endpoint: {health_url}")
            
            response = requests.get(health_url, timeout=10)
            if response.status_code == 200:
                logger.info("✅ Health endpoint accessible")
                return True
            else:
                logger.error(f"❌ Health endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing MCP connection: {e}")
            return False
    
    def test_sse_connection(self):
        """Test SSE connection through supergateway"""
        try:
            sse_url = f"http://localhost:{self.test_port}/sse"
            logger.info(f"Testing SSE endpoint: {sse_url}")
            
            response = requests.get(sse_url, timeout=5, stream=True)
            if response.status_code == 200:
                logger.info("✅ SSE endpoint accessible")
                return True
            else:
                logger.error(f"❌ SSE endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing SSE connection: {e}")
            return False
    
    def run_tests(self):
        """Run all tests"""
        logger.info("🚀 Starting supergateway MCP server tests")
        
        # Start supergateway
        if not self.start_supergateway():
            return False
            
        try:
            # Wait a bit more for full startup
            time.sleep(5)
            
            # Run tests
            tests = [
                ("MCP Connection", self.test_mcp_connection),
                ("SSE Connection", self.test_sse_connection),
            ]
            
            results = []
            for test_name, test_func in tests:
                logger.info(f"🧪 Running test: {test_name}")
                result = test_func()
                results.append((test_name, result))
                logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
            
            # Summary
            passed = sum(1 for _, result in results if result)
            total = len(results)
            
            logger.info(f"📊 Test Results: {passed}/{total} tests passed")
            
            if passed == total:
                logger.info("🎉 All tests passed! Supergateway MCP server is working correctly.")
                return True
            else:
                logger.error("❌ Some tests failed. Please check the logs above.")
                return False
                
        finally:
            self.stop_supergateway()

def main():
    """Main function"""
    tester = SupergatewayTester()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        logger.info("Received interrupt signal")
        tester.stop_supergateway()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = tester.run_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()