import { User } from '@supabase/supabase-js'

export const SUPABASE_TO_MEMORY_USER_MAP = {
  '<EMAIL>': 'aungheinaye',
  '<EMAIL>': 'yohanna'
} as const

export type MemoryUserId = typeof SUPABASE_TO_MEMORY_USER_MAP[keyof typeof SUPABASE_TO_MEMORY_USER_MAP]

export const getCurrentMemoryUserId = (supabaseUser: User | null): string => {
  if (!supabaseUser?.email) return ''
  return SUPABASE_TO_MEMORY_USER_MAP[supabaseUser.email as keyof typeof SUPABASE_TO_MEMORY_USER_MAP] || ''
}

export const getDisplayName = (memoryUserId: string): string => {
  switch (memoryUserId) {
    case 'aungheinaye':
      return 'Aung'
    case 'yohanna':
      return 'Yohanna'
    default:
      return 'Unknown User'
  }
}

export const getUserDbId = (memoryUserId: string): string => {
  return memoryUserId
}