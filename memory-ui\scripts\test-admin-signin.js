// Test admin sign in
const { createClient } = require('@supabase/supabase-js')

const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://192.168.1.218:8000',
  SUPABASE_SERVICE_KEY
)

async function testAdminSignIn() {
  console.log('Testing admin sign in as user...')
  
  try {
    // Get user ID
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers()
    if (userError) {
      console.error('Error getting users:', userError)
      return
    }
    
    console.log('Found users:', userData.users.map(u => ({ id: u.id, email: u.email })))
    
    const aungUser = userData.users.find(u => u.email === '<EMAIL>')
    if (!aungUser) {
      console.error('Aung user not found')
      return
    }
    
    // Generate session for user
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: '<EMAIL>'
    })
    
    if (sessionError) {
      console.error('Error generating session:', sessionError)
      return
    }
    
    console.log('Generated session link:', sessionData.properties.hashed_token)
    
    // Try to reset password to a known value
    const { data: resetData, error: resetError } = await supabase.auth.admin.updateUserById(
      aungUser.id,
      {
        password: 'password123'
      }
    )
    
    if (resetError) {
      console.error('Error resetting password:', resetError)
    } else {
      console.log('Password reset successful')
    }
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

testAdminSignIn()