'use client';

import React from 'react';
import { usePresence, PresenceUser } from '../hooks/usePresence';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Badge } from './ui/badge';
import { Users, Circle } from 'lucide-react';
import { cn } from '../lib/utils';

const getActivityLabel = (activity: string): string => {
  switch (activity) {
    case 'viewing_dashboard':
      return 'Dashboard';
    case 'managing_memories':
      return 'Memories';
    case 'viewing_memory':
      return 'Viewing Memory';
    case 'monitoring_sync':
      return 'Sync Monitor';
    case 'in_maintenance':
      return 'Maintenance';
    case 'configuring_settings':
      return 'Settings';
    default:
      return 'Browsing';
  }
};

const getActivityColor = (activity: string): string => {
  switch (activity) {
    case 'viewing_dashboard':
      return 'text-blue-500';
    case 'managing_memories':
      return 'text-green-500';
    case 'viewing_memory':
      return 'text-green-400';
    case 'monitoring_sync':
      return 'text-yellow-500';
    case 'in_maintenance':
      return 'text-red-500';
    case 'configuring_settings':
      return 'text-purple-500';
    default:
      return 'text-gray-500';
  }
};

const getUserInitials = (displayName: string): string => {
  return displayName
    .split(' ')
    .map(name => name[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

interface PresenceIndicatorProps {
  className?: string;
  compact?: boolean;
}

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({ 
  className,
  compact = false 
}) => {
  const { activeUsers, connectionState } = usePresence();

  if (connectionState === 'disconnected') {
    return null;
  }

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex -space-x-2">
          {activeUsers.slice(0, 3).map((user) => (
            <Avatar key={user.userId} className="w-6 h-6 border-2 border-background">
              <AvatarFallback className="text-xs bg-primary/20 text-primary">
                {getUserInitials(user.displayName)}
              </AvatarFallback>
            </Avatar>
          ))}
        </div>
        {activeUsers.length > 0 && (
          <span className="text-xs text-muted-foreground">
            {activeUsers.length} online
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center gap-2">
        <Users className="w-4 h-4 text-muted-foreground" />
        <span className="text-sm font-medium">Active Users</span>
        <Badge variant="secondary" className="text-xs">
          {activeUsers.length}
        </Badge>
        <div className="flex items-center gap-1">
          <Circle 
            className={cn(
              "w-2 h-2 fill-current",
              connectionState === 'connected' ? 'text-green-500' : 'text-yellow-500'
            )}
          />
          <span className="text-xs text-muted-foreground capitalize">
            {connectionState}
          </span>
        </div>
      </div>

      {activeUsers.length === 0 ? (
        <div className="text-sm text-muted-foreground">
          No other users online
        </div>
      ) : (
        <div className="space-y-2">
          {activeUsers.map((user) => (
            <div key={user.userId} className="flex items-center gap-3 p-2 rounded-lg bg-secondary/30">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-primary/20 text-primary text-xs">
                  {getUserInitials(user.displayName)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium truncate">
                    {user.displayName}
                  </span>
                  <Circle className="w-2 h-2 fill-current text-green-500" />
                </div>
                
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span className={getActivityColor(user.activity)}>
                    {getActivityLabel(user.activity)}
                  </span>
                  <span>•</span>
                  <span>
                    {new Date(user.lastSeen).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};