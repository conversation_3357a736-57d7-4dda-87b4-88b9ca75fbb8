import { test, expect, Page } from '@playwright/test';

test.describe('Google/Firebase Color Palette Implementation', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('Dashboard - Firebase Red Gradient in Memory Master Logo', async () => {
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/firebase-colors/01-dashboard-full.png',
      fullPage: true 
    });

    // Find and screenshot the logo area specifically
    const logoElement = page.locator('h1').filter({ hasText: 'Memory Master' });
    await expect(logoElement).toBeVisible();
    
    // Take screenshot of the logo area
    await logoElement.screenshot({ 
      path: 'test-results/firebase-colors/01a-memory-master-logo.png' 
    });

    // Verify the gradient classes are applied
    const logoClasses = await logoElement.getAttribute('class');
    expect(logoClasses).toContain('bg-gradient-to-r');
    expect(logoClasses).toContain('from-firebase-red');
    
    console.log('Logo classes:', logoClasses);
  });

  test('Dashboard - Firebase Red Primary Buttons', async () => {
    // Look for Create Memory button and other primary buttons
    const createMemoryButton = page.locator('button').filter({ hasText: 'Create Memory' });
    
    if (await createMemoryButton.isVisible()) {
      await createMemoryButton.screenshot({ 
        path: 'test-results/firebase-colors/02a-create-memory-button.png' 
      });
      
      // Verify button styling
      const buttonClasses = await createMemoryButton.getAttribute('class');
      console.log('Create Memory button classes:', buttonClasses);
      
      // Test hover state
      await createMemoryButton.hover();
      await page.waitForTimeout(500);
      await createMemoryButton.screenshot({ 
        path: 'test-results/firebase-colors/02b-create-memory-button-hover.png' 
      });
    }

    // Look for other primary buttons
    const allButtons = page.locator('button').filter({ hasText: /^(Create|Add|Save|Submit|Update)/ });
    const buttonCount = await allButtons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = allButtons.nth(i);
      if (await button.isVisible()) {
        const buttonText = await button.textContent();
        await button.screenshot({ 
          path: `test-results/firebase-colors/02c-button-${i}-${buttonText?.replace(/\s+/g, '-').toLowerCase()}.png` 
        });
      }
    }
  });

  test('Dashboard - Updated Stat Card Colors', async () => {
    // Find stat cards
    const statCards = page.locator('[class*="card"]').or(page.locator('[role="region"]')).or(page.locator('.bg-white'));
    const cardCount = await statCards.count();
    
    console.log(`Found ${cardCount} potential stat cards`);
    
    // Screenshot the stats section
    await page.screenshot({ 
      path: 'test-results/firebase-colors/03a-dashboard-stats-full.png',
      fullPage: true 
    });

    // Try to find specific stat areas
    const statsArea = page.locator('div').filter({ hasText: /Total Memories|Active Connections|Last Sync/ });
    if (await statsArea.first().isVisible()) {
      await statsArea.first().screenshot({ 
        path: 'test-results/firebase-colors/03b-stats-area.png' 
      });
    }
  });

  test('Dashboard - Connection Status Indicators', async () => {
    // Look for connection status indicators
    const statusIndicators = page.locator('[class*="status"]').or(
      page.locator('[class*="indicator"]')
    ).or(
      page.locator('[class*="connected"]')
    ).or(
      page.locator('[class*="online"]')
    );
    
    const indicatorCount = await statusIndicators.count();
    console.log(`Found ${indicatorCount} potential status indicators`);
    
    for (let i = 0; i < indicatorCount; i++) {
      const indicator = statusIndicators.nth(i);
      if (await indicator.isVisible()) {
        await indicator.screenshot({ 
          path: `test-results/firebase-colors/04a-status-indicator-${i}.png` 
        });
      }
    }

    // Look for presence indicators or user status
    const presenceIndicators = page.locator('[data-testid*="presence"]').or(
      page.locator('.presence-indicator')
    );
    
    if (await presenceIndicators.first().isVisible()) {
      await presenceIndicators.first().screenshot({ 
        path: 'test-results/firebase-colors/04b-presence-indicator.png' 
      });
    }
  });

  test('Navigation - Sync Monitor Page', async () => {
    // Navigate to Sync Monitor
    const syncLink = page.locator('a').filter({ hasText: /sync/i });
    if (await syncLink.isVisible()) {
      await syncLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      await page.screenshot({ 
        path: 'test-results/firebase-colors/05a-sync-monitor-full.png',
        fullPage: true 
      });

      // Check for Firebase Red elements on this page
      const primaryButtons = page.locator('button[class*="bg-firebase-red"]').or(
        page.locator('button[class*="red"]')
      );
      
      if (await primaryButtons.first().isVisible()) {
        await primaryButtons.first().screenshot({ 
          path: 'test-results/firebase-colors/05b-sync-monitor-buttons.png' 
        });
      }
    }
  });

  test('Navigation - Maintenance Page', async () => {
    // Navigate to Maintenance
    const maintenanceLink = page.locator('a').filter({ hasText: /maintenance/i });
    if (await maintenanceLink.isVisible()) {
      await maintenanceLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      await page.screenshot({ 
        path: 'test-results/firebase-colors/06a-maintenance-full.png',
        fullPage: true 
      });

      // Check for Firebase Red elements
      const redElements = page.locator('[class*="firebase-red"]').or(
        page.locator('[class*="red-"]')
      );
      
      const elementCount = await redElements.count();
      for (let i = 0; i < elementCount; i++) {
        const element = redElements.nth(i);
        if (await element.isVisible()) {
          await element.screenshot({ 
            path: `test-results/firebase-colors/06b-maintenance-red-element-${i}.png` 
          });
        }
      }
    }
  });

  test('Navigation - Settings Page', async () => {
    // Navigate to Settings
    const settingsLink = page.locator('a').filter({ hasText: /settings/i });
    if (await settingsLink.isVisible()) {
      await settingsLink.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      await page.screenshot({ 
        path: 'test-results/firebase-colors/07a-settings-full.png',
        fullPage: true 
      });

      // Test form elements for Firebase Red focus states
      const inputs = page.locator('input, textarea, select');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 3); i++) {
        const input = inputs.nth(i);
        if (await input.isVisible()) {
          await input.focus();
          await page.waitForTimeout(300);
          await input.screenshot({ 
            path: `test-results/firebase-colors/07b-settings-input-focus-${i}.png` 
          });
        }
      }
    }
  });

  test('Interactive Elements - Button Hover States', async () => {
    // Go back to dashboard for comprehensive button testing
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Find all buttons and test hover states
    const buttons = page.locator('button:visible');
    const buttonCount = await buttons.count();
    
    console.log(`Testing hover states for ${buttonCount} buttons`);
    
    for (let i = 0; i < Math.min(buttonCount, 10); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        const buttonText = (await button.textContent())?.trim() || `button-${i}`;
        const safeButtonName = buttonText.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
        
        // Normal state
        await button.screenshot({ 
          path: `test-results/firebase-colors/08a-button-${i}-${safeButtonName}-normal.png` 
        });
        
        // Hover state
        await button.hover();
        await page.waitForTimeout(300);
        await button.screenshot({ 
          path: `test-results/firebase-colors/08b-button-${i}-${safeButtonName}-hover.png` 
        });
        
        // Move away to reset hover
        await page.mouse.move(0, 0);
        await page.waitForTimeout(200);
      }
    }
  });

  test('Interactive Elements - Active Navigation States', async () => {
    // Test navigation active states
    const navLinks = page.locator('nav a, [role="navigation"] a').or(
      page.locator('a').filter({ hasText: /Dashboard|Sync|Maintenance|Settings/ })
    );
    
    const linkCount = await navLinks.count();
    console.log(`Found ${linkCount} navigation links`);
    
    for (let i = 0; i < linkCount; i++) {
      const link = navLinks.nth(i);
      if (await link.isVisible()) {
        const linkText = (await link.textContent())?.trim() || `link-${i}`;
        const safeLinkName = linkText.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
        
        // Click and screenshot active state
        await link.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(500);
        
        await page.screenshot({ 
          path: `test-results/firebase-colors/09a-nav-active-${safeLinkName}.png`,
          fullPage: true 
        });
        
        // Screenshot the active navigation element specifically
        await link.screenshot({ 
          path: `test-results/firebase-colors/09b-nav-link-active-${safeLinkName}.png` 
        });
      }
    }
  });

  test('Color Consistency Analysis', async () => {
    // Navigate through all pages and analyze color consistency
    const pages = ['/', '/sync', '/maintenance', '/settings'];
    
    for (const pagePath of pages) {
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
      
      const pageName = pagePath === '/' ? 'dashboard' : pagePath.substring(1);
      
      // Take full page screenshot
      await page.screenshot({ 
        path: `test-results/firebase-colors/10a-consistency-${pageName}.png`,
        fullPage: true 
      });
      
      // Analyze Firebase Red elements
      const firebaseRedElements = await page.locator('[class*="firebase-red"], [class*="red-"], [style*="red"]').count();
      console.log(`${pageName}: Found ${firebaseRedElements} Firebase Red elements`);
      
      // Analyze gradient elements
      const gradientElements = await page.locator('[class*="gradient"], [class*="bg-gradient"]').count();
      console.log(`${pageName}: Found ${gradientElements} gradient elements`);
    }
  });
});