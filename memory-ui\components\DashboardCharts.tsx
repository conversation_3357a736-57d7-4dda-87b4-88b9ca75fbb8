"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"

interface DashboardChartsProps {
  data: any[]
  isLoading?: boolean
}

// Lightweight chart replacement - removed recharts for performance
export function DashboardCharts({ data, isLoading }: DashboardChartsProps) {
  if (isLoading) {
    return (
      <Card className="glass border-border/50">
        <CardHeader>
          <CardTitle>Memory Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    )
  }

  // Simple SVG-based chart for performance
  const maxValue = Math.max(...data.map(d => d.memories || 0), 1)
  
  return (
    <Card className="glass border-border/50">
      <CardHeader>
        <CardTitle>Memory Analytics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64 relative">
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Grid lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={`h-${i}`}
                x1="40"
                y1={40 + i * 40}
                x2="380"
                y2={40 + i * 40}
                stroke="currentColor"
                strokeOpacity="0.1"
                strokeDasharray="2,2"
              />
            ))}
            
            {/* Y-axis labels */}
            {[0, 1, 2, 3, 4].map(i => (
              <text
                key={`y-${i}`}
                x="35"
                y={45 + (4 - i) * 40}
                fontSize="10"
                fill="currentColor"
                textAnchor="end"
                opacity="0.6"
              >
                {Math.round((maxValue / 4) * i)}
              </text>
            ))}
            
            {/* Data visualization */}
            {data.length > 0 && (
              <path
                d={
                  data.map((point, index) => {
                    const x = 40 + (index * (340 / Math.max(data.length - 1, 1)))
                    const y = 200 - 40 - ((point.memories || 0) / maxValue) * 120
                    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`
                  }).join(' ')
                }
                stroke="#2563eb"
                strokeWidth="2"
                fill="none"
              />
            )}
            
            {/* Data points */}
            {data.map((point, index) => {
              const x = 40 + (index * (340 / Math.max(data.length - 1, 1)))
              const y = 200 - 40 - ((point.memories || 0) / maxValue) * 120
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="3"
                  fill="#2563eb"
                />
              )
            })}
          </svg>
          
          {/* Simple tooltip on hover */}
          <div className="absolute bottom-2 left-2 text-xs text-muted-foreground">
            {data.length > 0 && `${data.length} data points`}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}