import { test, expect } from '@playwright/test';
import { Page } from '@playwright/test';

test.describe('Memory Master v2 - Comprehensive E2E Test Suite', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test.describe('Authentication & User Management', () => {
    test('should handle user switching and maintain session state', async ({ page }) => {
      // Test user switching between different personas
      const users = ['Guest User', 'Aung Hein Aye', 'Yohanna'];
      
      for (const user of users) {
        await page.locator(`button:has-text("${user}")`).click();
        await page.waitForTimeout(1000);
        
        // Verify user switched
        await expect(page.locator('h1')).toContainText(`Welcome back, ${user}`);
        
        // Verify user-specific data loads
        const userIdElement = page.locator('text="User ID:"');
        await expect(userIdElement).toBeVisible();
        
        // Take screenshot for visual verification
        await page.screenshot({ 
          path: `test-results/screenshots/user-${user.toLowerCase().replace(/\s+/g, '-')}.png`,
          fullPage: true 
        });
      }
    });

    test('should maintain authentication state across page reloads', async ({ page }) => {
      await page.locator('button:has-text("Aung Hein Aye")').click();
      await page.waitForTimeout(1000);
      
      // Reload page
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Verify user state persisted
      await expect(page.locator('h1')).toContainText('Welcome back, Aung Hein Aye');
      await expect(page.locator('text="User ID: aungheinaye"')).toBeVisible();
    });
  });

  test.describe('Memory Operations', () => {
    test('should perform complete memory CRUD operations', async ({ page }) => {
      // Switch to Aung (user with existing data)
      await page.locator('button:has-text("Aung Hein Aye")').click();
      await page.waitForTimeout(1000);
      
      // Navigate to memories page
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Test CREATE operation
      await page.locator('button:has-text("Create Memory")').click();
      await page.waitForTimeout(1000);
      
      // Fill memory form
      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
      const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
      
      if (await titleInput.isVisible()) {
        await titleInput.fill('E2E Test Memory - Comprehensive');
        await contentInput.fill('This is a comprehensive test memory created during automated testing. It contains important information about the dropshipping business operations, customer service procedures, and eBay store management.');
        
        // Submit form
        const submitButton = page.locator('button:has-text("Create"), button:has-text("Save"), button:has-text("Submit")').first();
        await submitButton.click();
        await page.waitForTimeout(3000);
        
        // Verify creation success
        const successIndicator = page.locator('text="Memory created", text="Success", text="Added successfully"');
        if (await successIndicator.isVisible()) {
          console.log('✓ Memory creation successful');
        }
      }
      
      // Test READ operation - verify memory list
      const memoryItems = page.locator('[data-testid="memory-item"]');
      const memoryCount = await memoryItems.count();
      expect(memoryCount).toBeGreaterThan(0);
      console.log(`✓ Found ${memoryCount} memory items`);
      
      // Test SEARCH functionality
      const searchInput = page.locator('input[placeholder*="search" i]').first();
      if (await searchInput.isVisible()) {
        await searchInput.fill('E2E Test');
        await page.waitForTimeout(1000);
        
        // Verify search results
        const searchResults = page.locator('[data-testid="memory-item"]');
        const searchCount = await searchResults.count();
        console.log(`✓ Search returned ${searchCount} results`);
      }
      
      // Test UPDATE operation (if edit functionality exists)
      const firstMemoryItem = memoryItems.first();
      const editButton = firstMemoryItem.locator('button:has-text("Edit"), button[aria-label*="edit" i]').first();
      
      if (await editButton.isVisible()) {
        await editButton.click();
        await page.waitForTimeout(1000);
        
        // Modify content
        const editContentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
        if (await editContentInput.isVisible()) {
          await editContentInput.fill('Updated content during E2E testing - comprehensive functionality verification');
          
          const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")').first();
          await saveButton.click();
          await page.waitForTimeout(2000);
          console.log('✓ Memory update successful');
        }
      }
      
      // Take screenshot of memory operations
      await page.screenshot({ 
        path: 'test-results/screenshots/memory-operations.png',
        fullPage: true 
      });
    });

    test('should handle memory operations with different content types', async ({ page }) => {
      await page.locator('button:has-text("Aung Hein Aye")').click();
      await page.waitForTimeout(1000);
      
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Test different content types
      const contentTypes = [
        {
          title: 'Customer Service Protocol',
          content: 'Standard customer service procedures for handling returns, refunds, and complaints on eBay platform.',
          type: 'procedure'
        },
        {
          title: 'Product Listing Template',
          content: 'Template for creating optimized product listings: Title format, description structure, pricing strategy, and keyword optimization.',
          type: 'template'
        },
        {
          title: 'Supplier Contact Information',
          content: 'Primary supplier: AliExpress Store XYZ, Contact: <EMAIL>, Phone: ******-0123, Response time: 24-48 hours.',
          type: 'contact'
        }
      ];
      
      for (const memory of contentTypes) {
        await page.locator('button:has-text("Create Memory")').click();
        await page.waitForTimeout(1000);
        
        const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
        const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
        
        if (await titleInput.isVisible()) {
          await titleInput.fill(memory.title);
          await contentInput.fill(memory.content);
          
          const submitButton = page.locator('button:has-text("Create"), button:has-text("Save")').first();
          await submitButton.click();
          await page.waitForTimeout(2000);
          
          console.log(`✓ Created ${memory.type} memory: ${memory.title}`);
        }
      }
    });
  });

  test.describe('Evolution Intelligence System', () => {
    test('should display evolution analytics and insights', async ({ page }) => {
      // Navigate to evolution/analytics if it exists
      await page.goto('/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check for evolution intelligence elements
      const evolutionElements = [
        'text="Evolution Intelligence"',
        'text="Analytics"',
        'text="Operations"',
        'text="Confidence Score"',
        'text="Memory Operations"'
      ];
      
      let evolutionFeaturesFound = 0;
      for (const selector of evolutionElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          evolutionFeaturesFound++;
          console.log(`✓ Found evolution element: ${selector}`);
        }
      }
      
      if (evolutionFeaturesFound > 0) {
        console.log(`✓ Evolution Intelligence system active with ${evolutionFeaturesFound} features`);
        
        // Take screenshot of evolution dashboard
        await page.screenshot({ 
          path: 'test-results/screenshots/evolution-intelligence.png',
          fullPage: true 
        });
      } else {
        console.log('! Evolution Intelligence features not found on this page');
      }
    });
  });

  test.describe('System Health & Monitoring', () => {
    test('should display system health status', async ({ page }) => {
      // Verify system health cards
      const healthElements = [
        'text="Backend System Health"',
        'text="Memory Engine"',
        'text="Database Status"',
        'text="API Response Time"'
      ];
      
      let healthElementsFound = 0;
      for (const selector of healthElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          healthElementsFound++;
          console.log(`✓ Health element found: ${selector}`);
        }
      }
      
      expect(healthElementsFound).toBeGreaterThan(0);
      console.log(`✓ System health monitoring active with ${healthElementsFound} indicators`);
      
      // Test API health endpoint
      const response = await page.request.get('/api/health');
      expect(response.status()).toBe(200);
      console.log('✓ API health endpoint responding');
    });

    test('should handle API connectivity issues gracefully', async ({ page }) => {
      // Test error handling by attempting to reach a non-existent endpoint
      await page.route('**/api/memories**', route => route.abort());
      
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Should show error state or loading state
      const errorIndicators = [
        'text="Error"',
        'text="Failed to load"',
        'text="Connection issue"',
        'text="Try again"',
        'text="Loading"'
      ];
      
      let errorHandlingFound = false;
      for (const selector of errorIndicators) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          errorHandlingFound = true;
          console.log(`✓ Error handling active: ${selector}`);
          break;
        }
      }
      
      expect(errorHandlingFound).toBe(true);
    });
  });

  test.describe('Navigation & UI Components', () => {
    test('should navigate between all main pages', async ({ page }) => {
      const pages = [
        { name: 'Dashboard', path: '/', elements: ['text="Welcome back"', 'text="Total Memories"'] },
        { name: 'Memories', path: '/memories', elements: ['text="Memory Management"', 'button:has-text("Create Memory")'] },
        { name: 'Settings', path: '/settings', elements: ['text="Settings"'] },
        { name: 'Evolution', path: '/evolution', elements: ['text="Evolution"'] }
      ];
      
      for (const pageInfo of pages) {
        await page.goto(pageInfo.path);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Verify page loaded
        const pageTitle = await page.title();
        expect(pageTitle).toContain('Memory Master');
        
        // Check for page-specific elements
        let elementsFound = 0;
        for (const selector of pageInfo.elements) {
          const element = page.locator(selector);
          if (await element.isVisible()) {
            elementsFound++;
          }
        }
        
        console.log(`✓ ${pageInfo.name} page loaded with ${elementsFound}/${pageInfo.elements.length} elements`);
        
        // Take screenshot
        await page.screenshot({ 
          path: `test-results/screenshots/page-${pageInfo.name.toLowerCase()}.png`,
          fullPage: true 
        });
      }
    });

    test('should handle responsive design across viewport sizes', async ({ page }) => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 1366, height: 768, name: 'Laptop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        // Verify responsive elements
        const mainContent = page.locator('main, [role="main"]');
        await expect(mainContent).toBeVisible();
        
        // Check navigation is accessible
        const navElements = page.locator('nav, [role="navigation"]');
        const navCount = await navElements.count();
        expect(navCount).toBeGreaterThan(0);
        
        // Take screenshot for visual verification
        await page.screenshot({ 
          path: `test-results/screenshots/responsive-${viewport.name.toLowerCase()}.png`,
          fullPage: true 
        });
        
        console.log(`✓ ${viewport.name} (${viewport.width}x${viewport.height}) responsive design verified`);
      }
    });
  });

  test.describe('Performance & Load Testing', () => {
    test('should handle concurrent user actions', async ({ page }) => {
      // Switch to user with data
      await page.locator('button:has-text("Aung Hein Aye")').click();
      await page.waitForTimeout(1000);
      
      // Navigate to memories
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Perform multiple rapid actions
      const searchInput = page.locator('input[placeholder*="search" i]').first();
      
      if (await searchInput.isVisible()) {
        // Rapid search operations
        const searchTerms = ['customer', 'product', 'supplier', 'eBay', 'service'];
        
        for (const term of searchTerms) {
          await searchInput.fill(term);
          await page.waitForTimeout(500);
          
          // Verify search responds within reasonable time
          const startTime = Date.now();
          await page.waitForLoadState('networkidle');
          const endTime = Date.now();
          
          const responseTime = endTime - startTime;
          expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds
          
          console.log(`✓ Search for "${term}" completed in ${responseTime}ms`);
        }
      }
    });

    test('should measure page load performance', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const endTime = Date.now();
      const loadTime = endTime - startTime;
      
      // Performance assertions
      expect(loadTime).toBeLessThan(10000); // Should load within 10 seconds
      console.log(`✓ Page loaded in ${loadTime}ms`);
      
      // Check for performance metrics
      const performanceEntries = await page.evaluate(() => {
        return JSON.stringify(performance.getEntriesByType('navigation'));
      });
      
      console.log('✓ Performance metrics captured:', JSON.parse(performanceEntries).length, 'entries');
    });
  });

  test.describe('Data Integrity & Validation', () => {
    test('should validate memory data persistence', async ({ page }) => {
      // Create a memory with specific content
      await page.locator('button:has-text("Aung Hein Aye")').click();
      await page.waitForTimeout(1000);
      
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      const testMemoryTitle = `Data Integrity Test - ${Date.now()}`;
      const testMemoryContent = 'This memory tests data persistence and integrity across sessions and user switches.';
      
      // Create memory
      await page.locator('button:has-text("Create Memory")').click();
      await page.waitForTimeout(1000);
      
      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
      const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
      
      if (await titleInput.isVisible()) {
        await titleInput.fill(testMemoryTitle);
        await contentInput.fill(testMemoryContent);
        
        const submitButton = page.locator('button:has-text("Create"), button:has-text("Save")').first();
        await submitButton.click();
        await page.waitForTimeout(3000);
        
        // Switch to another user
        await page.goto('/');
        await page.locator('button:has-text("Yohanna")').click();
        await page.waitForTimeout(1000);
        
        // Switch back to original user
        await page.locator('button:has-text("Aung Hein Aye")').click();
        await page.waitForTimeout(1000);
        
        // Navigate back to memories
        await page.goto('/memories');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Search for the created memory
        const searchInput = page.locator('input[placeholder*="search" i]').first();
        if (await searchInput.isVisible()) {
          await searchInput.fill(testMemoryTitle);
          await page.waitForTimeout(1000);
          
          // Verify memory still exists
          const memoryItem = page.locator(`text="${testMemoryTitle}"`);
          await expect(memoryItem).toBeVisible();
          
          console.log('✓ Memory data persisted across user switches');
        }
      }
    });
  });

  test.describe('Security & Access Control', () => {
    test('should enforce user data isolation', async ({ page }) => {
      // Test that different users see different data
      const userDataCounts = new Map();
      
      const users = ['Aung Hein Aye', 'Yohanna', 'Guest User'];
      
      for (const user of users) {
        await page.locator(`button:has-text("${user}")`).click();
        await page.waitForTimeout(1000);
        
        // Get memory count for this user
        const memoryCountElement = page.locator('text="Total Memories"').locator('..').locator('p.text-3xl');
        const memoryCount = await memoryCountElement.textContent();
        
        userDataCounts.set(user, memoryCount);
        console.log(`✓ User ${user} has ${memoryCount} memories`);
      }
      
      // Verify users have different data sets
      const aungCount = userDataCounts.get('Aung Hein Aye');
      const yohannaCount = userDataCounts.get('Yohanna');
      const guestCount = userDataCounts.get('Guest User');
      
      // Aung should have the most data, Yohanna should have 0
      expect(yohannaCount).toBe('0');
      expect(aungCount).not.toBe(yohannaCount);
      
      console.log('✓ User data isolation verified');
    });
  });

  test.afterEach(async ({ page }) => {
    // Clean up - take final screenshot
    await page.screenshot({ 
      path: `test-results/screenshots/final-state-${Date.now()}.png`,
      fullPage: true 
    });
  });
});