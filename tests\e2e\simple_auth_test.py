#!/usr/bin/env python3
"""
Simple MCP Authentication Test
"""

import requests
import time

def test_auth():
    base_url = "http://localhost:5678"
    valid_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
    
    print("🧪 Simple MCP Authentication Test")
    print("=" * 50)
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ Health Check: {response.status_code}")
    except Exception as e:
        print(f"❌ Health Check: {e}")
        return
    
    # Test 2: Query params authentication (valid)
    print("\n📋 Testing Query Parameter Authentication")
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            params={"api_key": valid_key, "user_id": "aungheinaye"},
            timeout=2,
            stream=True
        )
        print(f"✅ Valid Key Query Params: Status {response.status_code}")
        if response.status_code == 200:
            print("   🎉 Authentication successful!")
        elif response.status_code == 401:
            print("   ❌ Authentication failed")
        else:
            print(f"   ℹ️  Other status (likely SSE connection issue): {response.status_code}")
    except requests.exceptions.ReadTimeout:
        print("✅ Valid Key Query Params: Timeout (expected for SSE, means auth passed)")
    except Exception as e:
        print(f"❌ Valid Key Query Params: {e}")
    
    # Test 3: Query params authentication (invalid)
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            params={"api_key": "invalid_key", "user_id": "aungheinaye"},
            timeout=2
        )
        if response.status_code == 401:
            print("✅ Invalid Key Query Params: Correctly rejected (401)")
        else:
            print(f"❌ Invalid Key Query Params: Should be 401, got {response.status_code}")
    except Exception as e:
        print(f"❌ Invalid Key Query Params: {e}")
    
    # Test 4: Header authentication (valid)
    print("\n📋 Testing Header Authentication")
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            headers={"Authorization": f"Bearer {valid_key}"},
            timeout=2,
            stream=True
        )
        print(f"✅ Valid Key Header: Status {response.status_code}")
    except requests.exceptions.ReadTimeout:
        print("✅ Valid Key Header: Timeout (expected for SSE, means auth passed)")
    except Exception as e:
        print(f"❌ Valid Key Header: {e}")
    
    # Test 5: Header authentication (invalid)
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            headers={"Authorization": "Bearer invalid_key"},
            timeout=2
        )
        if response.status_code == 401:
            print("✅ Invalid Key Header: Correctly rejected (401)")
        else:
            print(f"❌ Invalid Key Header: Should be 401, got {response.status_code}")
    except Exception as e:
        print(f"❌ Invalid Key Header: {e}")
    
    # Test 6: No authentication (localhost bypass)
    print("\n📋 Testing Localhost Bypass")
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            timeout=2,
            stream=True
        )
        if response.status_code != 401:
            print("✅ Localhost Bypass: Working (no 401 error)")
        else:
            print("❌ Localhost Bypass: Got 401, should allow localhost")
    except requests.exceptions.ReadTimeout:
        print("✅ Localhost Bypass: Timeout (expected for SSE, means auth passed)")
    except Exception as e:
        print(f"❌ Localhost Bypass: {e}")
    
    # Test 7: Unauthorized user
    print("\n📋 Testing Unauthorized User")
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/hacker",
            params={"api_key": valid_key, "user_id": "hacker"},
            timeout=2
        )
        if response.status_code == 401:
            print("✅ Unauthorized User: Correctly rejected (401)")
        else:
            print(f"❌ Unauthorized User: Should be 401, got {response.status_code}")
    except Exception as e:
        print(f"❌ Unauthorized User: {e}")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    test_auth()