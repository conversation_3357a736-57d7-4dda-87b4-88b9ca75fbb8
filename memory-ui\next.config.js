/** @type {import('next').NextConfig} */

// Bundle analyzer for performance monitoring (conditional loading)
let withBundleAnalyzer;
try {
  withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true',
    openAnalyzer: false,
  });
} catch (error) {
  // Fallback if bundle analyzer is not installed
  console.warn('⚠️  @next/bundle-analyzer not found, bundle analysis disabled');
  withBundleAnalyzer = (config) => config;
}

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  output: 'standalone',
  
  // PERFORMANCE OPTIMIZATIONS
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  experimental: {
    // Enable standalone output for Docker
    outputFileTracingRoot: process.cwd(),
    // Bundle size optimization - removed heavy dependencies for performance
    optimizePackageImports: ['@radix-ui/react-*'],
  },
  
  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    domains: ['localhost', '*************'],
  },
  
  // Compression
  compress: true,
  poweredByHeader: false,
  
  // Webpack optimizations - simplified to prevent container restart issues
  webpack: (config, { dev, isServer }) => {
    // FIX: Basic cache optimization for webpack performance warning
    if (!dev) {
      config.cache = {
        type: 'filesystem',
        compression: 'gzip',
        maxMemoryGenerations: 1,
      };
    }

    // FIX: Optimize chunk splitting to prevent oversized chunks
    if (!isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        maxSize: 244000, // Prevent chunks larger than 244KB to fix webpack warning
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: 10,
            chunks: 'all',
            maxSize: 244000,
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true,
            maxSize: 244000,
          },
        },
      };
    }

    return config;
  },
}

module.exports = withBundleAnalyzer(nextConfig)