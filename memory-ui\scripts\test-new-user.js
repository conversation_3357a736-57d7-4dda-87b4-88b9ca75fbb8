// Test creating a new user and logging in
const { createClient } = require('@supabase/supabase-js')

const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://192.168.1.218:8000',
  SUPABASE_SERVICE_KEY
)

async function testNewUser() {
  const testEmail = '<EMAIL>'
  const testPassword = 'password123'
  
  console.log('Creating test user...')
  
  try {
    // Create user with admin API
    const { data: createData, error: createError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true
    })
    
    if (createError) {
      console.error('Create error:', createError)
      return
    }
    
    console.log('Test user created:', createData.user.id)
    
    // Now test login with regular client
    const clientSupabase = createClient(
      'http://192.168.1.218:8000',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
    )
    
    console.log('Testing login with regular client...')
    const { data: loginData, error: loginError } = await clientSupabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    })
    
    if (loginError) {
      console.error('Login error:', loginError)
    } else {
      console.log('Login successful!')
      console.log('User ID:', loginData.user.id)
    }
    
    // Cleanup
    await supabase.auth.admin.deleteUser(createData.user.id)
    console.log('Test user deleted')
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

testNewUser()