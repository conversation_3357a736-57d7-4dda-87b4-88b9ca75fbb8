import { test } from '@playwright/test';

test('Take screenshots of all pages after fixes', async ({ page }) => {
  // Dashboard
  await page.goto('http://localhost:3001');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  await page.screenshot({ 
    path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/final-01-dashboard.png',
    fullPage: true 
  });
  
  // Sync Monitor
  await page.goto('http://localhost:3001/sync');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);
  await page.screenshot({ 
    path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/final-02-sync-monitor.png',
    fullPage: true 
  });
  
  // Maintenance
  await page.goto('http://localhost:3001/maintenance');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);
  await page.screenshot({ 
    path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/final-03-maintenance.png',
    fullPage: true 
  });
  
  // Settings
  await page.goto('http://localhost:3001/settings');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);
  await page.screenshot({ 
    path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/final-04-settings.png',
    fullPage: true 
  });
  
  console.log('✓ All page screenshots captured');
});