"""
Configuration Change Listeners

This module contains listeners that respond to configuration changes
and perform necessary actions like reloading services or updating components.
"""

import logging
from typing import Dict, Any

from app.utils.config_manager import Config<PERSON>hangeEvent, ConfigChangeType
from app.utils.memory import reset_memory_client


def memory_client_config_listener(change_event: ConfigChangeEvent) -> None:
    """
    Configuration change listener for the memory client.
    
    This listener monitors changes to memory-related configuration
    and triggers memory client reinitialization when necessary.
    
    Args:
        change_event: Configuration change event data
    """
    try:
        # Check if memory-related configuration has changed
        memory_related_changes = _has_memory_config_changes(
            change_event.old_config, 
            change_event.new_config
        )
        
        if memory_related_changes:
            logging.info("Memory-related configuration changes detected, resetting memory client")
            
            # Reset the memory client to force reinitialization with new config
            reset_memory_client()
            
            logging.info("Memory client reset completed")
        else:
            logging.debug("No memory-related configuration changes detected")
            
    except Exception as e:
        logging.error(f"Error in memory client configuration listener: {e}")


def _has_memory_config_changes(old_config: Dict[str, Any], new_config: Dict[str, Any]) -> bool:
    """
    Check if there are memory-related configuration changes.
    
    Args:
        old_config: Previous configuration
        new_config: New configuration
        
    Returns:
        True if memory-related config has changed, False otherwise
    """
    # Memory-related configuration paths
    memory_config_paths = [
        'mem0.llm',
        'mem0.embedder',
        'mem0.vector_store',
        'openmemory.custom_instructions',
        'qdrant.host',
        'qdrant.port',
        'qdrant.collection_name',
        'vector_store.config.host',
        'vector_store.config.port'
    ]
    
    for path in memory_config_paths:
        old_value = _get_nested_config_value(old_config, path)
        new_value = _get_nested_config_value(new_config, path)
        
        if old_value != new_value:
            logging.debug(f"Memory config change detected at path: {path}")
            return True
    
    return False


def _get_nested_config_value(config: Dict[str, Any], path: str) -> Any:
    """Get nested configuration value using dot notation."""
    try:
        value = config
        for key in path.split('.'):
            value = value[key]
        return value
    except (KeyError, TypeError):
        return None


def logging_config_listener(change_event: ConfigChangeEvent) -> None:
    """
    Configuration change listener for logging settings.
    
    This listener monitors changes to logging configuration
    and updates the logging system accordingly.
    
    Args:
        change_event: Configuration change event data
    """
    try:
        # Check if logging configuration has changed
        old_log_level = _get_nested_config_value(change_event.old_config, 'logging.level')
        new_log_level = _get_nested_config_value(change_event.new_config, 'logging.level')
        
        if old_log_level != new_log_level and new_log_level:
            logging.info(f"Logging level changed from {old_log_level} to {new_log_level}")
            
            # Update logging level
            numeric_level = getattr(logging, new_log_level.upper(), None)
            if isinstance(numeric_level, int):
                logging.getLogger().setLevel(numeric_level)
                logging.info(f"Logging level updated to {new_log_level}")
            else:
                logging.warning(f"Invalid logging level: {new_log_level}")
        
        # Check for other logging configuration changes
        old_format = _get_nested_config_value(change_event.old_config, 'logging.format')
        new_format = _get_nested_config_value(change_event.new_config, 'logging.format')
        
        if old_format != new_format and new_format:
            logging.info("Logging format configuration changed (requires restart to take effect)")
            
    except Exception as e:
        logging.error(f"Error in logging configuration listener: {e}")


def system_config_listener(change_event: ConfigChangeEvent) -> None:
    """
    General system configuration change listener.
    
    This listener logs all configuration changes and provides
    general system-level responses to configuration updates.
    
    Args:
        change_event: Configuration change event data
    """
    try:
        logging.info(
            f"Configuration change detected: "
            f"version={change_event.config_version}, "
            f"type={change_event.change_type.value}, "
            f"requires_restart={change_event.requires_restart}"
        )
        
        if change_event.requires_restart:
            logging.warning(
                "Configuration changes require application restart to take full effect. "
                "Some changes may be applied immediately through hot-reload."
            )
        
        # Log specific changes for debugging
        if logging.getLogger().isEnabledFor(logging.DEBUG):
            _log_config_differences(change_event.old_config, change_event.new_config)
            
    except Exception as e:
        logging.error(f"Error in system configuration listener: {e}")


def _log_config_differences(old_config: Dict[str, Any], new_config: Dict[str, Any], prefix: str = "") -> None:
    """
    Log the differences between old and new configuration.
    
    Args:
        old_config: Previous configuration
        new_config: New configuration
        prefix: Key prefix for nested logging
    """
    try:
        # Find keys that exist in either config
        all_keys = set(old_config.keys()) | set(new_config.keys())
        
        for key in all_keys:
            current_prefix = f"{prefix}.{key}" if prefix else key
            
            old_value = old_config.get(key)
            new_value = new_config.get(key)
            
            if old_value != new_value:
                if isinstance(old_value, dict) and isinstance(new_value, dict):
                    # Recursively check nested dictionaries
                    _log_config_differences(old_value, new_value, current_prefix)
                else:
                    # Log the actual change (be careful with sensitive data)
                    if 'api_key' in key.lower() or 'password' in key.lower() or 'secret' in key.lower():
                        logging.debug(f"Config change: {current_prefix} = [REDACTED]")
                    else:
                        logging.debug(f"Config change: {current_prefix} = {old_value} -> {new_value}")
                        
    except Exception as e:
        logging.error(f"Error logging configuration differences: {e}")


def performance_config_listener(change_event: ConfigChangeEvent) -> None:
    """
    Configuration change listener for performance-related settings.
    
    This listener monitors changes to performance configuration
    and applies optimizations or adjustments as needed.
    
    Args:
        change_event: Configuration change event data
    """
    try:
        # Check for performance-related changes
        performance_paths = [
            'openmemory.max_text_length',
            'mem0.llm.config.max_tokens',
            'mem0.llm.config.temperature',
            'performance.cache_size',
            'performance.timeout'
        ]
        
        performance_changes = []
        for path in performance_paths:
            old_value = _get_nested_config_value(change_event.old_config, path)
            new_value = _get_nested_config_value(change_event.new_config, path)
            
            if old_value != new_value and new_value is not None:
                performance_changes.append((path, old_value, new_value))
        
        if performance_changes:
            logging.info(f"Performance configuration changes detected: {len(performance_changes)} changes")
            
            for path, old_val, new_val in performance_changes:
                logging.info(f"Performance config: {path} changed from {old_val} to {new_val}")
                
                # Apply specific performance optimizations based on the change
                if path == 'openmemory.max_text_length':
                    _handle_max_text_length_change(old_val, new_val)
                elif path.endswith('.max_tokens'):
                    _handle_max_tokens_change(old_val, new_val)
        
    except Exception as e:
        logging.error(f"Error in performance configuration listener: {e}")


def _handle_max_text_length_change(old_value: Any, new_value: Any) -> None:
    """Handle changes to max text length configuration."""
    try:
        if isinstance(new_value, int) and new_value > 5000:
            logging.warning(
                f"Max text length set to {new_value}, which may impact performance. "
                "Consider using smaller values for better response times."
            )
    except Exception as e:
        logging.error(f"Error handling max text length change: {e}")


def _handle_max_tokens_change(old_value: Any, new_value: Any) -> None:
    """Handle changes to max tokens configuration."""
    try:
        if isinstance(new_value, int) and new_value > 4000:
            logging.info(
                f"Max tokens set to {new_value}. Higher token limits may increase "
                "response time and API costs."
            )
    except Exception as e:
        logging.error(f"Error handling max tokens change: {e}")


# Default listeners that should be registered with the ConfigManager
DEFAULT_LISTENERS = [
    memory_client_config_listener,
    logging_config_listener,
    system_config_listener,
    performance_config_listener
]


def register_default_listeners(config_manager) -> None:
    """
    Register all default configuration change listeners.
    
    Args:
        config_manager: ConfigManager instance to register listeners with
    """
    for listener in DEFAULT_LISTENERS:
        config_manager.add_change_listener(listener)
        logging.info(f"Registered configuration listener: {listener.__name__}")
