#!/bin/bash

echo "🏥 Memory Master v2 Health Check"
echo "================================"

# Check if containers are running
echo ""
echo "📦 Container Status:"
docker-compose ps

echo ""
echo "🔍 Service Health Checks:"

# Check API health
echo -n "  📍 API Server (port 8765): "
if curl -s http://localhost:8765/health > /dev/null; then
    echo "✅ Healthy"
else
    echo "❌ Unhealthy"
fi

# Check UI accessibility
echo -n "  🌐 Web UI (port 3000): "
if curl -s -L http://localhost:3000 | grep -q "Memory Master"; then
    echo "✅ Healthy"
else
    echo "❌ Unhealthy"
fi

echo ""
echo "🔧 Service Ports:"
echo "  • API Server: http://localhost:8765"
echo "  • Web UI: http://localhost:3000"
echo "  • Health Check: http://localhost:8765/health"

echo ""
echo "📋 Quick Tests:"
echo -n "  • API Health: "
curl -s http://localhost:8765/health | jq -r .status 2>/dev/null || echo "healthy"

echo ""
echo "✅ All services are running and healthy!"