'use client'

import { useState } from 'react'
import { useAppSelector } from '@/store/hooks'
import Sidebar from '@/components/layout/sidebar'
import { ActivityNotifications } from '@/components/ActivityNotifications'
import { LoginForm } from '@/components/auth/LoginForm'
import { createClient } from '@/lib/supabase/client'
import { useAppDispatch, profileActions } from '@/lib/context/AppContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Menu, ChevronRight } from 'lucide-react'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const { isAuthenticated, isLoading } = useAppSelector(state => state.profile)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const dispatch = useAppDispatch()
  const router = useRouter()
  const supabase = createClient()

  const handleLogin = async (data: { email: string; password: string }) => {
    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })
      
      if (error) {
        return { error: error.message }
      }
      
      if (authData.user) {
        dispatch(profileActions.setUserFromAuth(authData.user))
        return {}
      }
      
      return { error: 'Login failed - no user returned' }
    } catch (error) {
      return { error: 'Login failed. Please try again.' }
    }
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Show login form when not authenticated
  if (!isAuthenticated) {
    return <LoginForm onSubmit={handleLogin} />
  }

  // Show main app when authenticated
  return (
    <div className="flex h-screen bg-background">
      <Sidebar isCollapsed={isCollapsed} />
      <main className="flex-1 overflow-hidden">
        <div className="relative h-full">
          {/* Collapse button in main content area */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 left-4 z-10 h-8 w-8 bg-background/80 backdrop-blur-sm border border-border/50"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <Menu className="h-4 w-4" />
            )}
          </Button>
          
          <div className="h-full overflow-y-auto">
            {children}
          </div>
        </div>
      </main>
      <ActivityNotifications />
    </div>
  )
}