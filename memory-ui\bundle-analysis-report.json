{"timestamp": "2025-07-10T11:39:00.811Z", "metrics": {"moduleCount": 0, "compileTime": 0, "bundleSize": 274}, "dependencies": {"totalDeps": 23, "totalDevDeps": 13, "heavyDeps": [], "radixComponents": 7}, "rawOutput": "\n> memory-ui@0.1.0 build\n> next build\n\n   ▲ Next.js 14.0.4\n   - Environments: .env.local\n   - Experiments (use at your own risk):\n     · outputFileTracingRoot\n\n   Creating an optimized production build ...\n\nwarn - The class `duration-[2000ms]` is ambiguous and matches multiple utilities.\nwarn - If this is content and not a class, replace it with `duration-&lsqb;2000ms&rsqb;` to silence this warning.\n ✓ Compiled successfully\n   Linting and checking validity of types ...\n   Collecting page data ...\n   Generating static pages (0/7) ...\n\r   Generating static pages (1/7) \r\n\r   Generating static pages (3/7) \r\n\r   Generating static pages (5/7) \r\n\r ✓ Generating static pages (7/7) \n   Finalizing page optimization ...\n   Collecting build traces ...\n\nRoute (app)                                      Size     First Load JS\n┌ ○ /                                            6.76 kB         286 kB\n├ ○ /_not-found                                  223 B           275 kB\n├ ○ /login                                       473 B           279 kB\n├ ○ /maintenance                                 3.82 kB         283 kB\n└ ○ /settings                                    2.5 kB          281 kB\n+ First Load JS shared by all                    274 kB\n  ├ chunks/main-app-f63c491fcfb8ba87.js          254 B\n  ├ chunks/vendors-0582c947-9bbbc901fe8ab733.js  17.9 kB\n  ├ chunks/vendors-2898f16f-15dd196a6042a08f.js  15.8 kB\n  ├ chunks/vendors-2f6dde1d-49b54e497f4188bb.js  10.8 kB\n  ├ chunks/vendors-3a166c33-d523c64d80c6bdc6.js  16.7 kB\n  ├ chunks/vendors-4a7382ad-ce8f74067dbc11c0.js  17.6 kB\n  ├ chunks/vendors-4aa88247-0b661b0764b179ff.js  11 kB\n  ├ chunks/vendors-55776fae-37bd8b66dc99dc1d.js  15.7 kB\n  ├ chunks/vendors-78a34c87-f56597ea190b8320.js  14.4 kB\n  ├ chunks/vendors-b49fab05-234efe96570904e5.js  3.35 kB\n  ├ chunks/vendors-c0d76f48-47068f3b553aa240.js  11 kB\n  ├ chunks/vendors-c3a08eae-17b70cff6dadadea.js  70.7 kB\n  ├ chunks/vendors-c4d2e9b7-680de5da7747fdf9.js  6.93 kB\n  ├ chunks/vendors-d031d8a3-e8a7a03862a7dc60.js  13.7 kB\n  ├ chunks/vendors-d91c2bd6-f33cb876c9c4189f.js  5.37 kB\n  ├ chunks/vendors-f72b7d4b-11a4a7ad95247f57.js  41.3 kB\n  └ chunks/webpack-c0e61997167bff5d.js           1.77 kB\n\n\nƒ Middleware                                     113 kB\n\n○  (Static)  prerendered as static content\n\n"}