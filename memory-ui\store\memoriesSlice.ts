import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Memory {
  id: string;
  user_id: string;
  title: string;
  content: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface MemoriesState {
  memories: Memory[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: {
    search: string;
    category: string;
    dateRange: {
      start: string | null;
      end: string | null;
    };
  };
}

const initialState: MemoriesState = {
  memories: [],
  loading: false,
  error: null,
  totalCount: 0,
  currentPage: 1,
  pageSize: 10,
  filters: {
    search: '',
    category: 'all',
    dateRange: {
      start: null,
      end: null
    }
  }
};

const memoriesSlice = createSlice({
  name: 'memories',
  initialState,
  reducers: {
    setMemories: (state, action: PayloadAction<{ memories: Memory[], totalCount: number }>) => {
      state.memories = action.payload.memories;
      state.totalCount = action.payload.totalCount;
      state.loading = false;
      state.error = null;
    },
    addMemory: (state, action: PayloadAction<Memory>) => {
      state.memories.unshift(action.payload);
      state.totalCount += 1;
    },
    updateMemory: (state, action: PayloadAction<Memory>) => {
      const index = state.memories.findIndex(m => m.id === action.payload.id);
      if (index !== -1) {
        state.memories[index] = action.payload;
      }
    },
    removeMemory: (state, action: PayloadAction<string>) => {
      state.memories = state.memories.filter(m => m.id !== action.payload);
      state.totalCount -= 1;
    },
    addMemoryFromRealtime: (state, action: PayloadAction<Memory>) => {
      // Check if memory already exists to avoid duplicates
      const exists = state.memories.some(m => m.id === action.payload.id);
      if (!exists) {
        state.memories.unshift(action.payload);
        state.totalCount += 1;
      }
    },
    updateMemoryFromRealtime: (state, action: PayloadAction<Memory>) => {
      const index = state.memories.findIndex(m => m.id === action.payload.id);
      if (index !== -1) {
        state.memories[index] = action.payload;
      }
    },
    removeMemoryFromRealtime: (state, action: PayloadAction<string>) => {
      const initialLength = state.memories.length;
      state.memories = state.memories.filter(m => m.id !== action.payload);
      if (state.memories.length < initialLength) {
        state.totalCount -= 1;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },
    setFilters: (state, action: PayloadAction<Partial<MemoriesState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.currentPage = 1; // Reset to first page
    }
  }
});

export const {
  setMemories,
  addMemory,
  updateMemory,
  removeMemory,
  addMemoryFromRealtime,
  updateMemoryFromRealtime,
  removeMemoryFromRealtime,
  setLoading,
  setError,
  setFilters,
  setPage,
  setPageSize
} = memoriesSlice.actions;

export default memoriesSlice.reducer;