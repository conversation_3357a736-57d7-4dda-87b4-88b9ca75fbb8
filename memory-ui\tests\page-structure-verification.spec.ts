import { test, expect } from '@playwright/test';

test.describe('Page Structure Verification', () => {
  
  test('should verify actual page structure and elements', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take a screenshot to see the actual page
    await page.screenshot({ 
      path: 'test-results/screenshots/page-structure-verification.png',
      fullPage: true 
    });
    
    // Check what elements actually exist on the page
    console.log('=== PAGE TITLE ===');
    const title = await page.title();
    console.log('Page title:', title);
    
    console.log('=== HEADINGS ===');
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    for (let i = 0; i < headings.length; i++) {
      const text = await headings[i].textContent();
      const tagName = await headings[i].evaluate(el => el.tagName);
      console.log(`${tagName}: "${text}"`);
    }
    
    console.log('=== MAIN CONTENT ===');
    const mainContent = page.locator('main, [role="main"]');
    if (await mainContent.isVisible()) {
      const mainText = await mainContent.textContent();
      console.log('Main content found:', mainText?.substring(0, 200) + '...');
    } else {
      console.log('No main content found');
    }
    
    console.log('=== NAVIGATION ===');
    const navItems = await page.locator('nav a, [role="navigation"] a').all();
    for (let i = 0; i < navItems.length; i++) {
      const text = await navItems[i].textContent();
      const href = await navItems[i].getAttribute('href');
      console.log(`Nav item: "${text}" -> ${href}`);
    }
    
    console.log('=== BUTTONS ===');
    const buttons = await page.locator('button').all();
    for (let i = 0; i < Math.min(buttons.length, 10); i++) {
      const text = await buttons[i].textContent();
      const visible = await buttons[i].isVisible();
      console.log(`Button: "${text}" (visible: ${visible})`);
    }
    
    console.log('=== USER ELEMENTS ===');
    const userElements = await page.locator('text="User", text="Guest", text="ID:"').all();
    for (let i = 0; i < userElements.length; i++) {
      const text = await userElements[i].textContent();
      console.log(`User element: "${text}"`);
    }
    
    console.log('=== MEMORY ELEMENTS ===');
    const memoryElements = await page.locator('text="Memory", text="Total"').all();
    for (let i = 0; i < memoryElements.length; i++) {
      const text = await memoryElements[i].textContent();
      console.log(`Memory element: "${text}"`);
    }
    
    // Check for common UI patterns
    console.log('=== COMMON UI PATTERNS ===');
    const patterns = [
      'Welcome',
      'Dashboard',
      'Memories',
      'Settings',
      'Create',
      'Login',
      'Sign in',
      'Profile',
      'Health',
      'Status'
    ];
    
    for (const pattern of patterns) {
      const elements = await page.locator(`text="${pattern}"`).all();
      if (elements.length > 0) {
        console.log(`Pattern "${pattern}" found: ${elements.length} instances`);
      }
    }
    
    // Check current URL
    console.log('=== URL INFO ===');
    console.log('Current URL:', page.url());
    
    // Check if there are any error messages
    console.log('=== ERROR MESSAGES ===');
    const errorPatterns = ['Error', 'Failed', 'Not found', '404', '500'];
    for (const pattern of errorPatterns) {
      const errorElements = await page.locator(`text="${pattern}"`).all();
      if (errorElements.length > 0) {
        console.log(`Error pattern "${pattern}" found: ${errorElements.length} instances`);
      }
    }
    
    // Basic verification - page should at least load
    await expect(page).toHaveTitle(/Memory Master/);
    console.log('✓ Page loaded successfully with Memory Master title');
    
    // Check if page has any content
    const bodyText = await page.locator('body').textContent();
    expect(bodyText?.length).toBeGreaterThan(0);
    console.log('✓ Page has content');
    
    console.log(`✓ Page structure verification completed. Body text length: ${bodyText?.length}`);
  });
});