#!/bin/bash

# Test script for Memory Master deployment
# This script performs basic validation of the deployment setup

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

success() { echo -e "${GREEN}✅ $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
info() { echo -e "${CYAN}ℹ️  $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

ENVIRONMENT=${1:-dev}

info "Testing Memory Master deployment configuration for $ENVIRONMENT environment"
echo ""

# Test 1: Check Docker and Docker Compose
info "Test 1: Checking Docker prerequisites..."
if ! command -v docker &> /dev/null; then
    error "Docker not found"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    error "Docker Compose not found"
    exit 1
fi

if ! docker info &> /dev/null; then
    error "Docker daemon not running"
    exit 1
fi

success "Docker and <PERSON>er Compose are available"

# Test 2: Validate Docker Compose files
info "Test 2: Validating Docker Compose configuration..."
if docker compose -f docker-compose.$ENVIRONMENT.yml config > /dev/null 2>&1; then
    success "Docker Compose configuration is valid"
else
    error "Docker Compose configuration has errors"
    docker compose -f docker-compose.$ENVIRONMENT.yml config
    exit 1
fi

# Test 3: Check Dockerfiles
info "Test 3: Checking Dockerfiles..."
if [[ "$ENVIRONMENT" == "prod" ]]; then
    if [[ ! -f "api/Dockerfile.prod" ]]; then
        error "Production API Dockerfile not found"
        exit 1
    fi
    if [[ ! -f "memory-ui/Dockerfile.prod" ]]; then
        error "Production UI Dockerfile not found"
        exit 1
    fi
    success "Production Dockerfiles found"
else
    if [[ ! -f "api/Dockerfile" ]]; then
        error "Development API Dockerfile not found"
        exit 1
    fi
    if [[ ! -f "memory-ui/Dockerfile" ]]; then
        error "Development UI Dockerfile not found"
        exit 1
    fi
    success "Development Dockerfiles found"
fi

# Test 4: Check environment configuration
info "Test 4: Checking environment configuration..."
if [[ "$ENVIRONMENT" == "prod" ]]; then
    if [[ ! -f ".env.production" ]]; then
        warning "Production environment file not found"
        info "Please copy .env.production.template to .env.production and configure it"
    else
        success "Production environment file found"
    fi
else
    if [[ ! -f "api/.env" ]]; then
        error "Development API environment file not found"
        exit 1
    fi
    if [[ ! -f "memory-ui/.env.local" ]]; then
        error "Development UI environment file not found"
        exit 1
    fi
    success "Development environment files found"
fi

# Test 5: Check deployment scripts
info "Test 5: Checking deployment scripts..."
if [[ ! -f "deploy-ubuntu.sh" ]]; then
    error "Ubuntu deployment script not found"
    exit 1
fi

if [[ ! -x "deploy-ubuntu.sh" ]]; then
    error "Ubuntu deployment script not executable"
    exit 1
fi

if [[ ! -f "deploy-windows.ps1" ]]; then
    error "Windows deployment script not found"
    exit 1
fi

success "Deployment scripts found"

# Test 6: Test Docker build (dry run)
info "Test 6: Testing Docker build process..."
info "Building API image..."
if docker build -t memory-api-test api/ -f api/Dockerfile$([ "$ENVIRONMENT" = "prod" ] && echo ".prod" || echo "") > /dev/null 2>&1; then
    success "API image builds successfully"
    docker rmi memory-api-test > /dev/null 2>&1 || true
else
    error "API image build failed"
    exit 1
fi

info "Building UI image..."
if docker build -t memory-ui-test memory-ui/ -f memory-ui/Dockerfile$([ "$ENVIRONMENT" = "prod" ] && echo ".prod" || echo "") > /dev/null 2>&1; then
    success "UI image builds successfully"
    docker rmi memory-ui-test > /dev/null 2>&1 || true
else
    error "UI image build failed"
    exit 1
fi

# Test 7: Check network ports
info "Test 7: Checking port availability..."
PORTS=("3210" "8765" "6333")
for port in "${PORTS[@]}"; do
    if lsof -i:$port > /dev/null 2>&1; then
        warning "Port $port is already in use"
    else
        success "Port $port is available"
    fi
done

# Test 8: Check required dependencies in package files
info "Test 8: Checking package dependencies..."
if [[ -f "memory-ui/package.json" ]]; then
    if grep -q "next" memory-ui/package.json; then
        success "Next.js found in UI dependencies"
    else
        error "Next.js not found in UI dependencies"
        exit 1
    fi
fi

if [[ -f "api/requirements.txt" ]]; then
    if grep -q "fastapi\|uvicorn" api/requirements.txt; then
        success "FastAPI/Uvicorn found in API dependencies"
    else
        error "FastAPI/Uvicorn not found in API dependencies"
        exit 1
    fi
fi

echo ""
success "All deployment tests passed! ✨"
echo ""
info "Next steps:"
echo "  1. Run deployment: ./deploy-ubuntu.sh -e $ENVIRONMENT"
echo "  2. Check services: docker compose -f docker-compose.$ENVIRONMENT.yml ps"
echo "  3. View logs: docker compose -f docker-compose.$ENVIRONMENT.yml logs -f"
echo "  4. Stop services: docker compose -f docker-compose.$ENVIRONMENT.yml down"
echo ""
success "Ready for deployment! 🚀"