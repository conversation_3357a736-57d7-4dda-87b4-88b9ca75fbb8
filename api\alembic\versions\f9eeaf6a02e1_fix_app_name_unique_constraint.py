"""fix_app_name_unique_constraint

Revision ID: f9eeaf6a02e1
Revises: clean_memory_master
Create Date: 2025-06-29 10:36:46.785814

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f9eeaf6a02e1'
down_revision: Union[str, None] = 'clean_memory_master'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the existing unique constraint on name
    op.drop_index('ix_memory_master_apps_name', table_name='apps', schema='memory_master')

    # Create a new composite unique constraint on (owner_id, name)
    op.create_unique_constraint(
        'uq_app_owner_name',
        'apps',
        ['owner_id', 'name'],
        schema='memory_master'
    )

    # Recreate the index on name (non-unique)
    op.create_index('ix_memory_master_apps_name', 'apps', ['name'], schema='memory_master')


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the composite unique constraint
    op.drop_constraint('uq_app_owner_name', 'apps', schema='memory_master')

    # Drop the non-unique index
    op.drop_index('ix_memory_master_apps_name', table_name='apps', schema='memory_master')

    # Recreate the original unique index on name
    op.create_index('ix_memory_master_apps_name', 'apps', ['name'], unique=True, schema='memory_master')
