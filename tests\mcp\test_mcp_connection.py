#!/usr/bin/env python3
"""
Test MCP Connection Speed
"""
import time
import requests
import subprocess
import json
import os

def test_mcp_connection():
    """Test MCP connection speed"""
    print("🧪 Testing MCP Connection Speed")
    print("=" * 50)
    
    # Configuration
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
    base_url = "http://localhost:8765"
    
    # Test 1: Basic health check
    print("\n1. Basic Health Check")
    start_time = time.time()
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        elapsed = time.time() - start_time
        print(f"   ✅ Health check: {response.status_code} in {elapsed:.2f}s")
    except Exception as e:
        print(f"   ❌ Health check failed: {e}")
        return False
    
    # Test 2: MCP SSE endpoint
    print("\n2. MCP SSE Endpoint Test")
    start_time = time.time()
    try:
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            params={"api_key": api_key, "user_id": "aungheinaye"},
            timeout=10,
            stream=True
        )
        elapsed = time.time() - start_time
        print(f"   ✅ SSE endpoint: {response.status_code} in {elapsed:.2f}s")
        
        # Check if we get SSE data
        if response.status_code == 200:
            try:
                # Read first few lines
                lines = []
                for line in response.iter_lines(decode_unicode=True):
                    lines.append(line)
                    if len(lines) >= 3:
                        break
                
                print(f"   📡 SSE data received: {len(lines)} lines")
                for i, line in enumerate(lines[:2]):
                    print(f"      Line {i+1}: {line[:50]}...")
                    
            except Exception as e:
                print(f"   ⚠️  SSE data reading issue: {e}")
                
    except requests.exceptions.ReadTimeout:
        elapsed = time.time() - start_time
        print(f"   ⚠️  SSE endpoint timeout after {elapsed:.2f}s (expected for SSE)")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ SSE endpoint failed in {elapsed:.2f}s: {e}")
        return False
    
    # Test 3: Test supergateway connection
    print("\n3. Testing Supergateway Connection")
    print("   📋 Configuration:")
    print(f"      URL: {base_url}/mcp/claude/sse/aungheinaye?api_key=[REDACTED]")
    print("      User: aungheinaye")
    
    # Test 4: Quick connection test (simulating Claude Desktop)
    print("\n4. Quick Connection Test")
    start_time = time.time()
    try:
        # This simulates what supergateway does
        response = requests.get(
            f"{base_url}/mcp/claude/sse/aungheinaye",
            params={"api_key": api_key, "user_id": "aungheinaye"},
            timeout=5
        )
        elapsed = time.time() - start_time
        print(f"   ✅ Quick connection: {response.status_code} in {elapsed:.2f}s")
        
        if elapsed < 5:
            print("   🎉 Connection is fast enough for MCP!")
            return True
        else:
            print("   ⚠️  Connection is slow, may timeout")
            return False
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"   ❌ Quick connection failed in {elapsed:.2f}s: {e}")
        return False

def print_mcp_config():
    """Print MCP configuration"""
    print("\n" + "=" * 50)
    print("📋 MCP Configuration for Claude Desktop")
    print("=" * 50)
    
    config = {
        "memory-mcp": {
            "command": "npx",
            "args": [
                "-y",
                "supergateway@latest",
                "--sse",
                "http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
            ],
            "disabled": False
        }
    }
    
    print(json.dumps(config, indent=2))

if __name__ == "__main__":
    success = test_mcp_connection()
    print_mcp_config()
    
    if success:
        print("\n🎉 MCP server is ready for Claude Desktop!")
        print("   Make sure to set 'disabled': false in your MCP configuration")
    else:
        print("\n❌ MCP server needs more troubleshooting")