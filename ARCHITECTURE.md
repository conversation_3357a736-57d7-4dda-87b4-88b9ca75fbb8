# Memory Master v2 Architecture Documentation

## Overview

Memory Master v2 is a sophisticated personal memory management system built on modern microservices architecture. This document provides a comprehensive overview of the system architecture, design patterns, data flow, and technical implementation details.

## System Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                Memory Master v2                                 │
│                            Microservices Architecture                           │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Claude Desktop│    │   VS Code       │    │   Web Browser   │
│   MCP Client    │    │   MCP Client    │    │   Dashboard     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Load Balancer        │
                    │    (Docker Compose)      │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API Gateway         │
                    │     (FastAPI + MCP)      │
                    └─────────────┬─────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                           │                           │
    ▼                           ▼                           ▼
┌───────────────┐    ┌───────────────┐    ┌───────────────┐
│  Memory Core  │    │  Evolution    │    │  Sync Engine  │
│   Service     │    │ Intelligence  │    │   Service     │
└───────┬───────┘    └───────┬───────┘    └───────┬───────┘
        │                    │                    │
        │                    │                    │
        └────────────────────┼────────────────────┘
                            │
        ┌───────────────────┼───────────────────┐
        │                   │                   │
        ▼                   ▼                   ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│  Supabase       │ │     Qdrant      │ │     MinIO       │
│  PostgreSQL     │ │  Vector Store   │ │ Object Storage  │
│ 192.168.1.218   │ │ 192.168.1.218   │ │ 192.168.1.177   │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

### Component Overview

#### Frontend Layer
- **Next.js 15 Web Application**: Modern React-based dashboard
- **MCP Clients**: Claude Desktop, VS Code, and other MCP-enabled applications
- **Real-time Communication**: WebSockets and SSE for live updates

#### API Layer
- **FastAPI Application**: High-performance Python web framework
- **MCP Server**: Model Context Protocol implementation
- **Authentication Layer**: Supabase Auth with fallback support
- **Rate Limiting**: Request throttling and abuse prevention

#### Service Layer
- **Memory Service**: Core memory operations and management
- **Evolution Service**: AI-powered memory optimization
- **Sync Service**: Bi-directional data synchronization
- **Health Service**: System monitoring and diagnostics

#### Data Layer
- **PostgreSQL**: Relational data storage (Supabase)
- **Qdrant**: Vector database for semantic search
- **MinIO**: Object storage for backups and assets
- **Redis**: Caching and session management (optional)

## Detailed Architecture

### 1. Backend Architecture (FastAPI)

#### Application Structure
```
api/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py               # Configuration management
│   ├── models.py               # SQLAlchemy database models
│   ├── schemas.py              # Pydantic request/response schemas
│   ├── database/               # Database layer
│   │   ├── __init__.py
│   │   ├── base.py            # Database connection and session
│   │   └── service.py         # Database service layer
│   ├── routers/               # API route handlers
│   │   ├── __init__.py
│   │   ├── memories.py        # Memory CRUD operations
│   │   ├── memories_v2.py     # Enhanced memory API
│   │   ├── auth.py            # Authentication endpoints
│   │   ├── config.py          # Configuration endpoints
│   │   ├── evolution_config.py # Evolution configuration
│   │   ├── sync.py            # Synchronization endpoints
│   │   ├── apps.py            # Application management
│   │   ├── health.py          # Health monitoring
│   │   └── stats.py           # Statistics endpoints
│   ├── services/              # Business logic layer
│   │   ├── __init__.py
│   │   ├── evolution_service.py      # Evolution intelligence
│   │   └── evolution_config_service.py # Evolution configuration
│   ├── utils/                 # Utility functions
│   │   ├── __init__.py
│   │   ├── memory.py          # Memory operations
│   │   ├── async_memory.py    # Async memory management
│   │   ├── categorization.py  # Category inference
│   │   ├── permissions.py     # Access control
│   │   ├── config_manager.py  # Configuration management
│   │   └── evolution_prompts.py # Evolution prompts
│   ├── auth/                  # Authentication layer
│   │   ├── __init__.py
│   │   ├── middleware.py      # Auth middleware
│   │   ├── supabase.py        # Supabase integration
│   │   └── mcp_auth.py        # MCP authentication
│   ├── memory_service.py      # Core memory service
│   ├── health_service.py      # Health monitoring service
│   ├── mcp_server.py          # MCP protocol implementation
│   └── mcp_models.py          # MCP data models
├── alembic/                   # Database migrations
├── tests/                     # Test suite
├── requirements.txt           # Python dependencies
└── Dockerfile                # Container configuration
```

#### Design Patterns

##### 1. Layered Architecture Pattern
```python
# Presentation Layer (Routers)
@router.get("/memories/")
async def list_memories(
    db: Session = Depends(get_db),
    user: AuthenticatedUser = Depends(get_current_user)
):
    # Delegate to service layer
    return await memory_service.list_memories(db, user.id)

# Service Layer (Business Logic)
class MemoryService:
    async def list_memories(self, db: Session, user_id: str):
        # Business logic and validation
        memories = await self.repository.get_user_memories(user_id)
        return self.transform_memories(memories)

# Repository Layer (Data Access)
class MemoryRepository:
    def get_user_memories(self, user_id: str):
        return db.query(Memory).filter(Memory.user_id == user_id).all()
```

##### 2. Dependency Injection Pattern
```python
# Service dependencies
class MemoryService:
    def __init__(
        self,
        repository: MemoryRepository,
        evolution_service: EvolutionService,
        config_manager: ConfigManager
    ):
        self.repository = repository
        self.evolution_service = evolution_service
        self.config_manager = config_manager

# FastAPI dependency injection
def get_memory_service(
    repository: MemoryRepository = Depends(get_memory_repository),
    evolution_service: EvolutionService = Depends(get_evolution_service)
) -> MemoryService:
    return MemoryService(repository, evolution_service)
```

##### 3. Strategy Pattern for Authentication
```python
class AuthenticationStrategy:
    def authenticate(self, request: Request) -> User:
        raise NotImplementedError

class SupabaseAuthStrategy(AuthenticationStrategy):
    def authenticate(self, request: Request) -> User:
        # Supabase JWT validation
        pass

class DefaultUserStrategy(AuthenticationStrategy):
    def authenticate(self, request: Request) -> User:
        # Default user fallback
        pass

class AuthenticationContext:
    def __init__(self, strategy: AuthenticationStrategy):
        self.strategy = strategy
    
    def authenticate(self, request: Request) -> User:
        return self.strategy.authenticate(request)
```

### 2. Frontend Architecture (Next.js)

#### Application Structure
```
memory-ui/
├── app/                       # Next.js 13+ app directory
│   ├── layout.tsx            # Root layout with providers
│   ├── page.tsx              # Main dashboard page
│   ├── login/                # Authentication pages
│   │   └── page.tsx
│   ├── settings/             # Settings pages
│   │   └── page.tsx
│   └── providers.tsx         # Context providers
├── components/               # React components
│   ├── ui/                   # Shadcn/UI components
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── dialog.tsx
│   │   └── ...
│   ├── auth/                 # Authentication components
│   │   └── LoginForm.tsx
│   ├── layout/               # Layout components
│   │   └── sidebar.tsx
│   ├── UserSwitcher.tsx      # User switching component
│   └── ActivityNotifications.tsx # Real-time notifications
├── hooks/                    # Custom React hooks
│   ├── useMemoriesApi.ts     # Memory operations
│   ├── useEvolutionRealtime.ts # Evolution updates
│   └── usePresence.ts        # User presence
├── lib/                      # Utility libraries
│   ├── api.ts               # API client
│   ├── utils.ts             # Utility functions
│   ├── auth/                # Authentication utilities
│   │   ├── AuthProvider.tsx
│   │   └── userMapping.ts
│   └── supabase/            # Supabase client
│       ├── client.ts
│       └── server.ts
├── store/                   # Redux store
│   ├── store.ts            # Store configuration
│   ├── memoriesSlice.ts    # Memory state management
│   ├── evolutionSlice.ts   # Evolution state
│   └── profileSlice.ts     # User profile state
├── styles/                 # Styling
│   └── globals.css
├── tests/                  # E2E tests
└── package.json           # Dependencies
```

#### State Management Architecture

##### Redux Toolkit Implementation
```typescript
// Store configuration
export const store = configureStore({
  reducer: {
    memories: memoriesSlice.reducer,
    evolution: evolutionSlice.reducer,
    auth: authSlice.reducer,
    ui: uiSlice.reducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

// Memory slice
const memoriesSlice = createSlice({
  name: 'memories',
  initialState: {
    items: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      size: 10,
      total: 0
    }
  },
  reducers: {
    setMemories: (state, action) => {
      state.items = action.payload.items;
      state.pagination = action.payload.pagination;
    },
    addMemory: (state, action) => {
      state.items.unshift(action.payload);
    },
    updateMemory: (state, action) => {
      const index = state.items.findIndex(m => m.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    }
  }
});
```

##### Custom Hooks Pattern
```typescript
// useMemoriesApi hook
export const useMemoriesApi = () => {
  const dispatch = useDispatch();
  const { items, loading, error } = useSelector(state => state.memories);

  const fetchMemories = useCallback(async (filters: FilterOptions) => {
    dispatch(setLoading(true));
    try {
      const response = await api.get('/memories', { params: filters });
      dispatch(setMemories(response.data));
    } catch (error) {
      dispatch(setError(error.message));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  const createMemory = useCallback(async (memoryData: CreateMemoryRequest) => {
    const response = await api.post('/memories', memoryData);
    dispatch(addMemory(response.data));
    return response.data;
  }, [dispatch]);

  return {
    memories: items,
    loading,
    error,
    fetchMemories,
    createMemory
  };
};
```

### 3. Database Architecture

#### Entity Relationship Diagram
```
Users (1) ──────────────────────────────────────────────────────────────┐
  │                                                                      │
  │ id (UUID, PK)                                                       │
  │ user_id (String, Unique)                                            │
  │ name (String)                                                       │
  │ email (String, Unique)                                              │
  │ created_at (DateTime)                                               │
  │ supabase_user_id (UUID)                                             │
  │                                                                      │
  └─────────────────────────────────────────────────────────────────────┘
              │                                    │
              │ 1:N                               │ 1:N
              ▼                                    ▼
┌──────────────────────────────────┐    ┌──────────────────────────────────┐
│             Apps                 │    │           Memories               │
│                                  │    │                                  │
│ id (UUID, PK)                   │    │ id (UUID, PK)                   │
│ name (String)                   │    │ text (Text)                     │
│ owner_id (UUID, FK → Users)     │    │ metadata (JSON)                 │
│ is_active (Boolean)             │    │ user_id (UUID, FK → Users)      │
│ created_at (DateTime)           │    │ app_id (UUID, FK → Apps)        │
│ updated_at (DateTime)           │    │ state (Enum)                    │
│                                  │    │ created_at (DateTime)           │
└──────────────────────────────────┘    │ updated_at (DateTime)           │
              │                          │ archived_at (DateTime)          │
              │ 1:N                     │ deleted_at (DateTime)           │
              └─────────────────────────▶│                                  │
                                        └──────────────────────────────────┘
                                                  │
                                                  │ 1:N
                                                  ▼
                                        ┌──────────────────────────────────┐
                                        │        Categories                │
                                        │                                  │
                                        │ id (UUID, PK)                   │
                                        │ name (String)                   │
                                        │ color (String)                  │
                                        │ created_at (DateTime)           │
                                        └──────────────────────────────────┘
                                                  │
                                                  │ N:M
                                                  ▼
                                        ┌──────────────────────────────────┐
                                        │      memory_categories           │
                                        │                                  │
                                        │ memory_id (UUID, FK)            │
                                        │ category_id (UUID, FK)          │
                                        └──────────────────────────────────┘
```

#### Database Schema Details

##### Users Table
```sql
CREATE TABLE memory_master.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    supabase_user_id UUID UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_sign_in_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_users_user_id ON memory_master.users(user_id);
CREATE INDEX idx_users_email ON memory_master.users(email);
CREATE INDEX idx_users_created_at ON memory_master.users(created_at);
```

##### Memories Table
```sql
CREATE TYPE memory_state AS ENUM ('active', 'paused', 'archived', 'deleted');

CREATE TABLE memory_master.memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    text TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    user_id UUID NOT NULL REFERENCES memory_master.users(id),
    app_id UUID NOT NULL REFERENCES memory_master.apps(id),
    state memory_state DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_memories_user_id ON memory_master.memories(user_id);
CREATE INDEX idx_memories_app_id ON memory_master.memories(app_id);
CREATE INDEX idx_memories_state ON memory_master.memories(state);
CREATE INDEX idx_memories_created_at ON memory_master.memories(created_at);
CREATE INDEX idx_memories_text_fts ON memory_master.memories 
    USING GIN (to_tsvector('english', text));
```

##### Evolution Intelligence Tables
```sql
CREATE TABLE memory_master.evolution_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES memory_master.users(id),
    memory_id UUID REFERENCES memory_master.memories(id),
    operation_type VARCHAR(50) NOT NULL,
    confidence_score DECIMAL(3,2),
    reasoning TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE memory_master.evolution_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES memory_master.users(id),
    app_id UUID REFERENCES memory_master.apps(id),
    domain_type VARCHAR(50) NOT NULL,
    custom_prompts JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Vector Database Architecture (Qdrant)

#### Collection Structure
```json
{
  "collection_name": "memories_{user_id}_{app_name}",
  "vectors": {
    "size": 1536,
    "distance": "Cosine"
  },
  "payload_schema": {
    "memory_id": "keyword",
    "user_id": "keyword",
    "app_id": "keyword",
    "text": "text",
    "metadata": "json",
    "categories": "keyword",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
}
```

#### Vector Operations
```python
# Vector insertion
async def add_memory_vector(
    collection_name: str,
    memory_id: str,
    text: str,
    metadata: dict
):
    embedding = await generate_embedding(text)
    point = {
        "id": memory_id,
        "vector": embedding,
        "payload": {
            "memory_id": memory_id,
            "text": text,
            "metadata": metadata,
            "created_at": datetime.utcnow().isoformat()
        }
    }
    
    await qdrant_client.upsert(
        collection_name=collection_name,
        points=[point]
    )

# Vector search
async def search_memories(
    collection_name: str,
    query: str,
    limit: int = 10
):
    query_embedding = await generate_embedding(query)
    
    search_result = await qdrant_client.search(
        collection_name=collection_name,
        query_vector=query_embedding,
        limit=limit,
        score_threshold=0.7
    )
    
    return search_result
```

### 5. MCP Protocol Implementation

#### MCP Server Architecture
```python
# FastMCP server setup
mcp_server = FastMCP("Memory Master v2")

@mcp_server.tool()
async def add_memories(text: str) -> str:
    """Add new memories with automatic chunking"""
    try:
        # Get user context
        user_id = await get_current_user_id()
        
        # Process memory through service layer
        memory_service = get_memory_service()
        result = await memory_service.add_memory(
            user_id=user_id,
            text=text,
            infer_categories=True
        )
        
        return json.dumps({
            "success": True,
            "memory_id": result.id,
            "message": "Memory added successfully"
        })
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        })

@mcp_server.tool()
async def search_memory(query: str) -> str:
    """Search memories using semantic search"""
    try:
        user_id = await get_current_user_id()
        memory_service = get_memory_service()
        
        results = await memory_service.search_memories(
            user_id=user_id,
            query=query,
            limit=10
        )
        
        return json.dumps({
            "success": True,
            "results": [
                {
                    "id": memory.id,
                    "text": memory.text,
                    "score": memory.score,
                    "created_at": memory.created_at.isoformat()
                }
                for memory in results
            ]
        })
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        })
```

#### SSE Connection Management
```python
# Server-Sent Events for real-time communication
@router.get("/mcp/claude/sse/{user_id}")
async def claude_sse_endpoint(
    user_id: str,
    request: Request
):
    """SSE endpoint for Claude Desktop integration"""
    
    # Authenticate user
    user = await authenticate_user(user_id)
    
    # Create SSE transport
    transport = SseServerTransport(
        "/mcp/claude/sse",
        request
    )
    
    # Create MCP session
    session = ServerSession(mcp_server, transport)
    
    # Initialize memory client for user
    await ensure_memory_client_initialized(user_id)
    
    # Run session
    async with session:
        await session.run()
```

### 6. Evolution Intelligence System

#### Evolution Service Architecture
```python
class EvolutionService:
    def __init__(
        self,
        llm_client: LLMClient,
        config_service: EvolutionConfigService,
        memory_repository: MemoryRepository
    ):
        self.llm_client = llm_client
        self.config_service = config_service
        self.memory_repository = memory_repository
    
    async def analyze_memory_operation(
        self,
        user_id: str,
        operation_data: dict
    ) -> EvolutionAnalysis:
        """Analyze memory operation for evolution insights"""
        
        # Get user's evolution configuration
        config = await self.config_service.get_active_config(user_id)
        
        # Extract operation details
        operation_type = operation_data.get('operation_type')
        confidence_score = operation_data.get('confidence_score', 0.0)
        
        # Analyze using configured prompts
        analysis = await self.llm_client.analyze(
            prompt=config.get_analysis_prompt(),
            data=operation_data
        )
        
        # Store evolution record
        evolution_record = EvolutionOperation(
            user_id=user_id,
            operation_type=operation_type,
            confidence_score=confidence_score,
            reasoning=analysis.reasoning,
            metadata=analysis.metadata
        )
        
        await self.memory_repository.save_evolution_record(evolution_record)
        
        return EvolutionAnalysis(
            learning_efficiency=analysis.learning_efficiency,
            optimization_suggestions=analysis.suggestions,
            quality_metrics=analysis.quality_metrics
        )
```

#### Evolution Configuration System
```python
class EvolutionConfigService:
    def __init__(self, repository: EvolutionRepository):
        self.repository = repository
    
    async def get_domain_configuration(
        self,
        user_id: str,
        domain_type: str
    ) -> EvolutionConfiguration:
        """Get domain-specific evolution configuration"""
        
        # Default configurations by domain
        default_configs = {
            "technical": {
                "fact_extraction_prompt": "Extract technical facts and concepts...",
                "update_memory_prompt": "Analyze technical memory updates...",
                "confidence_threshold": 0.8
            },
            "business": {
                "fact_extraction_prompt": "Extract business insights and decisions...",
                "update_memory_prompt": "Analyze business memory updates...",
                "confidence_threshold": 0.7
            },
            "general": {
                "fact_extraction_prompt": "Extract general information...",
                "update_memory_prompt": "Analyze general memory updates...",
                "confidence_threshold": 0.6
            }
        }
        
        # Get user's custom configuration
        user_config = await self.repository.get_user_config(user_id, domain_type)
        
        # Merge with defaults
        if user_config:
            return user_config
        else:
            return EvolutionConfiguration(
                user_id=user_id,
                domain_type=domain_type,
                **default_configs[domain_type]
            )
```

### 7. Real-time Data Synchronization

#### Sync Engine Architecture
```python
class SyncEngine:
    def __init__(
        self,
        postgresql_client: PostgreSQLClient,
        qdrant_client: QdrantClient,
        event_bus: EventBus
    ):
        self.postgresql = postgresql_client
        self.qdrant = qdrant_client
        self.event_bus = event_bus
    
    async def start_bidirectional_sync(self, user_id: str) -> SyncOperation:
        """Start bidirectional synchronization"""
        
        sync_id = str(uuid4())
        
        # Create sync operation record
        sync_operation = SyncOperation(
            id=sync_id,
            user_id=user_id,
            status="in_progress",
            started_at=datetime.utcnow()
        )
        
        try:
            # Phase 1: PostgreSQL → Qdrant
            await self.sync_postgres_to_qdrant(user_id, sync_id)
            
            # Phase 2: Qdrant → PostgreSQL
            await self.sync_qdrant_to_postgres(user_id, sync_id)
            
            # Phase 3: Validate consistency
            await self.validate_sync_consistency(user_id, sync_id)
            
            sync_operation.status = "completed"
            sync_operation.completed_at = datetime.utcnow()
            
        except Exception as e:
            sync_operation.status = "failed"
            sync_operation.error = str(e)
            
        finally:
            await self.save_sync_operation(sync_operation)
        
        return sync_operation
    
    async def sync_postgres_to_qdrant(self, user_id: str, sync_id: str):
        """Sync PostgreSQL memories to Qdrant"""
        
        # Get memories from PostgreSQL
        memories = await self.postgresql.get_user_memories(user_id)
        
        # Get existing vectors from Qdrant
        collection_name = f"memories_{user_id}_default"
        existing_vectors = await self.qdrant.get_collection_points(collection_name)
        existing_ids = {point.id for point in existing_vectors}
        
        # Identify missing vectors
        missing_memories = [
            memory for memory in memories 
            if memory.id not in existing_ids
        ]
        
        # Add missing vectors
        for memory in missing_memories:
            embedding = await self.generate_embedding(memory.text)
            
            await self.qdrant.upsert(
                collection_name=collection_name,
                points=[{
                    "id": memory.id,
                    "vector": embedding,
                    "payload": {
                        "memory_id": memory.id,
                        "text": memory.text,
                        "metadata": memory.metadata,
                        "created_at": memory.created_at.isoformat()
                    }
                }]
            )
            
            # Emit sync event
            await self.event_bus.emit(
                "sync.postgres_to_qdrant",
                {
                    "sync_id": sync_id,
                    "memory_id": memory.id,
                    "action": "added"
                }
            )
```

### 8. Configuration Management System

#### Hot Configuration Reload
```python
class ConfigManager:
    def __init__(self, db_session: Session):
        self.db = db_session
        self.config_cache = {}
        self.listeners = []
    
    async def update_configuration(
        self,
        user_id: str,
        config_data: dict
    ) -> ConfigUpdateResult:
        """Update configuration with hot reload"""
        
        try:
            # Validate configuration
            validated_config = await self.validate_config(config_data)
            
            # Update database
            await self.save_config_to_db(user_id, validated_config)
            
            # Update cache
            self.config_cache[user_id] = validated_config
            
            # Notify listeners
            await self.notify_config_change(user_id, validated_config)
            
            # Apply hot reload
            reload_result = await self.apply_hot_reload(user_id, validated_config)
            
            return ConfigUpdateResult(
                success=True,
                hot_reload_applied=reload_result.success,
                affected_components=reload_result.components
            )
            
        except Exception as e:
            return ConfigUpdateResult(
                success=False,
                error=str(e)
            )
    
    async def notify_config_change(self, user_id: str, config: dict):
        """Notify all listeners of configuration changes"""
        
        for listener in self.listeners:
            try:
                await listener.on_config_change(user_id, config)
            except Exception as e:
                logging.error(f"Config listener error: {e}")
```

### 9. Security Architecture

#### Authentication Flow
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Client      │    │   API Gateway   │    │   Supabase      │
│                 │    │                 │    │    Auth         │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ 1. Login Request     │                      │
          ├─────────────────────▶│                      │
          │                      │ 2. Validate with    │
          │                      │    Supabase         │
          │                      ├─────────────────────▶│
          │                      │                      │
          │                      │ 3. JWT Token        │
          │                      │◀─────────────────────┤
          │ 4. JWT Token         │                      │
          │◀─────────────────────┤                      │
          │                      │                      │
          │ 5. API Request       │                      │
          │    with JWT          │                      │
          ├─────────────────────▶│                      │
          │                      │ 6. Verify JWT       │
          │                      ├─────────────────────▶│
          │                      │                      │
          │                      │ 7. User Info        │
          │                      │◀─────────────────────┤
          │ 8. API Response      │                      │
          │◀─────────────────────┤                      │
          │                      │                      │
```

#### Permission System
```python
class PermissionManager:
    def __init__(self, db_session: Session):
        self.db = db_session
    
    async def check_memory_access(
        self,
        user_id: str,
        memory_id: str,
        operation: str
    ) -> bool:
        """Check if user has permission to access memory"""
        
        # Get memory
        memory = await self.db.query(Memory).filter(
            Memory.id == memory_id
        ).first()
        
        if not memory:
            return False
        
        # Check ownership
        if memory.user_id != user_id:
            return False
        
        # Check memory state
        if memory.state == "deleted":
            return False
        
        # Check operation permissions
        if operation == "delete" and memory.state == "archived":
            return False
        
        return True
    
    async def check_app_access(
        self,
        user_id: str,
        app_id: str
    ) -> bool:
        """Check if user has access to app"""
        
        app = await self.db.query(App).filter(
            App.id == app_id
        ).first()
        
        if not app:
            return False
        
        # Check ownership
        if app.owner_id != user_id:
            return False
        
        return True
```

### 10. Monitoring and Observability

#### Health Monitoring System
```python
class HealthService:
    def __init__(
        self,
        db_session: Session,
        qdrant_client: QdrantClient,
        memory_service: MemoryService
    ):
        self.db = db_session
        self.qdrant = qdrant_client
        self.memory_service = memory_service
    
    async def comprehensive_health_check(self) -> HealthStatus:
        """Perform comprehensive system health check"""
        
        components = {}
        
        # Database health
        components["database"] = await self.check_database_health()
        
        # Vector store health
        components["vector_store"] = await self.check_qdrant_health()
        
        # Memory engine health
        components["memory_engine"] = await self.check_memory_engine_health()
        
        # Evolution service health
        components["evolution_service"] = await self.check_evolution_service_health()
        
        # Determine overall health
        overall_health = "healthy" if all(
            comp["status"] == "healthy" for comp in components.values()
        ) else "degraded"
        
        return HealthStatus(
            overall_health=overall_health,
            components=components,
            timestamp=datetime.utcnow()
        )
    
    async def check_database_health(self) -> ComponentHealth:
        """Check database connectivity and performance"""
        
        start_time = time.time()
        
        try:
            # Test basic connectivity
            result = await self.db.execute(text("SELECT 1"))
            
            # Test query performance
            await self.db.execute(text("SELECT COUNT(*) FROM memory_master.users"))
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                status="healthy",
                response_time_ms=response_time,
                details={
                    "connection_pool": "available",
                    "query_performance": "normal"
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                status="unhealthy",
                error=str(e),
                details={
                    "connection_pool": "unavailable"
                }
            )
```

## Data Flow Architecture

### 1. Memory Creation Flow
```
User Input → Frontend → API Gateway → Authentication → Memory Service → 
Text Processing → Category Inference → Database Storage → Vector Generation → 
Qdrant Storage → Evolution Analysis → Response to User
```

### 2. Memory Search Flow
```
Search Query → Frontend → API Gateway → Authentication → Memory Service → 
Query Embedding → Qdrant Search → Result Ranking → Database Enrichment → 
Access Logging → Response to User
```

### 3. Configuration Update Flow
```
Config Change → Frontend → API Gateway → Authentication → Config Service → 
Validation → Database Update → Cache Update → Listener Notification → 
Hot Reload → Response to User
```

## Performance Optimizations

### 1. Database Optimizations
- **Connection Pooling**: 20 connections with 30 overflow
- **Query Optimization**: Proper indexing and query planning
- **Batch Operations**: Bulk inserts and updates
- **Pagination**: Efficient offset-based pagination

### 2. Vector Search Optimizations
- **Collection Sharding**: User-based collection separation
- **Embedding Caching**: Cache frequently used embeddings
- **Batch Processing**: Bulk vector operations
- **Similarity Thresholds**: Configurable score thresholds

### 3. API Performance
- **Response Caching**: Redis-based caching layer
- **Async Operations**: Non-blocking I/O operations
- **Background Tasks**: Deferred processing for heavy operations
- **Rate Limiting**: Request throttling and abuse prevention

### 4. Frontend Optimizations
- **Code Splitting**: Dynamic imports and lazy loading
- **State Management**: Efficient Redux state updates
- **Memoization**: React.memo and useMemo optimizations
- **Virtual Scrolling**: Efficient large list rendering

## Scalability Considerations

### 1. Horizontal Scaling
- **Stateless Services**: All services designed to be stateless
- **Load Balancing**: Multiple API server instances
- **Database Sharding**: User-based data sharding
- **CDN Integration**: Static asset distribution

### 2. Vertical Scaling
- **Resource Optimization**: Efficient memory and CPU usage
- **Database Tuning**: Optimized PostgreSQL configuration
- **Vector Database**: Qdrant performance tuning
- **Container Optimization**: Efficient Docker configurations

### 3. Caching Strategy
- **Multi-level Caching**: Application, database, and CDN caching
- **Cache Invalidation**: Smart cache invalidation strategies
- **Session Management**: Efficient session storage
- **Static Assets**: Long-term caching for static resources

## Deployment Architecture

### 1. Container Orchestration
```yaml
# docker-compose.yml
version: '3.8'

services:
  memory-mcp:
    build: ./api
    ports:
      - "8765:8765"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - QDRANT_HOST=${QDRANT_HOST}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - qdrant
    
  memory-ui:
    build: ./memory-ui
    ports:
      - "3210:3210"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8765
    depends_on:
      - memory-mcp
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=memory_master
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  qdrant:
    image: qdrant/qdrant
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
```

### 2. Production Deployment
- **Container Registry**: Docker Hub or AWS ECR
- **Orchestration**: Docker Swarm or Kubernetes
- **Service Discovery**: Consul or Kubernetes DNS
- **Configuration Management**: Environment-based configuration

## Testing Strategy

### 1. Unit Testing
- **Service Layer**: Business logic testing
- **Repository Layer**: Data access testing
- **Utility Functions**: Helper function testing
- **API Endpoints**: Route handler testing

### 2. Integration Testing
- **Database Integration**: Full database operations
- **Vector Store Integration**: Qdrant operations
- **API Integration**: End-to-end API testing
- **MCP Integration**: Protocol testing

### 3. E2E Testing
- **User Workflows**: Complete user journeys
- **Cross-browser Testing**: Multiple browser support
- **Performance Testing**: Load and stress testing
- **Security Testing**: Authentication and authorization

## Conclusion

Memory Master v2 represents a sophisticated, scalable, and maintainable architecture designed for modern AI-powered memory management. The system leverages best practices in microservices architecture, domain-driven design, and event-driven architecture to provide a robust foundation for intelligent memory operations.

The architecture supports:
- **Scalability**: Horizontal and vertical scaling capabilities
- **Maintainability**: Clean code organization and separation of concerns
- **Extensibility**: Plugin-based architecture for future enhancements
- **Reliability**: Comprehensive error handling and monitoring
- **Security**: Multi-layered security approach
- **Performance**: Optimized for high-performance operations

This architecture provides a solid foundation for the Memory Master v2 system and can accommodate future growth and feature requirements.