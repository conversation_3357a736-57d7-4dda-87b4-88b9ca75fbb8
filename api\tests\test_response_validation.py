import pytest
import uuid
from unittest.mock import Mock, patch, MagicMock
from app.mcp_server import (
    validate_mem0_response,
    _validate_add_update_response,
    _validate_get_response,
    _validate_search_response,
    _validate_delete_response,
    _validate_generic_response,
    verify_memory_storage
)

class TestValidateMem0Response:
    """Test the enhanced validate_mem0_response function."""
    
    def test_empty_response(self):
        """Test validation with empty response."""
        is_valid, message = validate_mem0_response(None, "add_memory")
        assert not is_valid
        assert "add_memory failed: Empty response" in message
    
    def test_error_response(self):
        """Test validation with error in response."""
        response = {"error": "Connection failed"}
        is_valid, message = validate_mem0_response(response, "add_memory")
        assert not is_valid
        assert "add_memory failed: Connection failed" in message
    
    def test_invalid_format_response(self):
        """Test validation with invalid response format."""
        is_valid, message = validate_mem0_response("invalid", "add_memory")
        assert not is_valid
        assert "Invalid response format" in message
        assert "expected dict, got str" in message
    
    def test_operation_type_routing(self):
        """Test that different operation types are routed correctly."""
        response = {"results": []}
        
        # Test add_memory routing
        is_valid, message = validate_mem0_response(response, "add_memory")
        assert not is_valid
        assert "add_memory failed: Empty results list" in message
        
        # Test get_memories routing
        is_valid, message = validate_mem0_response(response, "get_memories")
        assert is_valid
        assert "get_memories successful: 0 memory(ies) retrieved" in message

class TestValidateAddUpdateResponse:
    """Test add/update memory operation validation."""
    
    def test_missing_results(self):
        """Test validation when results key is missing."""
        response = {"status": "ok"}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "No results in response" in message
    
    def test_invalid_results_type(self):
        """Test validation when results is not a list."""
        response = {"results": "not a list"}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "Results is not a list" in message
    
    def test_empty_results(self):
        """Test validation with empty results list."""
        response = {"results": []}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "Empty results list" in message
    
    def test_invalid_result_structure(self):
        """Test validation with invalid result structure."""
        response = {"results": ["not a dict"]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "Result 0 is not a dict" in message
    
    def test_missing_id_field(self):
        """Test validation when result is missing id field."""
        response = {"results": [{"memory": "test"}]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "Result 0 missing 'id' field" in message
    
    def test_missing_memory_and_event_fields(self):
        """Test validation when result is missing both memory and event fields."""
        response = {"results": [{"id": str(uuid.uuid4())}]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "Result 0 missing 'memory' or 'event' field" in message
    
    def test_invalid_uuid_format(self):
        """Test validation with invalid UUID format."""
        response = {"results": [{"id": "invalid-uuid", "memory": "test"}]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert not is_valid
        assert "invalid UUID format" in message
    
    def test_valid_response_with_memory(self):
        """Test validation with valid response containing memory field."""
        memory_id = str(uuid.uuid4())
        response = {"results": [{"id": memory_id, "memory": "test memory"}]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert is_valid
        assert "add_memory successful: 1 memory(ies) processed" in message
    
    def test_valid_response_with_event(self):
        """Test validation with valid response containing event field."""
        memory_id = str(uuid.uuid4())
        response = {"results": [{"id": memory_id, "event": "ADD"}]}
        is_valid, message = _validate_add_update_response(response, "add_memory")
        assert is_valid
        assert "add_memory successful: 1 memory(ies) processed" in message
    
    def test_multiple_results(self):
        """Test validation with multiple valid results."""
        memory_id1 = str(uuid.uuid4())
        memory_id2 = str(uuid.uuid4())
        response = {
            "results": [
                {"id": memory_id1, "memory": "test memory 1"},
                {"id": memory_id2, "event": "ADD"}
            ]
        }
        is_valid, message = _validate_add_update_response(response, "update_memory")
        assert is_valid
        assert "update_memory successful: 2 memory(ies) processed" in message

class TestValidateGetResponse:
    """Test get memories operation validation."""
    
    def test_missing_results(self):
        """Test validation when results key is missing (valid for get)."""
        response = {"status": "ok"}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert is_valid
        assert "No memories found" in message
    
    def test_invalid_results_type(self):
        """Test validation when results is not a list."""
        response = {"results": "not a list"}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert not is_valid
        assert "Results is not a list" in message
    
    def test_empty_results(self):
        """Test validation with empty results (valid for get)."""
        response = {"results": []}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert is_valid
        assert "0 memory(ies) retrieved" in message
    
    def test_invalid_memory_structure(self):
        """Test validation with invalid memory structure."""
        response = {"results": ["not a dict"]}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert not is_valid
        assert "Memory 0 is not a dict" in message
    
    def test_missing_id_field(self):
        """Test validation when memory is missing id field."""
        response = {"results": [{"content": "test"}]}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert not is_valid
        assert "Memory 0 missing 'id' field" in message
    
    def test_invalid_uuid_format(self):
        """Test validation with invalid UUID format."""
        response = {"results": [{"id": "invalid-uuid", "content": "test"}]}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert not is_valid
        assert "invalid UUID format" in message
    
    def test_valid_response(self):
        """Test validation with valid response."""
        memory_id = str(uuid.uuid4())
        response = {"results": [{"id": memory_id, "content": "test memory"}]}
        is_valid, message = _validate_get_response(response, "get_memories")
        assert is_valid
        assert "1 memory(ies) retrieved" in message

class TestValidateSearchResponse:
    """Test search memories operation validation."""
    
    def test_missing_results(self):
        """Test validation when results key is missing (valid for search)."""
        response = {"status": "ok"}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert is_valid
        assert "No matching memories found" in message
    
    def test_empty_results(self):
        """Test validation with empty results (valid for search)."""
        response = {"results": []}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert is_valid
        assert "No matching memories found" in message
    
    def test_invalid_search_result_structure(self):
        """Test validation with invalid search result structure."""
        response = {"results": ["not a dict"]}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert not is_valid
        assert "Search result 0 is not a dict" in message
    
    def test_missing_id_field(self):
        """Test validation when search result is missing id field."""
        response = {"results": [{"memory": "test", "score": 0.9}]}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert not is_valid
        assert "Search result 0 missing 'id' field" in message
    
    def test_missing_memory_and_score_fields(self):
        """Test validation when search result is missing both memory and score fields."""
        response = {"results": [{"id": str(uuid.uuid4())}]}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert not is_valid
        assert "Search result 0 missing 'memory' or 'score' field" in message
    
    def test_valid_response_with_memory(self):
        """Test validation with valid search response containing memory field."""
        memory_id = str(uuid.uuid4())
        response = {"results": [{"id": memory_id, "memory": "test memory"}]}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert is_valid
        assert "1 matching memory(ies) found" in message
    
    def test_valid_response_with_score(self):
        """Test validation with valid search response containing score field."""
        memory_id = str(uuid.uuid4())
        response = {"results": [{"id": memory_id, "score": 0.95}]}
        is_valid, message = _validate_search_response(response, "search_memories")
        assert is_valid
        assert "1 matching memory(ies) found" in message

class TestValidateDeleteResponse:
    """Test delete memory operation validation."""
    
    def test_success_field_true(self):
        """Test validation with success field set to true."""
        response = {"success": True}
        is_valid, message = _validate_delete_response(response, "delete_memory")
        assert is_valid
        assert "delete_memory successful" in message
    
    def test_success_field_false(self):
        """Test validation with success field set to false."""
        response = {"success": False}
        is_valid, message = _validate_delete_response(response, "delete_memory")
        assert not is_valid
        assert "delete_memory failed" in message
    
    def test_results_field_with_data(self):
        """Test validation with results field containing data."""
        response = {"results": [{"id": str(uuid.uuid4()), "deleted": True}]}
        is_valid, message = _validate_delete_response(response, "delete_memory")
        assert is_valid
        assert "1 memory(ies) deleted" in message
    
    def test_results_field_empty(self):
        """Test validation with empty results field."""
        response = {"results": []}
        is_valid, message = _validate_delete_response(response, "delete_memory")
        assert is_valid
        assert "delete_memory completed" in message
    
    def test_unknown_format(self):
        """Test validation with unknown response format."""
        response = {"status": "deleted"}
        is_valid, message = _validate_delete_response(response, "delete_memory")
        assert is_valid
        assert "delete_memory completed" in message

class TestVerifyMemoryStorage:
    """Test memory storage verification functionality."""
    
    def test_invalid_response_format(self):
        """Test verification with invalid response format."""
        mock_client = Mock()
        is_valid, message = verify_memory_storage("invalid", mock_client)
        assert not is_valid
        assert "Invalid response format" in message
    
    def test_missing_results(self):
        """Test verification when results key is missing."""
        mock_client = Mock()
        response = {"status": "ok"}
        is_valid, message = verify_memory_storage(response, mock_client)
        assert not is_valid
        assert "Invalid response format" in message
    
    def test_empty_results(self):
        """Test verification with empty results."""
        mock_client = Mock()
        response = {"results": []}
        is_valid, message = verify_memory_storage(response, mock_client)
        assert not is_valid
        assert "No results to verify" in message
    
    def test_no_memory_ids(self):
        """Test verification when no memory IDs are found."""
        mock_client = Mock()
        response = {"results": [{"memory": "test"}]}
        is_valid, message = verify_memory_storage(response, mock_client)
        assert not is_valid
        assert "No memory IDs found" in message
    
    @patch('time.sleep')
    def test_verification_success(self, mock_sleep):
        """Test successful memory verification."""
        memory_id = str(uuid.uuid4())
        mock_client = Mock()
        mock_client.get_all.return_value = {
            "results": [{"id": memory_id, "content": "test"}]
        }
        
        response = {"results": [{"id": memory_id, "memory": "test"}]}
        is_valid, message = verify_memory_storage(response, mock_client)
        
        assert is_valid
        assert "1 memories confirmed stored" in message
        mock_sleep.assert_called_once_with(0.1)
        mock_client.get_all.assert_called_once()
    
    @patch('time.sleep')
    def test_verification_missing_memories(self, mock_sleep):
        """Test verification when stored memories are not found."""
        stored_id = str(uuid.uuid4())
        existing_id = str(uuid.uuid4())
        
        mock_client = Mock()
        mock_client.get_all.return_value = {
            "results": [{"id": existing_id, "content": "test"}]
        }
        
        response = {"results": [{"id": stored_id, "memory": "test"}]}
        is_valid, message = verify_memory_storage(response, mock_client)
        
        assert not is_valid
        assert "1 memories not found after storage" in message
    
    @patch('time.sleep')
    def test_verification_get_all_failure(self, mock_sleep):
        """Test verification when get_all fails."""
        mock_client = Mock()
        mock_client.get_all.return_value = "invalid response"
        
        response = {"results": [{"id": str(uuid.uuid4()), "memory": "test"}]}
        is_valid, message = verify_memory_storage(response, mock_client)
        
        assert not is_valid
        assert "Unable to retrieve memories for verification" in message
    
    @patch('time.sleep')
    def test_verification_exception_handling(self, mock_sleep):
        """Test verification exception handling."""
        mock_client = Mock()
        mock_client.get_all.side_effect = Exception("Connection error")
        
        response = {"results": [{"id": str(uuid.uuid4()), "memory": "test"}]}
        is_valid, message = verify_memory_storage(response, mock_client)
        
        assert not is_valid
        assert "Memory verification failed due to error: Connection error" in message
    
    @patch('time.sleep')
    def test_verification_with_request_id(self, mock_sleep):
        """Test verification with request ID for logging."""
        memory_id = str(uuid.uuid4())
        mock_client = Mock()
        mock_client.get_all.return_value = {
            "results": [{"id": memory_id, "content": "test"}]
        }
        
        response = {"results": [{"id": memory_id, "memory": "test"}]}
        
        with patch('app.mcp_server.logging') as mock_logging:
            is_valid, message = verify_memory_storage(response, mock_client, "test_req_123")
            
            assert is_valid
            # Verify that logging was called with request ID
            mock_logging.info.assert_called()
            log_calls = [call.args[0] for call in mock_logging.info.call_args_list]
            assert any("[REQ_test_req_123]" in call for call in log_calls)