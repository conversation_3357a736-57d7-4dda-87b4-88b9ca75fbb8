services:
  memory-mcp:
    build: 
      context: api/
      dockerfile: Dockerfile
    container_name: memory-mcp
    restart: unless-stopped
    env_file:
      - api/.env
    environment:
      # Use external service IPs directly for better reliability
      - QDRANT_HOST=${QDRANT_HOST:-*************}
      - SUPABASE_HOST=${SUPABASE_HOST:-*************}
    ports:
      - "0.0.0.0:8765:8765"  # Bind to all interfaces for external access
    volumes:
      - ./api:/usr/src/app
      - api_node_modules:/usr/src/app/node_modules
    working_dir: /usr/src/app
    command: >
      sh -c "uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 4"
    extra_hosts:
      # External service resolution
      - "qdrant.local:*************"
      - "supabase.local:*************"
    networks:
      - memory-network
    deploy:
      resources:
        limits:
          memory: 2G          # Increased from 1G to 2G for better performance
          cpus: '2.0'         # Increased from 1.0 to 2.0 for better concurrency
          
  memory-ui:
    build:
      context: memory-ui/
      dockerfile: Dockerfile
    container_name: memory-ui
    restart: unless-stopped
    ports:
      - "3210:3210"
    env_file:
      - memory-ui/.env.local
    environment:
      # API endpoint configuration for cross-platform
      - NEXT_PUBLIC_API_URL=${API_URL:-http://localhost:8765}
    volumes:
      - ./memory-ui:/usr/src/app
      - ui_node_modules:/usr/src/app/node_modules
      - ui_next_cache:/usr/src/app/.next
    working_dir: /usr/src/app
    depends_on:
      - memory-mcp
    networks:
      - memory-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  api_node_modules:
    driver: local
  ui_node_modules:
    driver: local
  ui_next_cache:
    driver: local

networks:
  memory-network:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
      com.docker.network.bridge.enable_ip_masquerade: "true"
    ipam:
      config:
        - subnet: **********/16