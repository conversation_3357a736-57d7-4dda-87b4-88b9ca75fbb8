{"name": "memory-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3210", "build": "next build", "start": "next start -p 3210", "lint": "next lint", "clear-cache": "node scripts/clear-cache.js", "build:analyze": "ANALYZE=true npm run build", "build:clean": "npm run clear-cache && npm run build", "verify-webpack-fix": "node scripts/verify-webpack-fix.js", "analyze-bundle": "node scripts/analyze-bundle.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:chrome": "playwright test --project=chrome-desktop", "test:e2e:mobile": "playwright test --project=chrome-mobile", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:all": "playwright test --project=chrome-desktop --project=firefox --project=webkit", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report test-results/html-report", "test:e2e:coverage": "playwright test --reporter=html --reporter=json-summary", "test:install": "playwright install"}, "dependencies": {"@heroicons/react": "^2.1.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.3.1", "lucide-react": "^0.309.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.60.0", "sonner": "^1.3.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.5", "@playwright/test": "^1.53.2", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "playwright": "^1.53.2", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}