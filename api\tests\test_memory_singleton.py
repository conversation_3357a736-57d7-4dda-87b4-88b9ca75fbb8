import threading
import time
import pytest
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.memory import MemoryClientSingleton, get_memory_client, reset_memory_client


class TestMemoryClientSingleton:
    """Test suite for the MemoryClientSingleton class."""
    
    def setup_method(self):
        """Reset the singleton before each test."""
        reset_memory_client()
    
    def test_singleton_instance_creation(self):
        """Test that only one instance of the singleton is created."""
        singleton1 = MemoryClientSingleton()
        singleton2 = MemoryClientSingleton()
        
        # Both should be the same instance
        assert singleton1 is singleton2
    
    def test_singleton_thread_safety(self):
        """Test that the singleton is thread-safe."""
        instances = []
        
        def create_singleton():
            instance = MemoryClientSingleton()
            instances.append(instance)
        
        # Create multiple threads that try to create the singleton
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=create_singleton)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All instances should be the same
        first_instance = instances[0]
        for instance in instances:
            assert instance is first_instance
    
    def test_client_initialization_once(self):
        """Test that the client is only initialized once."""
        singleton = MemoryClientSingleton()
        
        # Mock config for testing
        test_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1,
                    "api_key": "test-key"
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "model": "text-embedding-3-small",
                    "api_key": "test-key"
                }
            }
        }
        
        # Get client multiple times with same config
        client1 = singleton.get_client(test_config)
        client2 = singleton.get_client(test_config)
        
        # Should return the same client instance
        assert client1 is client2
    
    def test_config_change_triggers_reinit(self):
        """Test that changing config triggers client reinitialization."""
        singleton = MemoryClientSingleton()
        
        # First config
        config1 = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1,
                    "api_key": "test-key"
                }
            }
        }
        
        # Second config (different)
        config2 = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o",  # Different model
                    "temperature": 0.1,
                    "api_key": "test-key"
                }
            }
        }
        
        # Get client with first config
        client1 = singleton.get_client(config1)
        
        # Get client with second config
        client2 = singleton.get_client(config2)
        
        # Should be different clients due to config change
        # Note: This might be the same if mem0 doesn't recreate for config changes
        # The important thing is that the config hash changed
        assert singleton._config_hash is not None
    
    def test_reset_client(self):
        """Test that reset_client properly clears the client."""
        singleton = MemoryClientSingleton()
        
        # Initialize client
        test_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1,
                    "api_key": "test-key"
                }
            }
        }
        
        client1 = singleton.get_client(test_config)
        assert singleton._client is not None
        
        # Reset the client
        singleton.reset_client()
        assert singleton._client is None
        assert singleton._config_hash is None
        
        # Get client again should create new instance
        client2 = singleton.get_client(test_config)
        # Note: client1 and client2 might be the same object if mem0 reuses instances
        # The important thing is that reset worked without errors
        assert client2 is not None
    
    def test_health_check(self):
        """Test the health check functionality."""
        singleton = MemoryClientSingleton()
        
        # Health check with no client should return False
        assert not singleton.is_healthy()
        
        # Initialize client
        test_config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1,
                    "api_key": "test-key"
                }
            }
        }
        
        client = singleton.get_client(test_config)
        if client is not None:
            # Health check with client should return True
            assert singleton.is_healthy()
    
    def test_concurrent_access(self):
        """Test concurrent access to the singleton."""
        results = []
        errors = []
        
        def access_singleton():
            try:
                singleton = MemoryClientSingleton()
                test_config = {
                    "llm": {
                        "provider": "openai",
                        "config": {
                            "model": "gpt-4o-mini",
                            "temperature": 0.1,
                            "api_key": "test-key"
                        }
                    }
                }
                client = singleton.get_client(test_config)
                results.append((singleton, client))
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for _ in range(20):
            thread = threading.Thread(target=access_singleton)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check that no errors occurred
        assert len(errors) == 0, f"Errors occurred: {errors}"
        
        # Check that all singletons are the same instance
        if results:
            first_singleton = results[0][0]
            for singleton, client in results:
                assert singleton is first_singleton


class TestGetMemoryClient:
    """Test suite for the get_memory_client function."""
    
    def setup_method(self):
        """Reset the singleton before each test."""
        reset_memory_client()
    
    def test_get_memory_client_returns_client(self):
        """Test that get_memory_client returns a client or None gracefully."""
        # This should not raise an exception
        client = get_memory_client()
        # Client might be None if configuration is invalid, which is acceptable
        assert client is None or hasattr(client, 'add')  # mem0 clients have add method
    
    def test_get_memory_client_with_custom_instructions(self):
        """Test get_memory_client with custom instructions."""
        custom_instructions = "Test custom instructions for memory"
        
        # This should not raise an exception
        client = get_memory_client(custom_instructions=custom_instructions)
        # Client might be None if configuration is invalid, which is acceptable
        assert client is None or hasattr(client, 'add')
    
    def test_multiple_calls_return_same_client(self):
        """Test that multiple calls to get_memory_client return the same instance."""
        client1 = get_memory_client()
        client2 = get_memory_client()
        
        # If both are not None, they should be the same instance
        if client1 is not None and client2 is not None:
            assert client1 is client2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])