#!/usr/bin/env python3
"""
Comprehensive test for MCP server functionality
"""
import os
import sys
import asyncio
import aiohttp
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test configuration
BASE_URL = "http://0.0.0.0:8765"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
USER_ID = "aungheinaye"

# Set environment variables
os.environ['SUPABASE_ANON_KEY'] = SUPABASE_ANON_KEY
os.environ['MCP_USER_ID'] = USER_ID

async def test_basic_health():
    """Test basic API health"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"✅ Basic health check: {health_data}")
                    return True
                else:
                    logger.error(f"❌ Basic health check failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ Basic health check error: {e}")
        return False

async def test_mcp_health():
    """Test MCP server health"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/mcp/health") as response:
                if response.status == 200:
                    mcp_health_data = await response.json()
                    logger.info(f"✅ MCP health check: {mcp_health_data}")
                    return True
                else:
                    error_data = await response.json()
                    logger.error(f"❌ MCP health check failed: {response.status} - {error_data}")
                    return False
    except Exception as e:
        logger.error(f"❌ MCP health check error: {e}")
        return False

async def test_mcp_sse_connection():
    """Test MCP SSE connection (Claude Desktop endpoint)"""
    headers = {
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            url = f"{BASE_URL}/mcp/claude/sse/{USER_ID}"
            logger.info(f"🔗 Testing SSE connection to: {url}")
            
            # Test with a short timeout since SSE is a streaming endpoint
            async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    # For SSE, we expect a streaming response
                    # Read the first chunk to verify it's working
                    chunk = await response.content.read(1024)
                    if chunk:
                        logger.info(f"✅ SSE connection successful, received data: {chunk[:100]}...")
                        return True
                    else:
                        logger.warning("⚠️ SSE connection opened but no data received")
                        return False
                else:
                    error_data = await response.text()
                    logger.error(f"❌ SSE connection failed: {response.status} - {error_data}")
                    return False
    except asyncio.TimeoutError:
        logger.warning("⚠️ SSE connection timeout (expected for streaming endpoint)")
        return True  # Timeout is expected for SSE
    except Exception as e:
        logger.error(f"❌ SSE connection error: {e}")
        return False

async def test_mcp_tools_via_api():
    """Test MCP tools via regular API endpoints"""
    headers = {
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test the health endpoint that should be accessible
        async with aiohttp.ClientSession() as session:
            # Test memories endpoint
            async with session.get(f"{BASE_URL}/api/v1/memories", headers=headers) as response:
                if response.status == 200:
                    memories_data = await response.json()
                    logger.info(f"✅ Memories API accessible: {len(memories_data.get('items', []))} memories")
                    return True
                else:
                    error_data = await response.text()
                    logger.error(f"❌ Memories API failed: {response.status} - {error_data}")
                    return False
    except Exception as e:
        logger.error(f"❌ Memories API error: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting comprehensive MCP server tests")
    
    tests = [
        ("Basic Health", test_basic_health),
        ("MCP Health", test_mcp_health),
        ("MCP SSE Connection", test_mcp_sse_connection),
        ("MCP Tools via API", test_mcp_tools_via_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"🧪 Running test: {test_name}")
        result = await test_func()
        results.append((test_name, result))
        logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        print()  # Add spacing between tests
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    logger.info(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! MCP server is working correctly.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)