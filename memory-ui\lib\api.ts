const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5678';
const API_VERSION = process.env.NEXT_PUBLIC_API_VERSION || 'v1';

export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export class APIClient {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/api/${API_VERSION}`;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    userId?: string
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>,
    };
    
    // Add user ID header if provided
    if (userId && userId !== 'guest') {
      headers['X-User-ID'] = userId;
    }
    
    const config: RequestInit = {
      headers,
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new APIError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  }

  // Memory operations
  async getMemories(userId: string, params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
  } = {}) {
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.category) searchParams.append('category', params.category);
    
    const query = searchParams.toString();
    const endpoint = `/memories${query ? `?${query}` : ''}`;
    
    return this.request(endpoint, {}, userId);
  }

  async createMemory(userId: string, memory: {
    title: string;
    content: string;
    metadata?: Record<string, any>;
  }) {
    return this.request('/memories', {
      method: 'POST',
      body: JSON.stringify(memory)
    }, userId);
  }

  async updateMemory(userId: string, memoryId: string, memory: {
    title?: string;
    content?: string;
    metadata?: Record<string, any>;
  }) {
    return this.request(`/memories/${memoryId}`, {
      method: 'PUT',
      body: JSON.stringify(memory)
    }, userId);
  }

  async deleteMemory(userId: string, memoryId: string) {
    return this.request(`/memories/${memoryId}`, {
      method: 'DELETE'
    }, userId);
  }

  // Evolution operations
  async getEvolutionOperations(userId: string, params: {
    limit?: number;
    page?: number;
  } = {}) {
    const searchParams = new URLSearchParams();
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.page) searchParams.append('page', params.page.toString());
    
    const query = searchParams.toString();
    const endpoint = `/evolution/operations${query ? `?${query}` : ''}`;
    
    return this.request(endpoint, {}, userId);
  }

  async getEvolutionAnalytics(userId: string) {
    return this.request('/evolution/analytics', {}, userId);
  }

  // Health check
  async health() {
    return this.request('/health');
  }
}

export const apiClient = new APIClient();