"""
Async Memory Client Initialization

This module provides asynchronous initialization of the memory client to prevent
blocking the MCP server startup. The memory client is initialized in the background
while the MCP server can start immediately and respond to requests.
"""

import asyncio
import logging
import threading
import time
from typing import Optional, Callable, Any
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import asynccontextmanager

from app.utils.memory import MemoryClient<PERSON><PERSON>leton, get_memory_client
from app.enhanced_logging import operation_logger


class AsyncMemoryManager:
    """Manages async initialization of memory client"""
    
    def __init__(self):
        self._initialization_task: Optional[asyncio.Task] = None
        self._initialization_complete = asyncio.Event()
        self._client_ready = False
        self._error: Optional[Exception] = None
        self._executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="memory-init")
        
    async def start_initialization(self, user_id: str = "aungheinaye") -> None:
        """Start async initialization of memory client"""
        if self._initialization_task is None or self._initialization_task.done():
            logging.info(f"[ASYNC_MEMORY] Starting background memory client initialization for {user_id}")
            self._initialization_task = asyncio.create_task(
                self._initialize_memory_client(user_id)
            )
        
    async def _initialize_memory_client(self, user_id: str) -> None:
        """Initialize memory client in background"""
        try:
            logging.info("[ASYNC_MEMORY] Beginning memory client initialization...")
            
            # Run the blocking initialization in a thread pool
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self._executor,
                self._sync_initialize_client,
                user_id
            )
            
            self._client_ready = True
            self._initialization_complete.set()
            logging.info("[ASYNC_MEMORY] ✅ Memory client initialization completed successfully")
            
        except Exception as e:
            self._error = e
            self._initialization_complete.set()
            logging.error(f"[ASYNC_MEMORY] ❌ Memory client initialization failed: {e}")
            
    def _sync_initialize_client(self, user_id: str) -> None:
        """Synchronous initialization wrapped for thread execution"""
        try:
            # Force reinitialize to ensure fresh client
            singleton = MemoryClientSingleton()
            singleton._client = None
            singleton._last_config_hash = None
            
            # Disable signal-based timeout for background thread
            import os
            os.environ['DISABLE_MEMORY_TIMEOUT'] = 'true'
            
            # Initialize client
            client = get_memory_client()
            if client is None:
                raise Exception("Failed to initialize memory client")
                
            logging.info(f"[ASYNC_MEMORY] Memory client initialized for user: {user_id}")
            
        except Exception as e:
            logging.error(f"[ASYNC_MEMORY] Sync initialization error: {e}")
            raise
        finally:
            # Re-enable timeout for future calls
            if 'DISABLE_MEMORY_TIMEOUT' in os.environ:
                del os.environ['DISABLE_MEMORY_TIMEOUT']
        
    async def wait_for_initialization(self, timeout: float = 30.0) -> bool:
        """Wait for initialization to complete with timeout"""
        try:
            await asyncio.wait_for(self._initialization_complete.wait(), timeout=timeout)
            return self._client_ready
        except asyncio.TimeoutError:
            logging.warning(f"[ASYNC_MEMORY] Initialization timeout after {timeout}s")
            return False
            
    def is_ready(self) -> bool:
        """Check if memory client is ready"""
        return self._client_ready
        
    def get_error(self) -> Optional[Exception]:
        """Get initialization error if any"""
        return self._error
        
    def get_client_safely(self) -> Optional[Any]:
        """Get memory client if ready, None otherwise"""
        if self._client_ready:
            try:
                return get_memory_client()
            except Exception as e:
                logging.error(f"[ASYNC_MEMORY] Error getting client: {e}")
                return None
        return None


# Global async memory manager instance
_async_memory_manager: Optional[AsyncMemoryManager] = None
_manager_lock = threading.Lock()


def get_async_memory_manager() -> AsyncMemoryManager:
    """Get or create async memory manager singleton"""
    global _async_memory_manager
    if _async_memory_manager is None:
        with _manager_lock:
            if _async_memory_manager is None:
                _async_memory_manager = AsyncMemoryManager()
    return _async_memory_manager


async def ensure_memory_client_async(user_id: str = "aungheinaye") -> None:
    """Ensure memory client initialization has started"""
    manager = get_async_memory_manager()
    await manager.start_initialization(user_id)


def get_memory_client_safe() -> Optional[Any]:
    """Get memory client safely without blocking"""
    manager = get_async_memory_manager()
    return manager.get_client_safely()


async def wait_for_memory_client(timeout: float = 30.0, user_id: str = "aungheinaye") -> Optional[Any]:
    """Wait for memory client to be ready"""
    manager = get_async_memory_manager()
    
    # Start initialization if not started
    await manager.start_initialization(user_id)
    
    # Wait for completion
    if await manager.wait_for_initialization(timeout):
        return manager.get_client_safely()
    else:
        return None


class AsyncMemoryResponse:
    """Response object for operations when memory client is not ready"""
    
    def __init__(self, message: str, operation: str, user_id: str):
        self.message = message
        self.operation = operation
        self.user_id = user_id
        self.is_deferred = True
        
    def to_dict(self) -> dict:
        return {
            "message": self.message,
            "operation": self.operation,
            "user_id": self.user_id,
            "status": "deferred",
            "note": "Memory client is initializing. Operation will be processed when ready."
        }


@asynccontextmanager
async def async_memory_context(user_id: str = "aungheinaye", timeout: float = 10.0):
    """Async context manager for memory operations"""
    manager = get_async_memory_manager()
    
    # Start initialization if needed
    await manager.start_initialization(user_id)
    
    # Try to get client quickly
    if manager.is_ready():
        client = manager.get_client_safely()
        yield client
    else:
        # Wait briefly for initialization
        client = await wait_for_memory_client(timeout, user_id)
        yield client


def create_deferred_response(operation: str, user_id: str, text: str = "") -> AsyncMemoryResponse:
    """Create a deferred response when memory client is not ready"""
    messages = {
        "add_memory": f"Memory addition request queued for {user_id}. The memory will be added once the system is fully initialized.",
        "search_memory": f"Memory search request queued for {user_id}. Please try again in a few moments.",
        "list_memories": f"Memory list request queued for {user_id}. Please try again in a few moments."
    }
    
    message = messages.get(operation, f"Operation {operation} queued for {user_id}")
    return AsyncMemoryResponse(message, operation, user_id)