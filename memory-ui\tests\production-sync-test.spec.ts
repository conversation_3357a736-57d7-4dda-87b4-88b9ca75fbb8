import { test } from '@playwright/test';

test('Production sync test - no mock data', async ({ page }) => {
  // Capture console logs
  const consoleLogs: string[] = [];
  
  page.on('console', msg => {
    const text = msg.text();
    consoleLogs.push(text);
    console.log('CONSOLE:', text);
  });

  // Navigate to dashboard
  console.log('🚀 Loading dashboard...');
  await page.goto('http://localhost:3001');
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);

  // Find and click sync button
  const syncButton = page.locator('button:has-text("Sync")');
  if (await syncButton.isVisible()) {
    console.log('✅ Sync button found');
    await syncButton.click();
    console.log('🔄 Clicked sync button');
    
    // Wait for API calls
    await page.waitForTimeout(2000);
    
    // Check console logs for mock data messages
    const mockMessages = consoleLogs.filter(log => 
      log.includes('mock') || log.includes('Mock')
    );
    
    if (mockMessages.length > 0) {
      console.log('❌ FOUND MOCK DATA MESSAGES:');
      mockMessages.forEach(msg => console.log('  -', msg));
    } else {
      console.log('✅ NO MOCK DATA MESSAGES FOUND');
    }
    
    // Check for API calls
    const apiMessages = consoleLogs.filter(log => 
      log.includes('sync') || log.includes('Sync') || 
      log.includes('HTTP error') || log.includes('Failed')
    );
    
    console.log('🌐 API RELATED MESSAGES:');
    apiMessages.forEach(msg => console.log('  -', msg));
    
  } else {
    console.log('❌ Sync button not found');
  }
});