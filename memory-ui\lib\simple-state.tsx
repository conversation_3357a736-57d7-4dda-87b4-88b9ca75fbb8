"use client"

import { createContext, useContext, useState, ReactNode } from 'react'

// Lightweight state management for dashboard instead of Redux
interface DashboardState {
  memories: any[]
  isLoading: boolean
  syncStatus: string
  lastSync: string | null
}

interface DashboardContextType {
  state: DashboardState
  updateMemories: (memories: any[]) => void
  setLoading: (loading: boolean) => void
  setSyncStatus: (status: string) => void
  setLastSync: (sync: string) => void
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined)

export function DashboardProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<DashboardState>({
    memories: [],
    isLoading: false,
    syncStatus: 'idle',
    lastSync: null
  })

  const updateMemories = (memories: any[]) => {
    setState(prev => ({ ...prev, memories }))
  }

  const setLoading = (isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }))
  }

  const setSyncStatus = (syncStatus: string) => {
    setState(prev => ({ ...prev, syncStatus }))
  }

  const setLastSync = (lastSync: string) => {
    setState(prev => ({ ...prev, lastSync }))
  }

  const value = {
    state,
    updateMemories,
    setLoading,
    setSyncStatus,
    setLastSync
  }

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  )
}

export function useDashboard() {
  const context = useContext(DashboardContext)
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider')
  }
  return context
}