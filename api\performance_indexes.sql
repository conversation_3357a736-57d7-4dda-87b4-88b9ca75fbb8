-- Performance Optimization Indexes for Memory Master v2
-- Run this script on your PostgreSQL database to improve query performance

-- Create indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_user_app_state 
    ON memory_master.memories(user_id, app_id, state);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_created_at_desc 
    ON memory_master.memories(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_user_created_at 
    ON memory_master.memories(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_app_created_at 
    ON memory_master.memories(app_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evolution_user_app_date 
    ON memory_master.evolution_operations(user_id, app_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_evolution_operation_type 
    ON memory_master.evolution_operations(operation_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_categories_memory_id 
    ON memory_master.memory_categories(memory_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_categories_category_id 
    ON memory_master.memory_categories(category_id);

-- Add composite indexes for complex queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_user_app_state_created 
    ON memory_master.memories(user_id, app_id, state, created_at DESC);

-- Add index for text search if needed
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_content_gin 
    ON memory_master.memories USING gin(to_tsvector('english', content));

-- Add index for memory access logs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_access_log_memory_id 
    ON memory_master.memory_access_logs(memory_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_access_log_user_id 
    ON memory_master.memory_access_logs(user_id);

-- Add index for memory status history
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_memory_status_history_memory_id 
    ON memory_master.memory_status_history(memory_id);

-- Analyze tables to update statistics
ANALYZE memory_master.memories;
ANALYZE memory_master.evolution_operations;
ANALYZE memory_master.memory_categories;
ANALYZE memory_master.memory_access_logs;
ANALYZE memory_master.memory_status_history;

-- Display index usage statistics
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'memory_master'
ORDER BY idx_tup_read DESC;