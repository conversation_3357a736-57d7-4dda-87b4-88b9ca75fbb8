# Multi-stage build for production
FROM python:3.12-slim AS base

LABEL org.opencontainers.image.name="memory-master/api"
LABEL org.opencontainers.image.description="Memory Master API - Production"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd --gid 1001 appgroup
RUN useradd --uid 1001 --gid appgroup --shell /bin/bash --create-home appuser

WORKDIR /usr/src/app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set ownership
RUN chown -R appuser:appgroup /usr/src/app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8765/health || exit 1

# Expose port
EXPOSE 8765

# Set environment
ENV PYTHONPATH=/usr/src/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Start the application with production settings
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8765", "--workers", "4"]