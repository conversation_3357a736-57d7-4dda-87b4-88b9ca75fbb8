// Test script to interact with the user switcher
const puppeteer = require('puppeteer');

async function testUserSwitching() {
    const browser = await puppeteer.launch({
        headless: false, // Run in visible mode to see what's happening
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        devtools: true // Open devtools to see console errors
    });

    try {
        const page = await browser.newPage();
        
        // Listen for console messages
        page.on('console', msg => {
            console.log('PAGE LOG:', msg.text());
        });
        
        // Listen for errors
        page.on('pageerror', error => {
            console.log('PAGE ERROR:', error.message);
        });
        
        console.log('Navigating to memories page...');
        await page.goto('http://localhost:3001/memories', { waitUntil: 'networkidle2' });
        
        // Wait for the page to load
        await page.waitForTimeout(3000);
        
        console.log('Taking initial screenshot...');
        await page.screenshot({ path: '/tmp/puppeteer_initial.png' });
        
        // Check current user state
        const currentUser = await page.evaluate(() => {
            return {
                // Try to find the current user display
                currentUserText: document.querySelector('[aria-label="Demo User Switcher"] + * span')?.textContent || 'Not found',
                authMessage: document.querySelector('h2')?.textContent || 'Not found'
            };
        });
        
        console.log('Current state:', currentUser);
        
        // Try to click the "Aung Hein Aye" button
        console.log('Looking for Aung Hein Aye button...');
        
        const buttonClicked = await page.evaluate(() => {
            // Find all buttons
            const buttons = Array.from(document.querySelectorAll('button'));
            const aungButton = buttons.find(button => 
                button.textContent.includes('Aung Hein Aye')
            );
            
            if (aungButton) {
                console.log('Found Aung Hein Aye button, clicking...');
                aungButton.click();
                return true;
            } else {
                console.log('Aung Hein Aye button not found');
                console.log('Available buttons:', buttons.map(b => b.textContent.substring(0, 50)));
                return false;
            }
        });
        
        console.log('Button clicked:', buttonClicked);
        
        if (buttonClicked) {
            // Wait for state change
            await page.waitForTimeout(2000);
            
            console.log('Taking screenshot after click...');
            await page.screenshot({ path: '/tmp/puppeteer_after_click.png' });
            
            // Check new state
            const newState = await page.evaluate(() => {
                return {
                    currentUserText: document.querySelector('[aria-label="Demo User Switcher"] + * span')?.textContent || 'Not found',
                    authMessage: document.querySelector('h2')?.textContent || 'Not found',
                    hasMemoriesHeader: !!document.querySelector('h1')?.textContent.includes('Memories')
                };
            });
            
            console.log('New state after click:', newState);
        }
        
        // Keep browser open for manual inspection
        console.log('Browser will stay open for 10 seconds for manual inspection...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await browser.close();
    }
}

// Run the test
testUserSwitching().catch(console.error);