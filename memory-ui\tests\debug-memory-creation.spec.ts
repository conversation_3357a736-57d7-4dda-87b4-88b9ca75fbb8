import { test, expect } from '@playwright/test';

test.describe('Debug Memory Creation', () => {
  test('should inspect the create memory dialog structure', async ({ page }) => {
    // Navigate to memories page
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot before clicking
    await page.screenshot({ path: 'test-results/debug-01-before-click.png' });
    
    // Click Create Memory button
    const createMemoryButton = page.locator('button:has-text("Create Memory")').first();
    await expect(createMemoryButton).toBeVisible({ timeout: 10000 });
    await createMemoryButton.click();
    
    // Wait a bit for the dialog to appear
    await page.waitForTimeout(2000);
    
    // Take screenshot after clicking
    await page.screenshot({ path: 'test-results/debug-02-after-click.png' });
    
    // Check for dialog
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    
    // Take screenshot of dialog
    await page.screenshot({ path: 'test-results/debug-03-dialog-opened.png' });
    
    // Print all input elements
    const inputs = await page.locator('input').all();
    console.log('Found inputs:', inputs.length);
    
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const name = await input.getAttribute('name');
      const placeholder = await input.getAttribute('placeholder');
      const type = await input.getAttribute('type');
      console.log(`Input ${i}: name="${name}", placeholder="${placeholder}", type="${type}"`);
    }
    
    // Print all textarea elements
    const textareas = await page.locator('textarea').all();
    console.log('Found textareas:', textareas.length);
    
    for (let i = 0; i < textareas.length; i++) {
      const textarea = textareas[i];
      const name = await textarea.getAttribute('name');
      const placeholder = await textarea.getAttribute('placeholder');
      console.log(`Textarea ${i}: name="${name}", placeholder="${placeholder}"`);
    }
    
    // Print all form elements
    const forms = await page.locator('form').all();
    console.log('Found forms:', forms.length);
    
    // Print all labels
    const labels = await page.locator('label').all();
    console.log('Found labels:', labels.length);
    
    for (let i = 0; i < labels.length; i++) {
      const label = labels[i];
      const text = await label.textContent();
      const forAttr = await label.getAttribute('for');
      console.log(`Label ${i}: text="${text}", for="${forAttr}"`);
    }
    
    // Print the full dialog HTML
    const dialog = page.locator('[role="dialog"]').first();
    const dialogHTML = await dialog.innerHTML();
    console.log('Dialog HTML:', dialogHTML);
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/debug-04-final.png' });
  });
});