#!/usr/bin/env node
/**
 * Bundle Analysis Script for Memory Master UI
 * Analyzes webpack output to identify heavy dependencies and modules
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class BundleAnalyzer {
  constructor() {
    this.buildOutput = '';
    this.moduleCount = 0;
    this.compileTime = 0;
    this.bundleSize = 0;
  }

  parseBuildOutput(output) {
    // Extract module count
    const moduleMatch = output.match(/✓ Compiled \/.* in [\d.]+s \((\d+) modules\)/);
    if (moduleMatch) {
      this.moduleCount = parseInt(moduleMatch[1]);
    }

    // Extract compile time
    const timeMatch = output.match(/✓ Compiled \/.* in ([\d.]+)s/);
    if (timeMatch) {
      this.compileTime = parseFloat(timeMatch[1]);
    }

    // Extract bundle info
    const bundleMatch = output.match(/First Load JS.*?(\d+(?:\.\d+)?)\s*kB/);
    if (bundleMatch) {
      this.bundleSize = parseFloat(bundleMatch[1]);
    }
  }

  async runAnalysis() {
    console.log('🔍 Bundle Analysis - Memory Master UI');
    console.log('=====================================');
    
    return new Promise((resolve, reject) => {
      // Run build to get compilation metrics
      const buildProcess = spawn('npm', ['run', 'build'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        shell: true
      });

      buildProcess.stdout.on('data', (data) => {
        const output = data.toString();
        this.buildOutput += output;
        process.stdout.write(output);
        this.parseBuildOutput(output);
      });

      buildProcess.stderr.on('data', (data) => {
        const output = data.toString();
        this.buildOutput += output;
        process.stderr.write(output);
        this.parseBuildOutput(output);
      });

      buildProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with code ${code}`));
        }
      });
    });
  }

  analyzePackageJson() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = Object.keys(packageJson.dependencies || {});
    const devDependencies = Object.keys(packageJson.devDependencies || {});

    // Identify remaining heavy dependencies (removed framer-motion, recharts, lodash, redux)
    const heavyDeps = [
      '@tanstack/react-query',
      'three',
      'd3'
    ];

    const foundHeavyDeps = dependencies.filter(dep => 
      heavyDeps.some(heavy => dep.includes(heavy))
    );

    return {
      totalDeps: dependencies.length,
      totalDevDeps: devDependencies.length,
      heavyDeps: foundHeavyDeps,
      radixComponents: dependencies.filter(dep => dep.startsWith('@radix-ui')).length
    };
  }

  generateReport() {
    const packageAnalysis = this.analyzePackageJson();
    
    console.log('\\n\\n📊 BUNDLE ANALYSIS REPORT');
    console.log('=========================');
    
    // Compilation metrics
    console.log('\\n🏗️  COMPILATION METRICS:');
    console.log(`  Module Count: ${this.moduleCount || 'N/A'}`);
    console.log(`  Compile Time: ${this.compileTime || 'N/A'}s`);
    console.log(`  Bundle Size: ${this.bundleSize || 'N/A'}kB`);
    
    // Dependency analysis
    console.log('\\n📦 DEPENDENCY ANALYSIS:');
    console.log(`  Total Dependencies: ${packageAnalysis.totalDeps}`);
    console.log(`  Dev Dependencies: ${packageAnalysis.totalDevDeps}`);
    console.log(`  Radix UI Components: ${packageAnalysis.radixComponents}`);
    console.log(`  Heavy Dependencies: ${packageAnalysis.heavyDeps.join(', ') || 'None'}`);
    
    // Performance assessment
    console.log('\\n⚡ PERFORMANCE ASSESSMENT:');
    
    if (this.moduleCount > 1000) {
      console.log('  ❌ Module Count: EXCESSIVE (>1000 modules)');
      console.log('     Target: Reduce to 300-500 modules');
    } else if (this.moduleCount > 500) {
      console.log('  ⚠️  Module Count: HIGH (>500 modules)');
      console.log('     Target: Reduce to 300-400 modules');
    } else {
      console.log('  ✅ Module Count: GOOD (<500 modules)');
    }
    
    if (this.compileTime > 20) {
      console.log('  ❌ Compile Time: SLOW (>20s)');
      console.log('     Target: Reduce to <10s');
    } else if (this.compileTime > 10) {
      console.log('  ⚠️  Compile Time: MODERATE (>10s)');
      console.log('     Target: Reduce to <8s');
    } else {
      console.log('  ✅ Compile Time: FAST (<10s)');
    }
    
    if (this.bundleSize > 200) {
      console.log('  ❌ Bundle Size: LARGE (>200kB)');
      console.log('     Target: Reduce to <150kB');
    } else if (this.bundleSize > 150) {
      console.log('  ⚠️  Bundle Size: MODERATE (>150kB)');
      console.log('     Target: Reduce to <120kB');
    } else {
      console.log('  ✅ Bundle Size: GOOD (<150kB)');
    }
    
    // Optimization recommendations
    console.log('\\n💡 OPTIMIZATION RECOMMENDATIONS:');
    
    if (packageAnalysis.heavyDeps.length > 0) {
      console.log('  🔧 Remove or replace heavy dependencies:');
      packageAnalysis.heavyDeps.forEach(dep => {
        console.log(`     - ${dep}: Consider dynamic import or alternative`);
      });
    }
    
    if (packageAnalysis.radixComponents > 5) {
      console.log('  🔧 Reduce Radix UI components:');
      console.log('     - Use only essential components');
      console.log('     - Consider custom lightweight alternatives');
    }
    
    if (this.moduleCount > 500) {
      console.log('  🔧 Reduce module count:');
      console.log('     - Implement dynamic imports for heavy components');
      console.log('     - Remove unused dependencies');
      console.log('     - Optimize import statements');
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      metrics: {
        moduleCount: this.moduleCount,
        compileTime: this.compileTime,
        bundleSize: this.bundleSize
      },
      dependencies: packageAnalysis,
      rawOutput: this.buildOutput
    };
    
    fs.writeFileSync('bundle-analysis-report.json', JSON.stringify(report, null, 2));
    console.log('\\n📄 Detailed report saved to: bundle-analysis-report.json');
  }

  async run() {
    try {
      await this.runAnalysis();
      this.generateReport();
    } catch (error) {
      console.error('❌ Bundle analysis failed:', error.message);
      process.exit(1);
    }
  }
}

// Run analysis
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.run();
}