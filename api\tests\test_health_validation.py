import pytest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from app.utils.memory import MemoryClientSingleton


class TestHealthValidation:
    """Test suite for client health validation functionality."""
    
    def setup_method(self):
        """Reset singleton before each test."""
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._lock = threading.Lock()
    
    def test_is_client_healthy_no_client(self):
        """Test health check when no client exists."""
        singleton = MemoryClientSingleton()
        assert not singleton._is_client_healthy()
    
    def test_is_client_healthy_no_config(self):
        """Test health check when client has no config."""
        singleton = MemoryClientSingleton()
        singleton._client = Mock()
        del singleton._client.config  # Remove config attribute
        
        assert not singleton._is_client_healthy()
    
    def test_is_client_healthy_frequency_skip(self):
        """Test that health checks are skipped when called too frequently."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.return_value = []
        singleton._client = mock_client
        singleton._last_health_check = time.time()  # Recent check
        
        # Should skip the actual health check due to frequency limit
        result = singleton._is_client_healthy()
        assert result is True  # Returns True when skipping
        mock_client.search.assert_not_called()
    
    def test_is_client_healthy_vector_store_success(self):
        """Test successful vector store health check."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.return_value = []
        singleton._client = mock_client
        singleton._last_health_check = None  # Force health check
        
        result = singleton._is_client_healthy()
        assert result is True
        mock_client.search.assert_called_once_with(query="", limit=1)
        assert singleton._last_health_check is not None
    
    def test_is_client_healthy_vector_store_failure(self):
        """Test vector store health check failure."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.side_effect = Exception("Connection failed")
        singleton._client = mock_client
        singleton._last_health_check = None  # Force health check
        
        result = singleton._is_client_healthy()
        assert result is False
        mock_client.search.assert_called_once_with(query="", limit=1)
    
    def test_is_client_healthy_temporary_unavailability(self):
        """Test handling of temporary vector store unavailability."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.side_effect = Exception("Temporary unavailable")
        singleton._client = mock_client
        singleton._last_health_check = None
        
        with patch('builtins.print') as mock_print:
            result = singleton._is_client_healthy()
            assert result is False
            # Check that appropriate message was printed
            mock_print.assert_any_call("Vector store temporarily unavailable, marking as unhealthy")
    
    def test_public_is_healthy_method(self):
        """Test the public is_healthy method."""
        singleton = MemoryClientSingleton()
        
        # No client
        assert not singleton.is_healthy()
        
        # Healthy client
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.return_value = []
        singleton._client = mock_client
        singleton._last_health_check = None
        
        assert singleton.is_healthy()
    
    def test_get_health_status_no_client(self):
        """Test health status when no client exists."""
        singleton = MemoryClientSingleton()
        status = singleton.get_health_status()
        
        assert status["healthy"] is False
        assert status["client_exists"] is False
        assert "No client instance" in status["details"]
    
    def test_get_health_status_no_config(self):
        """Test health status when client has no config."""
        singleton = MemoryClientSingleton()
        singleton._client = Mock()
        del singleton._client.config
        
        status = singleton.get_health_status()
        
        assert status["healthy"] is False
        assert status["client_exists"] is True
        assert "Client missing configuration" in status["details"]
    
    def test_get_health_status_no_vector_store(self):
        """Test health status when vector store is not initialized."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        del mock_client.vector_store
        singleton._client = mock_client
        
        status = singleton.get_health_status()
        
        assert status["healthy"] is False
        assert status["client_exists"] is True
        assert "Vector store not initialized" in status["details"]
    
    def test_get_health_status_healthy(self):
        """Test health status for a healthy client."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.vector_store = Mock()
        mock_client.search.return_value = []
        singleton._client = mock_client
        
        status = singleton.get_health_status()
        
        assert status["healthy"] is True
        assert status["client_exists"] is True
        assert "All health checks passed" in status["details"]
    
    def test_get_health_status_connectivity_failure(self):
        """Test health status when connectivity test fails."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.vector_store = Mock()
        mock_client.search.side_effect = Exception("Connection error")
        singleton._client = mock_client
        
        status = singleton.get_health_status()
        
        assert status["healthy"] is False
        assert status["client_exists"] is True
        assert "Vector store connectivity failed: Connection error" in status["details"]
    
    def test_attempt_recovery_no_client(self):
        """Test recovery attempt when no client exists."""
        singleton = MemoryClientSingleton()
        config_dict = {"test": "config"}
        
        result = singleton._attempt_recovery(config_dict)
        assert result is False
    
    def test_attempt_recovery_automatic_success(self):
        """Test recovery when client recovers automatically."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.return_value = []
        singleton._client = mock_client
        singleton._last_health_check = None
        
        with patch('builtins.print') as mock_print:
            result = singleton._attempt_recovery({"test": "config"})
            assert result is True
            mock_print.assert_any_call("Client recovered automatically")
    
    def test_attempt_recovery_vector_store_reconnection(self):
        """Test recovery through vector store reconnection."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = None  # Make initial health check fail
        mock_client.vector_store = Mock()
        mock_client.search.return_value = []  # But search works for recovery
        singleton._client = mock_client
        singleton._last_health_check = None
        
        with patch('builtins.print') as mock_print:
            with patch.object(singleton, '_is_client_healthy', side_effect=[False, True]):
                result = singleton._attempt_recovery({"test": "config"})
                assert result is True
                mock_print.assert_any_call("Vector store reconnection successful")
    
    @patch('app.utils.memory.Memory')
    def test_attempt_recovery_full_client_recovery(self, mock_memory_class):
        """Test full client recovery."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = None
        mock_client.vector_store = None
        singleton._client = mock_client
        
        # Mock the Memory.from_config to return a new client
        new_mock_client = Mock()
        mock_memory_class.from_config.return_value = new_mock_client
        
        with patch('builtins.print') as mock_print:
            with patch.object(singleton, '_is_client_healthy', return_value=False):
                with patch('signal.signal'), patch('signal.alarm'):
                    result = singleton._attempt_recovery({"test": "config"})
                    assert result is True
                    assert singleton._client == new_mock_client
                    mock_print.assert_any_call("Full client recovery successful")
    
    def test_attempt_recovery_failure(self):
        """Test recovery failure scenarios."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = None
        mock_client.vector_store = Mock()
        mock_client.search.side_effect = Exception("Recovery failed")
        singleton._client = mock_client
        
        with patch('builtins.print') as mock_print:
            with patch.object(singleton, '_is_client_healthy', return_value=False):
                with patch('app.utils.memory.Memory') as mock_memory:
                    # Make full recovery also fail
                    mock_memory.from_config.side_effect = Exception("Full recovery failed")
                    result = singleton._attempt_recovery({"test": "config"})
                    assert result is False
                    mock_print.assert_any_call("Vector store recovery failed: Recovery failed")
                    mock_print.assert_any_call("Full client recovery failed: Full recovery failed")
    
    def test_health_check_integration_with_get_client(self):
        """Test health check integration with get_client method."""
        singleton = MemoryClientSingleton()
        
        # Mock an unhealthy client that should trigger reinitialization
        unhealthy_client = Mock()
        unhealthy_client.config = None  # Make it unhealthy
        singleton._client = unhealthy_client
        singleton._config_hash = "old_hash"
        
        config_dict = {
            "vector_store": {"provider": "test"},
            "llm": {"provider": "test"},
            "embedder": {"provider": "test"}
        }
        
        with patch('app.utils.memory.Memory') as mock_memory:
            healthy_client = Mock()
            healthy_client.config = {"test": "config"}
            mock_memory.from_config.return_value = healthy_client
            
            result = singleton.get_client(config_dict)
            
            # Should have created a new client due to health check failure
            assert result == healthy_client
            assert singleton._client == healthy_client
    
    def test_concurrent_health_checks(self):
        """Test concurrent health checks don't interfere with each other."""
        singleton = MemoryClientSingleton()
        mock_client = Mock()
        mock_client.config = {"test": "config"}
        mock_client.search.return_value = []
        singleton._client = mock_client
        singleton._last_health_check = None
        
        results = []
        
        def check_health():
            results.append(singleton.is_healthy())
        
        threads = [threading.Thread(target=check_health) for _ in range(5)]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # All health checks should succeed
        assert all(results)
        assert len(results) == 5