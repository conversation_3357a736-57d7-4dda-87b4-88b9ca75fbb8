import { test, expect } from '@playwright/test';

test.describe('Memory Master v2 - Simplified E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Give time for initial load
  });

  test('1. Initial State - Dashboard loads with real data', async ({ page }) => {
    console.log('🔍 Starting initial state test...');
    
    // Take screenshot of initial state
    await page.screenshot({ 
      path: 'tests/screenshots/01-initial-dashboard.png',
      fullPage: true 
    });
    
    // Check that we have the welcome message
    await expect(page.locator('h1')).toContainText('Welcome back');
    console.log('✅ Welcome message found');
    
    // Check that we have memory stats
    const memoryStatCard = page.locator('text="Total Memories"').first();
    await expect(memoryStatCard).toBeVisible();
    console.log('✅ Memory stats card visible');
    
    // Check system health section
    await expect(page.locator('text="Backend System Health"')).toBeVisible();
    console.log('✅ System health section visible');
    
    // Check that we have real data (not loading state)
    const totalMemoriesValue = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('📊 Total memories displayed:', totalMemoriesValue);
    
    // Verify we have the Demo User Switcher
    await expect(page.locator('text="Demo User Switcher"')).toBeVisible();
    console.log('✅ Demo User Switcher visible');
    
    console.log('🎉 Initial state test completed successfully!');
  });

  test('2. User Switching - Switch to Aung Hein Aye', async ({ page }) => {
    console.log('🔄 Starting user switching test...');
    
    // Wait for user switcher to be visible
    await expect(page.locator('text="Demo User Switcher"')).toBeVisible();
    
    // Find the user switcher button for Aung Hein Aye (in the sidebar)
    const aungSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Aung Hein Aye")').first();
    await expect(aungSwitcherButton).toBeVisible();
    await aungSwitcherButton.click();
    
    // Wait for the switch to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot after switching
    await page.screenshot({ 
      path: 'tests/screenshots/02-switched-to-aung.png',
      fullPage: true 
    });
    
    // Verify the welcome message shows Aung's name
    await expect(page.locator('h1')).toContainText('Aung Hein Aye');
    console.log('✅ Welcome message updated to show Aung Hein Aye');
    
    // Check that memory count is substantial (real data)
    const memoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('📊 Memory count for Aung:', memoryCount);
    
    // Verify we have a good number of memories
    const numericCount = parseInt(memoryCount?.replace(/[,\s]/g, '') || '0');
    expect(numericCount).toBeGreaterThan(0);
    console.log('✅ Aung has', numericCount, 'memories');
    
    console.log('🎉 User switching test completed successfully!');
  });

  test('3. Switch to Yohanna - Empty State', async ({ page }) => {
    console.log('👤 Starting Yohanna switch test...');
    
    // Switch to Yohanna
    const yohannaSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Yohanna")').first();
    await expect(yohannaSwitcherButton).toBeVisible();
    await yohannaSwitcherButton.click();
    
    // Wait for the switch to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot after switching
    await page.screenshot({ 
      path: 'tests/screenshots/03-switched-to-yohanna.png',
      fullPage: true 
    });
    
    // Verify the welcome message shows Yohanna's name
    await expect(page.locator('h1')).toContainText('Yohanna');
    console.log('✅ Welcome message updated to show Yohanna');
    
    // Check that memory count is 0 (empty state)
    const memoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('📊 Memory count for Yohanna:', memoryCount);
    
    // Verify Yohanna has 0 memories
    expect(memoryCount).toBe('0');
    console.log('✅ Yohanna correctly shows 0 memories');
    
    console.log('🎉 Yohanna switch test completed successfully!');
  });

  test('4. Switch Back to Aung - Data Returns', async ({ page }) => {
    console.log('🔄 Starting switch back to Aung test...');
    
    // First switch to Yohanna
    const yohannaSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Yohanna")').first();
    await yohannaSwitcherButton.click();
    await page.waitForTimeout(1000);
    
    // Then switch back to Aung
    const aungSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Aung Hein Aye")').first();
    await aungSwitcherButton.click();
    await page.waitForTimeout(2000);
    
    // Take screenshot after switching back
    await page.screenshot({ 
      path: 'tests/screenshots/04-switched-back-to-aung.png',
      fullPage: true 
    });
    
    // Verify the welcome message shows Aung's name
    await expect(page.locator('h1')).toContainText('Aung Hein Aye');
    console.log('✅ Welcome message updated to show Aung Hein Aye');
    
    // Check that memory count is substantial again
    const memoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('📊 Memory count for Aung (after switch back):', memoryCount);
    
    // Verify we have a good number of memories again
    const numericCount = parseInt(memoryCount?.replace(/[,\s]/g, '') || '0');
    expect(numericCount).toBeGreaterThan(0);
    console.log('✅ Aung still has', numericCount, 'memories after switching back');
    
    console.log('🎉 Switch back test completed successfully!');
  });

  test('5. Navigation - Memories Page', async ({ page }) => {
    console.log('🧭 Starting navigation test...');
    
    // Ensure we're on Aung's account (who has data)
    const aungSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Aung Hein Aye")').first();
    await aungSwitcherButton.click();
    await page.waitForTimeout(1000);
    
    // Navigate to memories page
    await page.goto('/memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take screenshot of memories page
    await page.screenshot({ 
      path: 'tests/screenshots/05-memories-page.png',
      fullPage: true 
    });
    
    // Verify we're on the memories page
    await expect(page.locator('h1')).toContainText('Memories');
    console.log('✅ On memories page');
    
    // Check that we have the Create Memory button
    await expect(page.locator('button:has-text("Create Memory")')).toBeVisible();
    console.log('✅ Create Memory button visible');
    
    // Check that we have search functionality
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible();
    console.log('✅ Search input visible');
    
    // Check that we have memory stats
    await expect(page.locator('text="Total Memories"')).toBeVisible();
    console.log('✅ Memory stats visible');
    
    // Check if we have memory items or empty state
    const memoryItems = page.locator('div:has-text("Created by")');
    const emptyState = page.locator('text="No memories found"');
    
    const hasMemoryItems = await memoryItems.count() > 0;
    const hasEmptyState = await emptyState.count() > 0;
    
    if (hasMemoryItems) {
      console.log('✅ Memory items found on page');
    } else if (hasEmptyState) {
      console.log('✅ Empty state properly displayed');
    } else {
      console.log('⚠️ Neither memory items nor empty state found');
    }
    
    console.log('🎉 Navigation test completed successfully!');
  });

  test('6. Memory Creation Dialog', async ({ page }) => {
    console.log('➕ Starting memory creation test...');
    
    // Ensure we're on Aung's account
    const aungSwitcherButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Aung Hein Aye")').first();
    await aungSwitcherButton.click();
    await page.waitForTimeout(1000);
    
    // Navigate to memories page
    await page.goto('/memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Click Create Memory button
    await page.locator('button:has-text("Create Memory")').click();
    await page.waitForTimeout(1000);
    
    // Take screenshot of create dialog
    await page.screenshot({ 
      path: 'tests/screenshots/06-create-memory-dialog.png',
      fullPage: true 
    });
    
    // Check if create dialog opened
    const dialogTitle = page.locator('text="Create New Memory"');
    await expect(dialogTitle).toBeVisible();
    console.log('✅ Create Memory dialog opened');
    
    // Check form fields
    await expect(page.locator('input[placeholder*="title"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder*="content"]')).toBeVisible();
    console.log('✅ Form fields visible');
    
    // Fill in test data
    await page.locator('input[placeholder*="title"]').fill('E2E Test Memory');
    await page.locator('textarea[placeholder*="content"]').fill('This memory was created during E2E testing to verify the complete functionality of the Memory Master v2 system.');
    
    // Take screenshot with filled form
    await page.screenshot({ 
      path: 'tests/screenshots/07-create-memory-form-filled.png',
      fullPage: true 
    });
    
    // Check if Create button is available
    const createButton = page.locator('button:has-text("Create Memory")').last();
    await expect(createButton).toBeVisible();
    console.log('✅ Create button visible');
    
    // Attempt to create (but don't wait for success due to API dependencies)
    await createButton.click();
    await page.waitForTimeout(2000);
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/08-after-create-attempt.png',
      fullPage: true 
    });
    
    console.log('🎉 Memory creation test completed!');
  });

  test('7. Multi-User System Flow', async ({ page }) => {
    console.log('🔀 Starting comprehensive multi-user test...');
    
    // Guest state
    const guestButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Guest User")').first();
    await guestButton.click();
    await page.waitForTimeout(1000);
    
    const guestMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('👤 Guest memory count:', guestMemoryCount);
    
    // Aung state
    const aungButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Aung Hein Aye")').first();
    await aungButton.click();
    await page.waitForTimeout(1000);
    
    const aungMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('👨‍💻 Aung memory count:', aungMemoryCount);
    
    // Yohanna state
    const yohannaButton = page.locator('div:has-text("Demo User Switcher")').locator('button:has-text("Yohanna")').first();
    await yohannaButton.click();
    await page.waitForTimeout(1000);
    
    const yohannaMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('👩‍💼 Yohanna memory count:', yohannaMemoryCount);
    
    // Take final comprehensive screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/09-multi-user-final.png',
      fullPage: true 
    });
    
    // Verify all users have appropriate states
    expect(guestMemoryCount).toBe('0');
    expect(yohannaMemoryCount).toBe('0');
    
    const aungCount = parseInt(aungMemoryCount?.replace(/[,\s]/g, '') || '0');
    expect(aungCount).toBeGreaterThan(0);
    
    console.log('🎉 Multi-user system test completed successfully!');
    console.log('📊 Final counts - Guest: 0, Aung:', aungCount, ', Yohanna: 0');
  });
});