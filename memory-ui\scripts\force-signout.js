// Force sign out all sessions
const { createClient } = require('@supabase/supabase-js')

const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://192.168.1.218:8000',
  SUPABASE_SERVICE_KEY
)

async function forceSignOut() {
  console.log('Force signing out all users...')
  
  try {
    // Sign out Aung's user
    const { error } = await supabase.auth.admin.signOut('310407fb-56fa-47a2-a534-5d836cdb23df')
    if (error) {
      console.error('Error signing out:', error)
    } else {
      console.log('Successfully signed out Aung')
    }
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

forceSignOut()