# Windows PowerShell Setup Script for Memory Master
param(
    [switch]$Force
)

Write-Host "🪟 Setting up Memory Master for Windows environment..." -ForegroundColor Cyan

# Check if Docker Desktop is installed
$dockerInstalled = Get-Command docker -ErrorAction SilentlyContinue
if (-not $dockerInstalled) {
    Write-Host "❌ Docker Desktop not found. Please install Docker Desktop for Windows first." -ForegroundColor Red
    Write-Host "   Download from: https://www.docker.com/products/docker-desktop/" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Set environment variables for Windows
$env:QDRANT_HOST = "host.docker.internal"
$env:SUPABASE_HOST = "host.docker.internal"
$env:API_HOST = "host.docker.internal"

# Create Docker environment file
$envContent = @"
# Platform: windows
PLATFORM=windows
QDRANT_HOST=$($env:QDRANT_HOST)
SUPABASE_HOST=$($env:SUPABASE_HOST)
API_URL=http://localhost:8765
EXTERNAL_QDRANT_IP=host-gateway
EXTERNAL_SUPABASE_IP=host-gateway
COMPOSE_PROJECT_NAME=memory-master
"@

Set-Content -Path ".env.docker" -Value $envContent
Write-Host "✅ Created .env.docker for Windows" -ForegroundColor Green

# Generate MCP configuration
$mcpTemplate = Get-Content ".mcp.template.json" -Raw
$mcpConfig = $mcpTemplate -replace "{{API_HOST}}", $env:API_HOST
Set-Content -Path ".mcp.json" -Value $mcpConfig
Write-Host "✅ Generated .mcp.json for Windows" -ForegroundColor Green

# Check if containers are already running
$runningContainers = docker ps --filter "name=memory-" --format "table {{.Names}}"
if ($runningContainers.Length -gt 1 -and -not $Force) {
    Write-Host "⚠️  Memory containers are already running:" -ForegroundColor Yellow
    Write-Host $runningContainers
    $response = Read-Host "Do you want to restart them? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "Skipping container restart" -ForegroundColor Yellow
        exit 0
    }
    docker-compose down
}

# Build and start containers
Write-Host "🚀 Building and starting containers..." -ForegroundColor Cyan
docker-compose --env-file .env.docker up -d --build

if ($LASTEXITCODE -eq 0) {
    Write-Host "🎉 Setup complete for Windows!" -ForegroundColor Green
    Write-Host "📡 Services:" -ForegroundColor Cyan
    Write-Host "   - API: http://localhost:8765" -ForegroundColor White
    Write-Host "   - UI: http://localhost:3210" -ForegroundColor White
    Write-Host "   - MCP: Configured in .mcp.json" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 To check status: docker-compose ps" -ForegroundColor Yellow
    Write-Host "📋 To view logs: docker-compose logs -f" -ForegroundColor Yellow
    Write-Host "🛑 To stop: docker-compose down" -ForegroundColor Yellow
} else {
    Write-Host "❌ Setup failed. Check the output above for errors." -ForegroundColor Red
    exit 1
}