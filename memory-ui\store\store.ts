import { configureStore } from '@reduxjs/toolkit';
import profileReducer from './profileSlice';
import memoriesReducer from './memoriesSlice';
import evolutionReducer from './evolutionSlice';

export const store = configureStore({
  reducer: {
    profile: profileReducer,
    memories: memoriesReducer,
    evolution: evolutionReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['profile/setUserFromAuth'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;