'use client'

import React, { useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '../supabase/client'
import { useAppDispatch, profileActions } from '../context/AppContext'

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const getInitialSession = async () => {
      dispatch(profileActions.setLoading(true))
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        // Handle refresh token errors specifically
        if (error && error.message?.includes('refresh_token_not_found')) {
          console.warn('Invalid refresh token found, clearing auth state')
          await supabase.auth.signOut()
          localStorage.clear()
          sessionStorage.clear()
          dispatch(profileActions.setUserFromAuth(null))
          router.push('/login')
          return
        }
        
        if (error) {
          console.error('Error getting session:', error)
        }
        
        dispatch(profileActions.setUserFromAuth(session?.user || null))
        
        // If no session, redirect to login
        if (!session) {
          router.push('/login')
        }
      } catch (error) {
        console.error('Error in auth initialization:', error)
        // Clear potentially corrupted auth state
        await supabase.auth.signOut()
        localStorage.clear()
        sessionStorage.clear()
        dispatch(profileActions.setUserFromAuth(null))
        router.push('/login')
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Handle token refresh errors
        if (event === 'TOKEN_REFRESHED' && !session) {
          console.warn('Token refresh failed, clearing auth state')
          localStorage.clear()
          sessionStorage.clear()
          dispatch(profileActions.setUserFromAuth(null))
          router.push('/login')
          return
        }
        
        dispatch(profileActions.setUserFromAuth(session?.user || null))
        
        // Handle auth events
        if (event === 'SIGNED_IN') {
          router.push('/')
        } else if (event === 'SIGNED_OUT') {
          router.push('/login')
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [dispatch, router, supabase])

  return <>{children}</>
}