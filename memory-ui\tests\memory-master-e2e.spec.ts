import { test, expect } from '@playwright/test';

test.describe('Memory Master v2 E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Give some time for any animations to complete
    await page.waitForTimeout(2000);
  });

  test('1. Initial State Verification - Real API Data', async ({ page }) => {
    // Check that we're on the dashboard page
    await expect(page.locator('h1')).toContainText('Welcome back');
    
    // Take screenshot of initial state
    await page.screenshot({ 
      path: 'tests/screenshots/01-initial-state.png',
      fullPage: true 
    });
    
    // Check that we have real data - look for memory count
    const totalMemoriesCard = page.locator('text="Total Memories"').locator('..').locator('p.text-3xl');
    await expect(totalMemoriesCard).toBeVisible();
    
    const memoryCount = await totalMemoriesCard.textContent();
    console.log('Initial memory count:', memoryCount);
    
    // Verify user info is displayed
    const userBadge = page.locator('text="User ID:"');
    await expect(userBadge).toBeVisible();
    
    // Check authentication status
    const authBadge = page.locator('text="Authenticated"');
    await expect(authBadge).toBeVisible();
    
    // Verify system health cards are showing
    await expect(page.locator('text="Backend System Health"')).toBeVisible();
    await expect(page.locator('text="Memory Engine"')).toBeVisible();
  });

  test('2. Manual User Switching Test - Current User to Aung Hein Aye', async ({ page }) => {
    // Find and click the Demo User Switcher section
    const userSwitcher = page.locator('text="Demo User Switcher"');
    await expect(userSwitcher).toBeVisible();
    
    // Check current user before switching
    const currentUserBadge = page.locator('text="Current:"').locator('..');
    const currentUser = await currentUserBadge.textContent();
    console.log('Current user before switch:', currentUser);
    
    // Click on "Aung Hein Aye" button
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await expect(aungButton).toBeVisible();
    await aungButton.click();
    
    // Wait for the switch to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot after switching
    await page.screenshot({ 
      path: 'tests/screenshots/02-switched-to-aung.png',
      fullPage: true 
    });
    
    // Verify user switched to Aung Hein Aye
    await expect(page.locator('h1')).toContainText('Welcome back, Aung Hein Aye');
    
    // Check that memory count shows real data (should be 191+ memories)
    const totalMemoriesCard = page.locator('text="Total Memories"').locator('..').locator('p.text-3xl');
    const memoryCount = await totalMemoriesCard.textContent();
    console.log('Memory count after switching to Aung:', memoryCount);
    
    // Verify we have a substantial number of memories
    const numericCount = parseInt(memoryCount?.replace(/[,\s]/g, '') || '0');
    expect(numericCount).toBeGreaterThan(0);
    
    // Check that user ID updated
    const userIdText = page.locator('text="User ID: aungheinaye"');
    await expect(userIdText).toBeVisible();
  });

  test('3. Switch to Yohanna Test - Verify Empty State', async ({ page }) => {
    // First ensure we're on Aung's account
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await aungButton.click();
    await page.waitForTimeout(1000);
    
    // Now switch to Yohanna
    const yohannaButton = page.locator('button:has-text("Yohanna")');
    await expect(yohannaButton).toBeVisible();
    await yohannaButton.click();
    
    // Wait for the switch to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot showing Yohanna's state
    await page.screenshot({ 
      path: 'tests/screenshots/03-switched-to-yohanna.png',
      fullPage: true 
    });
    
    // Verify user switched to Yohanna
    await expect(page.locator('h1')).toContainText('Welcome back, Yohanna');
    
    // Check that memory count shows 0 (empty state)
    const totalMemoriesCard = page.locator('text="Total Memories"').locator('..').locator('p.text-3xl');
    const memoryCount = await totalMemoriesCard.textContent();
    console.log('Memory count for Yohanna:', memoryCount);
    
    // Verify Yohanna has 0 memories
    expect(memoryCount).toBe('0');
    
    // Check that user ID updated
    const userIdText = page.locator('text="User ID: yohanna"');
    await expect(userIdText).toBeVisible();
    
    // Verify the UI properly handles empty state
    const memoriesSection = page.locator('text="All Memories"');
    await expect(memoriesSection).toBeVisible();
  });

  test('4. Switch Back to Aung Test - Verify 191+ Memories Return', async ({ page }) => {
    // Switch to Yohanna first
    const yohannaButton = page.locator('button:has-text("Yohanna")');
    await yohannaButton.click();
    await page.waitForTimeout(1000);
    
    // Now switch back to Aung
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await aungButton.click();
    
    // Wait for the switch to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot showing populated state
    await page.screenshot({ 
      path: 'tests/screenshots/04-switched-back-to-aung.png',
      fullPage: true 
    });
    
    // Verify user switched back to Aung
    await expect(page.locator('h1')).toContainText('Welcome back, Aung Hein Aye');
    
    // Check that memory count shows real data again
    const totalMemoriesCard = page.locator('text="Total Memories"').locator('..').locator('p.text-3xl');
    const memoryCount = await totalMemoriesCard.textContent();
    console.log('Memory count after switching back to Aung:', memoryCount);
    
    // Verify we have a substantial number of memories again
    const numericCount = parseInt(memoryCount?.replace(/[,\s]/g, '') || '0');
    expect(numericCount).toBeGreaterThan(0);
    
    // Check that user ID updated back
    const userIdText = page.locator('text="User ID: aungheinaye"');
    await expect(userIdText).toBeVisible();
  });

  test('5. Navigation Test - Memories Page with Real Data', async ({ page }) => {
    // Ensure we're on Aung's account (who has data)
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await aungButton.click();
    await page.waitForTimeout(1000);
    
    // Navigate to the memories page
    await page.goto('/memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot of memories page
    await page.screenshot({ 
      path: 'tests/screenshots/05-memories-page.png',
      fullPage: true 
    });
    
    // Verify we're on the memories page
    await expect(page.locator('h1')).toContainText('Memory Management');
    
    // Check that real memory content is displayed
    const memoryCards = page.locator('[data-testid="memory-item"]');
    const memoryCount = await memoryCards.count();
    console.log('Number of memory items on page:', memoryCount);
    
    // Verify we have memory items displayed
    expect(memoryCount).toBeGreaterThan(0);
    
    // Check that search functionality is present
    const searchInput = page.locator('input[placeholder*="Search"]');
    await expect(searchInput).toBeVisible();
    
    // Check that Create Memory button is present
    const createButton = page.locator('button:has-text("Create Memory")');
    await expect(createButton).toBeVisible();
  });

  test('6. Memory Creation Test', async ({ page }) => {
    // Ensure we're on Aung's account
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await aungButton.click();
    await page.waitForTimeout(1000);
    
    // Navigate to memories page
    await page.goto('/memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Click Create Memory button
    const createButton = page.locator('button:has-text("Create Memory")');
    await createButton.click();
    
    // Wait for dialog to open
    await page.waitForTimeout(1000);
    
    // Check if dialog opened (look for common dialog elements)
    const dialogTitle = page.locator('h2:has-text("Create Memory"), h3:has-text("Create Memory"), text="Add New Memory"');
    const titleInput = page.locator('input[placeholder*="title" i], input[name="title"]');
    const contentInput = page.locator('textarea[placeholder*="content" i], textarea[name="content"]');
    
    // Try to fill in the form if dialog elements are found
    if (await dialogTitle.count() > 0) {
      console.log('Memory creation dialog found');
      
      // Fill in test data
      if (await titleInput.count() > 0) {
        await titleInput.fill('E2E Test Memory');
      }
      
      if (await contentInput.count() > 0) {
        await contentInput.fill('This memory was created during E2E testing to verify the complete functionality');
      }
      
      // Look for submit button
      const submitButton = page.locator('button:has-text("Create"), button:has-text("Save"), button:has-text("Submit")');
      if (await submitButton.count() > 0) {
        await submitButton.click();
        
        // Wait for creation to complete
        await page.waitForTimeout(2000);
        
        // Take final screenshot
        await page.screenshot({ 
          path: 'tests/screenshots/06-after-memory-creation.png',
          fullPage: true 
        });
        
        // Check if memory was created successfully
        const successMessage = page.locator('text="Memory created", text="Success", text="Added successfully"');
        if (await successMessage.count() > 0) {
          console.log('Memory creation appeared to be successful');
        }
      } else {
        console.log('Submit button not found in create memory dialog');
      }
    } else {
      console.log('Memory creation dialog not found - taking screenshot of current state');
      await page.screenshot({ 
        path: 'tests/screenshots/06-create-memory-attempt.png',
        fullPage: true 
      });
    }
  });

  test('7. Multi-User System Comprehensive Test', async ({ page }) => {
    // Test the complete multi-user flow
    
    // Start with Guest user
    const guestButton = page.locator('button:has-text("Guest User")');
    await guestButton.click();
    await page.waitForTimeout(1000);
    
    // Verify guest state
    await expect(page.locator('h1')).toContainText('Welcome back, Guest User');
    const guestMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('Guest memory count:', guestMemoryCount);
    
    // Switch to Aung
    const aungButton = page.locator('button:has-text("Aung Hein Aye")');
    await aungButton.click();
    await page.waitForTimeout(1000);
    
    // Verify Aung's data
    await expect(page.locator('h1')).toContainText('Welcome back, Aung Hein Aye');
    const aungMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('Aung memory count:', aungMemoryCount);
    
    // Switch to Yohanna
    const yohannaButton = page.locator('button:has-text("Yohanna")');
    await yohannaButton.click();
    await page.waitForTimeout(1000);
    
    // Verify Yohanna's state
    await expect(page.locator('h1')).toContainText('Welcome back, Yohanna');
    const yohannaMemoryCount = await page.locator('text="Total Memories"').locator('..').locator('p.text-3xl').textContent();
    console.log('Yohanna memory count:', yohannaMemoryCount);
    
    // Take final comprehensive screenshot
    await page.screenshot({ 
      path: 'tests/screenshots/07-multi-user-final-state.png',
      fullPage: true 
    });
    
    // Verify all users have different states
    expect(guestMemoryCount).not.toBe(aungMemoryCount);
    expect(yohannaMemoryCount).toBe('0');
    
    console.log('Multi-user system test completed successfully');
  });
});