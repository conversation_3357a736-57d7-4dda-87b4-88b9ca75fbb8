# OpenAI API Configuration
OPENAI_API_KEY=sk-xxx

# User Configuration
# Set to 'aungheinaye' or 'yohanna' based on your user profile
# This determines user-specific settings and data isolation
USER=aungheinaye

# Qdrant Vector Database Configuration
# Remote Qdrant instance for vector storage and retrieval
QDRANT_HOST=*************
QDRANT_PORT=6333

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
SUPABASE_DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/postgres

# Authentication Feature Flag
AUTH_ENABLED=false

# Database Configuration - Now uses Supabase only
# SUPABASE_DATABASE_URL is required and should point to your Supabase PostgreSQL instance
DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/postgres

# Migration Mode
MIGRATION_MODE=supabase_only

# API Key for authentication
API_KEY=your_api_key_here

# MinIO Configuration for backup operations
MINIO_ENDPOINT=http://*************:9000
MINIO_CONSOLE=http://*************:9090