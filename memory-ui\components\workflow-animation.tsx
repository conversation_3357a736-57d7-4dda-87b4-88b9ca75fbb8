"use client"

import { useState, useEffect } from "react"
import { ArrowRight, Cpu, Database, CheckCircle } from "lucide-react"

const WorkflowAnimation = () => {
  const [activeStep, setActiveStep] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % 4)
    }, 2000)
    return () => clearInterval(interval)
  }, [])

  const steps = [
    { icon: Cpu, label: "Request", color: "from-blue-500 to-cyan-500" },
    { icon: Cpu, label: "Evolution", color: "from-purple-500 to-pink-500" },
    { icon: Database, label: "Memory", color: "from-green-500 to-emerald-500" },
    { icon: CheckCircle, label: "Saved", color: "from-amber-500 to-orange-500" },
  ]

  return (
    <div className="relative h-32 flex items-center justify-center">
      <div className="flex items-center gap-4">
        {steps.map((step, index) => {
          const Icon = step.icon
          const isActive = index === activeStep
          const isPassed = index < activeStep

          return (
            <div key={index} className="flex items-center">
              <div
                className={cn(
                  "relative w-16 h-16 rounded-full flex items-center justify-center",
                  "bg-gradient-to-br transition-all duration-300",
                  isActive ? step.color : "from-gray-700 to-gray-800",
                  isActive && "shadow-lg shadow-current/50 scale-110",
                  isPassed && "opacity-60"
                )}
              >
                <Icon className={cn("w-8 h-8", isActive ? "text-white" : "text-gray-400")} />
                {isActive && (
                  <div className="absolute inset-0 rounded-full bg-current opacity-20 animate-ping" />
                )}
              </div>
              <p
                className={cn(
                  "absolute -bottom-8 text-xs font-medium transition-opacity duration-300",
                  isActive ? "text-white opacity-100" : "text-gray-500 opacity-70"
                )}
              >
                {step.label}
              </p>
              {index < steps.length - 1 && (
                <div className="mx-2">
                  <ArrowRight
                    className={cn(
                      "w-6 h-6 transition-all duration-300",
                      isPassed || (index === activeStep - 1) ? "text-primary" : "text-gray-600"
                    )}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>
      
      {/* Data flow visualization */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none opacity-30">
        <svg className="w-full h-full">
          <circle
            cx={`${25 + activeStep * 25}%`}
            cy="50%"
            r="3"
            fill="currentColor"
            className="text-primary transition-all duration-[2000ms] ease-in-out"
          />
        </svg>
      </div>
    </div>
  )
}

function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(" ")
}

export default WorkflowAnimation