import { test, expect } from '@playwright/test';

test('Debug sync dashboard console errors', async ({ page }) => {
  // Capture console logs
  const consoleLogs: string[] = [];
  const consoleErrors: string[] = [];
  
  page.on('console', msg => {
    const text = msg.text();
    if (msg.type() === 'error') {
      consoleErrors.push(text);
      console.log('🔴 CONSOLE ERROR:', text);
    } else if (msg.type() === 'log') {
      consoleLogs.push(text);
      console.log('📝 CONSOLE LOG:', text);
    } else if (msg.type() === 'warning') {
      console.log('⚠️ CONSOLE WARNING:', text);
    }
  });

  // Capture network requests
  page.on('request', request => {
    console.log('🌐 REQUEST:', request.method(), request.url());
  });

  page.on('response', response => {
    const status = response.status();
    const url = response.url();
    if (status >= 400) {
      console.log('❌ FAILED RESPONSE:', status, url);
      // Log more details for 404s to identify the source
      if (status === 404) {
        console.log('🔍 404 Details - URL:', url);
      }
    } else {
      console.log('✅ SUCCESS RESPONSE:', status, url);
    }
  });

  // Navigate to dashboard
  console.log('🚀 Loading dashboard page...');
  await page.goto('http://localhost:3001');

  // Wait for page to load
  await page.waitForLoadState('domcontentloaded');

  // Wait a bit more to catch async operations
  await page.waitForTimeout(8000);

  // Take screenshot for debugging
  await page.screenshot({ path: 'test-results/sync-dashboard-debug.png', fullPage: true });

  // Check for sync status card
  const syncCard = page.locator('text=Database Sync');
  if (await syncCard.isVisible()) {
    console.log('✅ Sync status card is visible');
  } else {
    console.log('❌ Sync status card is NOT visible');
  }

  // Test sync button functionality
  const syncButton = page.locator('button:has-text("Sync")');
  if (await syncButton.isVisible()) {
    console.log('✅ Sync button found');
    
    // Click the sync button and monitor for changes
    await syncButton.click();
    console.log('🔄 Clicked sync button');
    
    // Wait a moment to see if sync animation starts
    await page.waitForTimeout(1000);
    
    // Check if button text changed to "Syncing..."
    const syncingButton = page.locator('button:has-text("Syncing...")');
    if (await syncingButton.isVisible()) {
      console.log('✅ Sync button shows "Syncing..." state');
    } else {
      console.log('❌ Sync button did not change to "Syncing..." state');
    }
  } else {
    console.log('❌ Sync button not found');
  }

  // Check for specific errors mentioned
  const syncErrors = consoleErrors.filter(error => 
    error.includes('Failed to fetch sync counts') || 
    error.includes('Failed to fetch sync data') ||
    error.includes('supabase.co')
  );

  console.log('\n📊 SUMMARY:');
  console.log('Total console errors:', consoleErrors.length);
  console.log('Total console logs:', consoleLogs.length);
  console.log('Sync-related errors:', syncErrors.length);

  if (syncErrors.length > 0) {
    console.log('\n🔍 SYNC ERRORS FOUND:');
    syncErrors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }

  // Log all errors for analysis
  if (consoleErrors.length > 0) {
    console.log('\n🔴 ALL CONSOLE ERRORS:');
    consoleErrors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
});