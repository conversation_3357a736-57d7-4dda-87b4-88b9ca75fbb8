"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  ActivityIcon,
  BrainIcon,  
  DatabaseIcon,
  RefreshIcon,
  SearchIcon,
  PlusIcon
} from "@/components/ui/icons"

// Simplified stats interface
interface Stats {
  totalMemories: number
  activeMemories: number
  categoriesCount: number
  syncStatus: string
}

// Lightweight memory interface
interface SimpleMemory {
  id: string
  content: string
  created_at: number
  categories: string[]
}

export function LightweightDashboard() {
  const [stats, setStats] = useState<Stats>({
    totalMemories: 0,
    activeMemories: 0,
    categoriesCount: 0,
    syncStatus: 'idle'
  })
  
  const [memories, setMemories] = useState<SimpleMemory[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Simplified data fetching without heavy React Query
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Basic fetch without heavy wrappers
        const response = await fetch('/api/v1/memories?limit=10')
        if (response.ok) {
          const data = await response.json()
          setMemories(data.items || [])
          setStats({
            totalMemories: data.total || 0,
            activeMemories: data.items?.length || 0,
            categoriesCount: 5, // Simplified
            syncStatus: 'synced'
          })
        }
      } catch (error) {
        console.error('Failed to fetch data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const filteredMemories = memories.filter(memory =>
    memory.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Memory Master</h1>
          <p className="text-muted-foreground">Manage your memories efficiently</p>
        </div>
        <Button className="gap-2">
          <PlusIcon className="w-4 h-4" />
          Add Memory
        </Button>
      </div>

      {/* Stats Cards - Simplified */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="border-border/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Memories</p>
                <p className="text-2xl font-bold">{stats.totalMemories}</p>
              </div>
              <BrainIcon className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold">{stats.activeMemories}</p>
              </div>
              <ActivityIcon className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Categories</p>
                <p className="text-2xl font-bold">{stats.categoriesCount}</p>
              </div>
              <DatabaseIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-border/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Sync Status</p>
                <p className="text-sm font-medium capitalize text-green-600">
                  {stats.syncStatus}
                </p>
              </div>
              <RefreshIcon className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Memory List - Simplified */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SearchIcon className="w-5 h-5" />
              Recent Memories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input
                placeholder="Search memories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-16 bg-muted animate-pulse rounded" />
                  ))}
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {filteredMemories.map((memory) => (
                    <div
                      key={memory.id}
                      className="p-3 rounded-lg bg-secondary/30 hover:bg-secondary/50 transition-colors"
                    >
                      <p className="text-sm line-clamp-2">{memory.content}</p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex gap-1">
                          {memory.categories.map((cat) => (
                            <span
                              key={cat}
                              className="px-2 py-1 text-xs bg-primary/10 text-primary rounded"
                            >
                              {cat}
                            </span>
                          ))}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(memory.created_at * 1000).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="border-border/50">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start gap-2">
                <PlusIcon className="w-4 h-4" />
                Add New Memory
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <SearchIcon className="w-4 h-4" />
                Search All Memories
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <RefreshIcon className="w-4 h-4" />
                Sync Database
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <DatabaseIcon className="w-4 h-4" />
                View Analytics
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}