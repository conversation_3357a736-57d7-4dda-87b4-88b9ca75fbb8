"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  User,
  Key,
  Database,
  Bell,
  Shield,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertCircle,
} from "lucide-react"
import { cn } from "@/lib/utils"

export default function SettingsPage() {
  const [apiKey, setApiKey] = useState("sk-proj-••••••••••••••••••••••••••••••••")
  const [maxMemoryLength, setMaxMemoryLength] = useState("2000")
  const [autoSync, setAutoSync] = useState(true)
  const [notifications, setNotifications] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = () => {
    setIsSaving(true)
    setTimeout(() => setIsSaving(false), 2000)
  }

  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-4xl font-bold gradient-text">Settings</h1>
        <p className="text-muted-foreground mt-2">Manage your Memory Master configuration</p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="api">API Configuration</TabsTrigger>
          <TabsTrigger value="backup">Backup</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure basic system preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="max-memory">Maximum Memory Length</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="max-memory"
                    type="number"
                    value={maxMemoryLength}
                    onChange={(e) => setMaxMemoryLength(e.target.value)}
                    className="max-w-[200px] bg-secondary/50"
                  />
                  <span className="text-sm text-muted-foreground">characters</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Text longer than this will be automatically chunked
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-sync">Automatic Synchronization</Label>
                  <p className="text-sm text-muted-foreground">
                    Sync memories between Supabase and Qdrant automatically
                  </p>
                </div>
                <Switch
                  id="auto-sync"
                  checked={autoSync}
                  onCheckedChange={setAutoSync}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifications">System Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive alerts for system events and errors
                  </p>
                </div>
                <Switch
                  id="notifications"
                  checked={notifications}
                  onCheckedChange={setNotifications}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Configuration */}
        <TabsContent value="api" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
              <CardDescription>Manage API keys and external service connections</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="openai-key">OpenAI API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="openai-key"
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    className="font-mono bg-secondary/50"
                  />
                  <Button variant="outline" size="icon">
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Required for memory embeddings and evolution intelligence
                </p>
              </div>

              <div className="p-4 rounded-lg bg-blue-500/10 border border-blue-500/50">
                <div className="flex gap-3">
                  <AlertCircle className="w-5 h-5 text-blue-500 flex-shrink-0" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">API Usage This Month</p>
                    <p className="text-sm text-muted-foreground">
                      Embeddings: 12,543 tokens • Cost: $0.02
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Service Status</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 rounded bg-secondary/30">
                    <span className="text-sm">OpenAI API</span>
                    <Badge variant="outline" className="bg-green-500/20 text-green-300 border-green-500/50">
                      Connected
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 rounded bg-secondary/30">
                    <span className="text-sm">Supabase</span>
                    <Badge variant="outline" className="bg-green-500/20 text-green-300 border-green-500/50">
                      Connected
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 rounded bg-secondary/30">
                    <span className="text-sm">Qdrant</span>
                    <Badge variant="outline" className="bg-green-500/20 text-green-300 border-green-500/50">
                      Connected
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Backup Settings */}
        <TabsContent value="backup" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
              <CardDescription>Configure backup settings and restore options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-secondary/30 border-border/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Download className="w-8 h-8 text-primary" />
                      <Badge variant="outline">Manual</Badge>
                    </div>
                    <h4 className="font-medium mb-1">Export Data</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Download all memories and settings
                    </p>
                    <Button className="w-full" size="sm">
                      Export Now
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-secondary/30 border-border/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Upload className="w-8 h-8 text-primary" />
                      <Badge variant="outline">Manual</Badge>
                    </div>
                    <h4 className="font-medium mb-1">Import Data</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Restore from a backup file
                    </p>
                    <Button className="w-full" size="sm" variant="outline">
                      Import File
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">Automated Backups</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 rounded bg-secondary/30">
                    <div>
                      <p className="text-sm font-medium">Daily Backups</p>
                      <p className="text-xs text-muted-foreground">Backup at 6:00 AM UTC</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between p-3 rounded bg-secondary/30">
                    <div>
                      <p className="text-sm font-medium">Backup Retention</p>
                      <p className="text-xs text-muted-foreground">Keep last 7 backups</p>
                    </div>
                    <Input
                      type="number"
                      defaultValue="7"
                      className="w-20 h-8 text-center bg-secondary/50"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Profile Settings */}
        <TabsContent value="profile" className="space-y-4">
          <Card className="glass border-border/50">
            <CardHeader>
              <CardTitle>User Profile</CardTitle>
              <CardDescription>Manage your account information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-4">
                <div className="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center">
                  <User className="w-10 h-10 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-medium">Aung</h3>
                  <p className="text-sm text-muted-foreground">Administrator</p>
                  <Badge variant="outline" className="mt-2">
                    User ID: user_aung
                  </Badge>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value="Aung"
                    className="bg-secondary/50"
                    disabled
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Input
                    id="role"
                    value="Administrator"
                    className="bg-secondary/50"
                    disabled
                  />
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Statistics</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 rounded bg-secondary/30">
                      <p className="text-xs text-muted-foreground">Memories Created</p>
                      <p className="text-lg font-bold">924</p>
                    </div>
                    <div className="p-3 rounded bg-secondary/30">
                      <p className="text-xs text-muted-foreground">Last Active</p>
                      <p className="text-lg font-bold">2 min ago</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="gap-2"
        >
          {isSaving ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}