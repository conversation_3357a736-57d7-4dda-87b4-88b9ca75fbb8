#!/bin/bash
# Universal Setup Script for Memory Master
# Automatically detects platform and runs appropriate setup

set -e

echo "🔍 Detecting platform..."

# Platform detection
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ -n "$WINDIR" ]]; then
    echo "🪟 Windows detected"
    if command -v powershell &> /dev/null; then
        powershell -ExecutionPolicy Bypass -File setup-windows.ps1
    elif command -v pwsh &> /dev/null; then
        pwsh -ExecutionPolicy Bypass -File setup-windows.ps1
    else
        echo "❌ PowerShell not found. Please run setup-windows.ps1 manually."
        exit 1
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 Linux detected"
    chmod +x setup-linux.sh
    ./setup-linux.sh
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 macOS detected - using Linux setup"
    chmod +x setup-linux.sh
    ./setup-linux.sh
else
    echo "❓ Unknown platform: $OSTYPE"
    echo "Please run the appropriate setup script manually:"
    echo "  - Linux/WSL/macOS: ./setup-linux.sh"
    echo "  - Windows: powershell -ExecutionPolicy Bypass -File setup-windows.ps1"
    exit 1
fi