'use client';

import React, { useState, useEffect } from 'react';
import { useAppSelector } from '../store/hooks';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { <PERSON>, <PERSON>, User, Brain } from 'lucide-react';
// Removed framer-motion for performance - using CSS transitions instead
import { toast } from 'sonner';

interface ActivityNotification {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  user: string;
  action?: 'memory_created' | 'memory_updated' | 'memory_deleted' | 'user_joined' | 'user_left';
  metadata?: Record<string, any>;
}

export const ActivityNotifications: React.FC = () => {
  const { userId, isAuthenticated } = useAppSelector(state => state.profile);
  const [notifications, setNotifications] = useState<ActivityNotification[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [removingIds, setRemovingIds] = useState<Set<string>>(new Set());

  const addNotification = (notification: Omit<ActivityNotification, 'id'>) => {
    const newNotification: ActivityNotification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    };
    
    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Keep only 5 notifications
    setIsVisible(true);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      removeNotification(newNotification.id);
    }, 5000);
  };

  const removeNotification = (id: string) => {
    // Add smooth exit animation
    setRemovingIds(prev => new Set(prev).add(id));
    
    setTimeout(() => {
      setNotifications(prev => {
        const filtered = prev.filter(n => n.id !== id);
        if (filtered.length === 0) {
          setIsVisible(false);
        }
        return filtered;
      });
      setRemovingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300); // Match CSS transition duration
  };

  useEffect(() => {
    if (!isAuthenticated || userId === 'guest') {
      return;
    }

    // Real activity monitoring implementation
    const setupActivityMonitoring = async () => {
      try {
        // TODO: Implement real activity monitoring when backend WebSocket/SSE is available
        console.log('Activity monitoring initialized for user:', userId);
        
      } catch (error) {
        console.error('Activity monitoring setup failed:', error);
      }
    };

    setupActivityMonitoring();

    return () => {
      console.log('Activity monitoring cleaned up');
    };
  }, [userId, isAuthenticated]);

  const getNotificationIcon = (action?: string) => {
    switch (action) {
      case 'memory_created':
      case 'memory_updated':
      case 'memory_deleted':
        return <Brain className="w-4 h-4" />;
      case 'user_joined':
      case 'user_left':
        return <User className="w-4 h-4" />;
      default:
        return <Bell className="w-4 h-4" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-500/50 bg-green-500/10';
      case 'warning':
        return 'border-yellow-500/50 bg-yellow-500/10';
      case 'error':
        return 'border-red-500/50 bg-red-500/10';
      default:
        return 'border-blue-500/50 bg-blue-500/10';
    }
  };

  if (!isVisible || notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => {
        const isRemoving = removingIds.has(notification.id);
        return (
          <div
            key={notification.id}
            className={`transform transition-all duration-300 ease-in-out ${
              isRemoving 
                ? 'opacity-0 translate-x-80 scale-90' 
                : 'opacity-100 translate-x-0 scale-100'
            }`}
          >
            <Card className={`border glass backdrop-blur-xl ${getNotificationColor(notification.type)}`}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getNotificationIcon(notification.action)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="secondary" className="text-xs">
                        {notification.user}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {notification.timestamp.toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    </div>
                    
                    <p className="text-sm leading-tight">
                      {notification.message}
                    </p>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-6 h-6 flex-shrink-0"
                    onClick={() => removeNotification(notification.id)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      })}
    </div>
  );
};