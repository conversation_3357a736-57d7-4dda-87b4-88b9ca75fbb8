// Test Supabase auth connection
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'http://192.168.1.218:8000',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY'
)

async function testLogin() {
  console.log('Testing Supabase auth login...')
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    })
    
    if (error) {
      console.error('Login error:', error)
      return
    }
    
    console.log('Login successful!')
    console.log('User ID:', data.user.id)
    console.log('Email:', data.user.email)
    
    // Test session
    const { data: session } = await supabase.auth.getSession()
    console.log('Session exists:', !!session.session)
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

testLogin()