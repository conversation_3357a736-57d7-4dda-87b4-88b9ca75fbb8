#!/usr/bin/env python3
"""
Quick test script to verify MCP server is working
"""
import os
import sys
import asyncio
import aiohttp
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set environment variables for the test
os.environ['SUPABASE_ANON_KEY'] = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY'
os.environ['MCP_USER_ID'] = 'aungheinaye'

async def test_mcp_server():
    """Test MCP server functionality"""
    base_url = "http://0.0.0.0:8765"
    
    # Test basic health first
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"✅ Basic health check passed: {health_data}")
                else:
                    logger.error(f"❌ Basic health check failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ Basic health check error: {e}")
        return False
    
    # Test MCP health endpoint
    headers = {
        "Authorization": f"Bearer {os.environ['SUPABASE_ANON_KEY']}"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/mcp/claude/sse/health", headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    mcp_health_data = await response.json()
                    logger.info(f"✅ MCP health check passed: {mcp_health_data}")
                else:
                    error_data = await response.json()
                    logger.error(f"❌ MCP health check failed: {response.status} - {error_data}")
                    return False
    except asyncio.TimeoutError:
        logger.error("❌ MCP health check timed out")
        return False
    except Exception as e:
        logger.error(f"❌ MCP health check error: {e}")
        return False
    
    logger.info("✅ All MCP server tests passed!")
    return True

if __name__ == "__main__":
    success = asyncio.run(test_mcp_server())
    sys.exit(0 if success else 1)