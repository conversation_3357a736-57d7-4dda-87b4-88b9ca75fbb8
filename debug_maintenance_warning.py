#!/usr/bin/env python3
"""
Debug script to identify the source of "temporary maintenance" warning
"""

import requests
import j<PERSON>

def check_all_endpoints():
    """Check all endpoints for maintenance messages"""
    
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
    
    endpoints = [
        "http://localhost:8765/health",
        "http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=" + api_key,
        "http://*************:6333/health",  # Qdrant
        "http://*************:8000/health",  # Supabase
    ]
    
    print("🔍 Checking all endpoints for maintenance messages...")
    print("=" * 60)
    
    for endpoint in endpoints:
        print(f"\n📡 Testing: {endpoint[:50]}...")
        try:
            response = requests.get(endpoint, timeout=5)
            content = response.text
            
            if "maintenance" in content.lower():
                print(f"   ⚠️  FOUND MAINTENANCE MESSAGE:")
                print(f"   Status: {response.status_code}")
                print(f"   Content: {content[:200]}...")
            else:
                print(f"   ✅ No maintenance message (Status: {response.status_code})")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 Additional checks:")
    
    # Check if it's from Claude Desktop
    print("\n📱 Checking Claude Desktop connection...")
    print("   If you're seeing this in Claude Desktop, it might be:")
    print("   - Claude Desktop itself showing maintenance warning")
    print("   - Network connectivity issues")
    print("   - Corporate firewall/proxy interference")
    
    # Check OpenAI API
    print("\n🤖 Checking OpenAI API status...")
    try:
        response = requests.get("https://status.openai.com/api/v2/status.json", timeout=10)
        status_data = response.json()
        print(f"   OpenAI Status: {status_data.get('status', {}).get('description', 'Unknown')}")
    except Exception as e:
        print(f"   ❌ Could not check OpenAI status: {e}")

if __name__ == "__main__":
    check_all_endpoints()