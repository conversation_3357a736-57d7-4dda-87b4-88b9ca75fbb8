"""
Test utilities and helpers for comprehensive integration testing.

This module provides common utilities, fixtures, and helper functions
used across the integration test suite.
"""

import time
import threading
import uuid
import json
import logging
from typing import Dict, Any, List, Optional, Callable
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.database import SessionLocal, Base
from app.models import User, App, Memory, Config as ConfigModel
from app.utils.memory import MemoryClientSingleton, reset_memory_client
from app.utils.config_manager import ConfigManager
from app.enhanced_logging import MemoryOperationStatus


class TestDatabaseManager:
    """Manages test database setup and cleanup."""
    
    def __init__(self):
        self.test_engine = None
        self.test_session = None
    
    def setup_test_db(self):
        """Set up a clean test database."""
        # Create test tables
        Base.metadata.create_all(bind=self.test_engine)
        
        # Create test session
        TestSession = sessionmaker(bind=self.test_engine)
        self.test_session = TestSession()
        
        return self.test_session
    
    def cleanup_test_db(self):
        """Clean up test database."""
        if self.test_session:
            self.test_session.close()
        
        # Drop all tables
        if self.test_engine:
            Base.metadata.drop_all(bind=self.test_engine)


class MockVectorStore:
    """Mock vector store for controlled testing."""
    
    def __init__(self, should_fail: bool = False, failure_type: str = "connection"):
        self.should_fail = should_fail
        self.failure_type = failure_type
        self.stored_memories = {}
        self.call_count = 0
        self.last_operation = None
    
    def add(self, content: str, metadata: dict = None, user_id: str = None):
        """Mock add operation."""
        self.call_count += 1
        self.last_operation = "add"
        
        if self.should_fail:
            if self.failure_type == "connection":
                raise ConnectionError("Vector store connection failed")
            elif self.failure_type == "timeout":
                raise TimeoutError("Vector store operation timed out")
            elif self.failure_type == "auth":
                raise Exception("Authentication failed")
        
        memory_id = str(uuid.uuid4())
        self.stored_memories[memory_id] = {
            "id": memory_id,
            "content": content,
            "metadata": metadata or {},
            "user_id": user_id
        }
        
        return {"id": memory_id, "status": "success"}
    
    def get(self, memory_id: str):
        """Mock get operation."""
        self.call_count += 1
        self.last_operation = "get"
        
        if self.should_fail:
            raise ConnectionError("Vector store connection failed")
        
        return self.stored_memories.get(memory_id)
    
    def search(self, query: str, user_id: str = None, limit: int = 10):
        """Mock search operation."""
        self.call_count += 1
        self.last_operation = "search"
        
        if self.should_fail:
            raise ConnectionError("Vector store connection failed")
        
        # Simple mock search - return memories containing query terms
        results = []
        for memory in self.stored_memories.values():
            if query.lower() in memory["content"].lower():
                if not user_id or memory.get("user_id") == user_id:
                    results.append(memory)
                    if len(results) >= limit:
                        break
        
        return {"results": results}
    
    def reset(self):
        """Reset mock state."""
        self.stored_memories.clear()
        self.call_count = 0
        self.last_operation = None
        self.should_fail = False


class TestConfigManager:
    """Helper for managing test configurations."""
    
    @staticmethod
    def create_test_config(include_critical: bool = True) -> Dict[str, Any]:
        """Create a test configuration."""
        config = {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4o-mini",
                        "temperature": 0.1,
                        "api_key": "test-api-key"
                    }
                },
                "embedder": {
                    "provider": "openai", 
                    "config": {
                        "model": "text-embedding-3-small",
                        "api_key": "test-api-key"
                    }
                }
            }
        }
        
        if include_critical:
            config["vector_store"] = {
                "provider": "qdrant",
                "config": {
                    "host": "localhost",
                    "port": 6333
                }
            }
        
        return config
    
    @staticmethod
    def create_non_critical_config_change() -> Dict[str, Any]:
        """Create a non-critical configuration change."""
        return {
            "mem0": {
                "llm": {
                    "config": {
                        "temperature": 0.7,
                        "max_tokens": 1000
                    }
                }
            }
        }
    
    @staticmethod
    def create_critical_config_change() -> Dict[str, Any]:
        """Create a critical configuration change that requires restart."""
        return {
            "mem0": {
                "llm": {
                    "provider": "anthropic",
                    "config": {
                        "api_key": "new-api-key"
                    }
                }
            }
        }


class ConcurrencyTestHelper:
    """Helper for testing concurrent operations."""
    
    def __init__(self):
        self.results = []
        self.errors = []
        self.threads = []
        self.lock = threading.Lock()
    
    def add_result(self, result: Any):
        """Thread-safe result addition."""
        with self.lock:
            self.results.append(result)
    
    def add_error(self, error: Exception):
        """Thread-safe error addition."""
        with self.lock:
            self.errors.append(error)
    
    def run_concurrent_operations(self, operation: Callable, args_list: List[tuple], 
                                max_workers: int = 10) -> Dict[str, Any]:
        """Run operations concurrently and collect results."""
        self.results.clear()
        self.errors.clear()
        self.threads.clear()
        
        def worker(args):
            try:
                result = operation(*args)
                self.add_result(result)
            except Exception as e:
                self.add_error(e)
        
        # Create and start threads
        for args in args_list[:max_workers]:
            thread = threading.Thread(target=worker, args=(args,))
            self.threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in self.threads:
            thread.join()
        
        return {
            "results": self.results.copy(),
            "errors": self.errors.copy(),
            "success_count": len(self.results),
            "error_count": len(self.errors),
            "total_operations": len(args_list[:max_workers])
        }


@contextmanager
def mock_memory_client(mock_vector_store: MockVectorStore = None):
    """Context manager for mocking the memory client."""
    if mock_vector_store is None:
        mock_vector_store = MockVectorStore()
    
    with patch('app.utils.memory.Memory') as mock_memory_class:
        mock_client = Mock()
        mock_client.add = mock_vector_store.add
        mock_client.get = mock_vector_store.get
        mock_client.search = mock_vector_store.search
        mock_memory_class.from_config.return_value = mock_client
        
        yield mock_client, mock_vector_store


@contextmanager
def temporary_config_change(config_manager: ConfigManager, new_config: Dict[str, Any]):
    """Context manager for temporary configuration changes."""
    original_config = config_manager.get_config().copy()
    
    try:
        config_manager.update_config(new_config, save_to_db=False)
        yield
    finally:
        # Restore original configuration
        config_manager.update_config(original_config, save_to_db=False)


def wait_for_condition(condition: Callable[[], bool], timeout: float = 5.0, 
                      interval: float = 0.1) -> bool:
    """Wait for a condition to become true within a timeout."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition():
            return True
        time.sleep(interval)
    return False


def assert_memory_operation_result(result: Any, expected_status: MemoryOperationStatus = None):
    """Assert that a memory operation result has the expected structure and status."""
    assert result is not None, "Memory operation result should not be None"
    
    if hasattr(result, 'status'):
        if expected_status:
            assert result.status == expected_status, f"Expected status {expected_status}, got {result.status}"
    elif isinstance(result, dict):
        assert 'status' in result or 'id' in result, "Result should contain status or id field"
        if expected_status and 'status' in result:
            assert result['status'] == expected_status.value, f"Expected status {expected_status.value}, got {result['status']}"


def create_test_user_and_app(db_session) -> tuple:
    """Create test user and app in database."""
    user_id = uuid.uuid4()
    app_id = uuid.uuid4()
    
    user = User(
        id=user_id,
        user_id=str(user_id),
        name="Test User"
    )
    
    app = App(
        id=app_id,
        name="test-app",
        owner_id=user_id
    )
    
    db_session.add(user)
    db_session.add(app)
    db_session.commit()
    
    return user, app


def setup_test_logging():
    """Set up logging for tests."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
