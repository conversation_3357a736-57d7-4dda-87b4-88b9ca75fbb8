// Quick script to create Yohanna's user
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'http://*************:8000',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
)

async function createYohanna() {
  console.log('Creating Yohanna user...')
  
  // Note: This will need service role key in production
  // For now, this won't work with anon key, but shows the structure
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',  // Change this
      email_confirm: true,
      user_metadata: {
        display_name: '<PERSON><PERSON>'
      }
    })
    
    if (error) {
      console.error('Error:', error)
      return
    }
    
    console.log('Created user:', data.user.id)
    
    // Update memory_master.users
    const { error: updateError } = await supabase
      .from('memory_master.users')
      .update({
        supabase_user_id: data.user.id,
        email: '<EMAIL>',
        email_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', 'yohanna')
    
    if (updateError) {
      console.error('Update error:', updateError)
    } else {
      console.log('Updated memory_master.users')
    }
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

createYohanna()