import { test } from '@playwright/test';

test.describe('Memory Master v2 - Simple Screenshot Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test('Take all verification screenshots', async ({ page }) => {
    console.log('Taking screenshot of main dashboard...');
    await page.screenshot({ 
      path: 'screenshots/verification-main-dashboard.png',
      fullPage: true 
    });

    console.log('Navigating to memories page...');
    await page.click('text=Memories');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    console.log('Taking screenshot of memories page...');
    await page.screenshot({ 
      path: 'screenshots/verification-memories-page.png',
      fullPage: true 
    });

    console.log('Testing Create Memory button...');
    const createButton = page.locator('button:has-text("Create Memory")');
    if (await createButton.isVisible()) {
      await createButton.click();
      await page.waitForTimeout(1000);
      
      console.log('Taking screenshot of create memory dialog...');
      await page.screenshot({ 
        path: 'screenshots/verification-create-memory-dialog.png',
        fullPage: true 
      });
    }

    console.log('All screenshots completed successfully!');
  });
});