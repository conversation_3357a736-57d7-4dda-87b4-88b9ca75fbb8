#!/usr/bin/env python3
"""
Run database analysis using the existing application database connection
"""

import os
import sys

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import after path is set
from app.database.base import engine
from sqlalchemy import text

def execute_query(query_name, query):
    """Execute and display query results"""
    print(f"\n{'='*80}")
    print(f"{query_name}")
    print('='*80)
    
    with engine.connect() as conn:
        result = conn.execute(text(query))
        rows = result.fetchall()
        
        if rows:
            # Get column names
            columns = list(result.keys())
            
            # Calculate column widths
            col_widths = []
            for i, col in enumerate(columns):
                max_width = len(str(col))
                for row in rows:
                    val = str(row[i]) if row[i] is not None else "NULL"
                    max_width = max(max_width, len(val))
                col_widths.append(min(max_width, 50))  # Cap at 50 chars
            
            # Print header
            header = " | ".join(str(col).ljust(col_widths[i])[:col_widths[i]] for i, col in enumerate(columns))
            print(header)
            print("-" * len(header))
            
            # Print rows
            for row in rows:
                row_str = " | ".join(
                    (str(val) if val is not None else "NULL").ljust(col_widths[i])[:col_widths[i]] 
                    for i, val in enumerate(row)
                )
                print(row_str)
            
            print(f"\nTotal rows: {len(rows)}")
        else:
            print("No data found.")

def main():
    print("ANALYZING DATABASE USER STRUCTURE")
    print("="*80)
    
    queries = [
        ("1. All Users with User IDs", """
            SELECT 
                id,
                user_id,
                name,
                email,
                created_at,
                updated_at,
                supabase_user_id,
                email_verified,
                last_sign_in_at
            FROM memory_master.users
            ORDER BY created_at DESC
        """),
        
        ("2. Memory Counts per User", """
            SELECT 
                u.user_id,
                u.name,
                u.email,
                COUNT(m.id) as memory_count,
                COUNT(CASE WHEN m.state = 'active' THEN 1 END) as active_memories,
                COUNT(CASE WHEN m.state = 'archived' THEN 1 END) as archived_memories,
                COUNT(CASE WHEN m.state = 'deleted' THEN 1 END) as deleted_memories
            FROM memory_master.users u
            LEFT JOIN memory_master.memories m ON u.id = m.user_id
            GROUP BY u.id, u.user_id, u.name, u.email
            ORDER BY memory_count DESC
        """),
        
        ("3. Evolution Operation Counts per User", """
            SELECT 
                u.user_id,
                u.name,
                COUNT(eo.id) as total_operations,
                COUNT(CASE WHEN eo.operation_type = 'ADD' THEN 1 END) as add_ops,
                COUNT(CASE WHEN eo.operation_type = 'UPDATE' THEN 1 END) as update_ops,
                COUNT(CASE WHEN eo.operation_type = 'DELETE' THEN 1 END) as delete_ops,
                COUNT(CASE WHEN eo.operation_type = 'NOOP' THEN 1 END) as noop_ops
            FROM memory_master.users u
            LEFT JOIN memory_master.evolution_operations eo ON u.id = eo.user_id
            GROUP BY u.id, u.user_id, u.name
            ORDER BY total_operations DESC
        """),
        
        ("4. App Ownership per User", """
            SELECT 
                u.user_id,
                u.name,
                COUNT(a.id) as app_count,
                COUNT(CASE WHEN a.is_active = true THEN 1 END) as active_apps,
                array_agg(a.name ORDER BY a.created_at) as app_names
            FROM memory_master.users u
            LEFT JOIN memory_master.apps a ON u.id = a.owner_id
            GROUP BY u.id, u.user_id, u.name
            ORDER BY app_count DESC
        """),
        
        ("5. Tables Referencing Users (Foreign Keys)", """
            SELECT 
                tc.table_schema,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                tc.constraint_name
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND ccu.table_name = 'users'
                AND tc.table_schema = 'memory_master'
            ORDER BY tc.table_name
        """),
        
        ("6. Detailed User Information with All Related Counts", """
            SELECT 
                u.id,
                u.user_id,
                u.name,
                u.email,
                (SELECT COUNT(*) FROM memory_master.memories m WHERE m.user_id = u.id) as memories,
                (SELECT COUNT(*) FROM memory_master.apps a WHERE a.owner_id = u.id) as apps,
                (SELECT COUNT(*) FROM memory_master.evolution_operations eo WHERE eo.user_id = u.id) as evolution_ops,
                (SELECT COUNT(*) FROM memory_master.evolution_insights ei WHERE ei.user_id = u.id) as insights
            FROM memory_master.users u
            ORDER BY u.created_at DESC
        """),
        
        ("7. User ID Analysis", """
            SELECT 
                user_id,
                LENGTH(user_id) as id_length,
                CASE 
                    WHEN user_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN 'UUID Format'
                    WHEN user_id ~ '^[0-9]+$' THEN 'Numeric'
                    WHEN user_id ~ '^[a-zA-Z0-9_-]+$' THEN 'Alphanumeric'
                    ELSE 'Other'
                END as id_format,
                COUNT(*) as count
            FROM memory_master.users
            GROUP BY user_id, id_length, id_format
            ORDER BY user_id
        """),
        
        ("8. Row Counts for Tables with User References", """
            SELECT 'memories' as table_name, COUNT(*) as row_count FROM memory_master.memories
            UNION ALL
            SELECT 'apps', COUNT(*) FROM memory_master.apps
            UNION ALL
            SELECT 'evolution_operations', COUNT(*) FROM memory_master.evolution_operations
            UNION ALL
            SELECT 'evolution_insights', COUNT(*) FROM memory_master.evolution_insights
            UNION ALL
            SELECT 'evolution_configurations', COUNT(*) FROM memory_master.evolution_configurations
            UNION ALL
            SELECT 'memory_status_history', COUNT(*) FROM memory_master.memory_status_history
            UNION ALL
            SELECT 'configuration_versions', COUNT(*) FROM memory_master.configuration_versions
            UNION ALL
            SELECT 'evolution_analytics', COUNT(*) FROM memory_master.evolution_analytics
            ORDER BY table_name
        """)
    ]
    
    for query_name, query in queries:
        execute_query(query_name, query)

if __name__ == "__main__":
    main()