const http = require('http');

// Function to make API call
function makeAPICall(userId, callback) {
    const options = {
        hostname: 'localhost',
        port: 5678,
        path: '/api/v1/memories?limit=3',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-User-ID': userId
        }
    };

    const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                callback(null, response);
            } catch (e) {
                callback(e, null);
            }
        });
    });

    req.on('error', (e) => { callback(e, null); });
    req.end();
}

// Test both users
console.log('Testing API calls for different users:');
console.log('=====================================');

makeAPICall('aungheinaye', (err, data) => {
    if (err) {
        console.log('Error for aungheinaye:', err.message);
    } else {
        console.log('aungheinaye - Total memories:', data.totalCount);
        console.log('First 3 memories:', data.memories.slice(0, 3).map(m => ({
            id: m.id.substring(0, 8),
            title: m.title || 'No title',
            content: m.content.substring(0, 100) + '...'
        })));
    }

    makeAPICall('yohanna', (err, data) => {
        if (err) {
            console.log('Error for yohanna:', err.message);
        } else {
            console.log('\nyohanna - Total memories:', data.totalCount);
            console.log('First 3 memories:', data.memories.slice(0, 3).map(m => ({
                id: m.id.substring(0, 8),
                title: m.title || 'No title',
                content: m.content.substring(0, 100) + '...'
            })));
        }
    });
});