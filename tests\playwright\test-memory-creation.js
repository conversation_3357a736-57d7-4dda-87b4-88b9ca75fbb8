const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

async function testMemoryCreation() {
  console.log('Starting Playwright test for memory creation...');
  
  // Create screenshots directory if it doesn't exist
  const screenshotsDir = path.join(__dirname, 'test-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir);
  }

  const browser = await chromium.launch({ 
    headless: false, // Run in visible mode to see what's happening
    slowMo: 1000 // Add delay between actions for better visibility
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  // Listen for console messages and network errors
  const consoleMessages = [];
  const networkErrors = [];
  
  page.on('console', msg => {
    consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    console.log(`Console ${msg.type()}: ${msg.text()}`);
  });
  
  page.on('response', response => {
    if (!response.ok() && response.url().includes('api')) {
      networkErrors.push(`${response.status()}: ${response.url()}`);
      console.log(`Network Error: ${response.status()} - ${response.url()}`);
    }
  });

  try {
    console.log('\n1. Navigating to dashboard...');
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '1-dashboard-loaded.png') });
    console.log('✓ Dashboard loaded successfully');

    console.log('\n2. Looking for Create Memory button...');
    // Wait for and click the Create Memory button
    const createButton = page.locator('button:has-text("Create Memory"), button:has-text("Add Memory"), [data-testid="create-memory"], .create-memory-btn');
    await createButton.first().waitFor({ timeout: 10000 });
    await page.screenshot({ path: path.join(screenshotsDir, '2-before-click-create.png') });
    
    await createButton.first().click();
    console.log('✓ Clicked Create Memory button');

    console.log('\n3. Waiting for form dialog to appear...');
    // Wait for the form dialog to appear
    await page.waitForSelector('form, [role="dialog"]', { timeout: 10000 });
    await page.screenshot({ path: path.join(screenshotsDir, '3-form-dialog-open.png') });
    console.log('✓ Form dialog appeared');

    console.log('\n4. Filling in the form...');
    // Fill in the title field
    const titleField = page.locator('input[name="title"], input[placeholder*="title" i], label:has-text("Title") + input, input[id*="title"]');
    await titleField.first().waitFor({ timeout: 5000 });
    await titleField.first().fill('Post-Fix Test Memory');
    console.log('✓ Filled in title');

    // Fill in the content field
    const contentField = page.locator('textarea[name="content"], textarea[placeholder*="content" i], label:has-text("Content") + textarea, textarea[id*="content"]');
    await contentField.first().waitFor({ timeout: 5000 });
    await contentField.first().fill('This memory is created to test that the UUID conversion fix is working properly and no longer shows failure messages.');
    console.log('✓ Filled in content');

    await page.screenshot({ path: path.join(screenshotsDir, '4-form-filled.png') });

    console.log('\n5. Submitting the form...');
    // Submit the form
    const submitButton = page.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create"), button:has-text("Add")');
    await submitButton.first().click();
    console.log('✓ Clicked submit button');

    console.log('\n6. Waiting for form submission to complete...');
    // Wait a moment for the submission to process
    await page.waitForTimeout(3000);
    await page.screenshot({ path: path.join(screenshotsDir, '5-after-submit.png') });

    console.log('\n7. Checking if dialog closed...');
    // Check if dialog closed (form should no longer be visible)
    const dialogStillOpen = await page.locator('[role="dialog"], .dialog-overlay').count();
    const dialogClosed = dialogStillOpen === 0;
    console.log(`✓ Dialog closed: ${dialogClosed ? 'Yes' : 'No'}`);

    console.log('\n8. Checking for the new memory in the list...');
    // Look for the new memory in the list
    await page.waitForTimeout(2000); // Give time for the list to update
    const memoryInList = await page.locator('text="Post-Fix Test Memory"').count() > 0;
    console.log(`✓ Memory appears in list: ${memoryInList ? 'Yes' : 'No'}`);

    await page.screenshot({ path: path.join(screenshotsDir, '6-final-state.png') });

    console.log('\n9. Checking for error messages...');
    // Check for any error messages on the page
    const errorMessages = await page.locator('.error, [role="alert"], .alert-error, .text-red, .text-destructive').count();
    const hasErrors = errorMessages > 0;
    console.log(`✓ Error messages present: ${hasErrors ? 'Yes' : 'No'}`);

    if (hasErrors) {
      const errorTexts = await page.locator('.error, [role="alert"], .alert-error, .text-red, .text-destructive').allTextContents();
      console.log('Error messages found:', errorTexts);
    }

    // Final results
    console.log('\n=== TEST RESULTS ===');
    console.log(`✓ Form submits successfully: ${!hasErrors}`);
    console.log(`✓ Dialog closes properly: ${dialogClosed}`);
    console.log(`✓ Memory appears in list: ${memoryInList}`);
    console.log(`✓ No console errors: ${consoleMessages.filter(msg => msg.includes('error')).length === 0}`);
    console.log(`✓ No network errors: ${networkErrors.length === 0}`);

    if (consoleMessages.length > 0) {
      console.log('\nConsole messages:', consoleMessages);
    }
    
    if (networkErrors.length > 0) {
      console.log('\nNetwork errors:', networkErrors);
    }

    const allTestsPassed = !hasErrors && dialogClosed && memoryInList && 
                          consoleMessages.filter(msg => msg.includes('error')).length === 0 && 
                          networkErrors.length === 0;

    console.log(`\n🎯 OVERALL RESULT: ${allTestsPassed ? 'ALL TESTS PASSED! 🎉' : 'SOME TESTS FAILED ❌'}`);

    return allTestsPassed;

  } catch (error) {
    console.error('Test failed with error:', error);
    await page.screenshot({ path: path.join(screenshotsDir, 'error-screenshot.png') });
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
testMemoryCreation().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});