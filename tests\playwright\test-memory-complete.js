const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

async function testMemoryCreationComplete() {
  console.log('Starting complete memory creation test...');
  
  const screenshotsDir = path.join(__dirname, 'test-screenshots-complete');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir);
  }

  const browser = await chromium.launch({ headless: false, slowMo: 1000 });
  const context = await browser.newContext({ viewport: { width: 1280, height: 720 } });
  const page = await context.newPage();
  
  const consoleMessages = [];
  const networkErrors = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      consoleMessages.push(`ERROR: ${msg.text()}`);
    }
  });
  
  page.on('response', response => {
    if (!response.ok() && response.url().includes('api')) {
      networkErrors.push(`${response.status()}: ${response.url()}`);
    }
  });

  try {
    // Navigate to dashboard
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: path.join(screenshotsDir, '1-dashboard.png') });

    // Click on Memories in sidebar to go to memories page
    console.log('Navigating to Memories page...');
    await page.click('text=Memories');
    await page.waitForTimeout(2000);
    await page.screenshot({ path: path.join(screenshotsDir, '2-memories-page.png') });

    // Click Create Memory button
    console.log('Opening create memory form...');
    const createButton = page.locator('button:has-text("Create Memory"), button:has-text("Add Memory"), button:has-text("New Memory")');
    await createButton.first().click();
    await page.waitForTimeout(1000);
    await page.screenshot({ path: path.join(screenshotsDir, '3-form-open.png') });

    // Fill form
    console.log('Filling form...');
    const titleField = page.locator('input[name="title"], input[placeholder*="title" i]').first();
    await titleField.fill('Post-Fix Test Memory - Complete');
    
    const contentField = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
    await contentField.fill('This memory tests the complete flow including dialog closure and list update after the UUID fix.');
    
    await page.screenshot({ path: path.join(screenshotsDir, '4-form-filled.png') });

    // Submit form
    console.log('Submitting form...');
    const submitButton = page.locator('button[type="submit"], button:has-text("Create Memory")').first();
    await submitButton.click();

    // Wait for success message
    console.log('Waiting for success notification...');
    await page.waitForSelector('text=Memory created successfully', { timeout: 10000 });
    await page.screenshot({ path: path.join(screenshotsDir, '5-success-notification.png') });

    // Wait for dialog to close
    console.log('Waiting for dialog to close...');
    await page.waitForTimeout(3000);
    const dialogOpen = await page.locator('[role="dialog"]').count();
    console.log(`Dialog still open: ${dialogOpen > 0 ? 'Yes' : 'No'}`);

    // If dialog is still open, try to close it
    if (dialogOpen > 0) {
      console.log('Manually closing dialog...');
      await page.click('button:has-text("Cancel"), [aria-label="Close"], button[aria-label="Close"]');
      await page.waitForTimeout(1000);
    }

    await page.screenshot({ path: path.join(screenshotsDir, '6-dialog-closed.png') });

    // Check if new memory appears in the list
    console.log('Checking for new memory in list...');
    await page.waitForTimeout(2000); // Give time for list to refresh
    const memoryInList = await page.locator('text="Post-Fix Test Memory - Complete"').count() > 0;
    console.log(`New memory visible in list: ${memoryInList ? 'Yes' : 'No'}`);

    await page.screenshot({ path: path.join(screenshotsDir, '7-final-list.png') });

    // Final summary
    const finalDialogOpen = await page.locator('[role="dialog"]').count() > 0;
    const hasNetworkErrors = networkErrors.length > 0;
    const hasConsoleErrors = consoleMessages.length > 0;

    console.log('\n=== FINAL TEST RESULTS ===');
    console.log(`✓ Success notification shown: Yes`);
    console.log(`✓ Dialog closed properly: ${!finalDialogOpen ? 'Yes' : 'No'}`);
    console.log(`✓ Memory appears in list: ${memoryInList ? 'Yes' : 'No'}`);
    console.log(`✓ No network errors: ${!hasNetworkErrors ? 'Yes' : 'No'}`);
    console.log(`✓ No console errors: ${!hasConsoleErrors ? 'Yes' : 'No'}`);

    if (hasNetworkErrors) {
      console.log('\nNetwork errors:', networkErrors);
    }
    if (hasConsoleErrors) {
      console.log('\nConsole errors:', consoleMessages);
    }

    const allGood = !finalDialogOpen && memoryInList && !hasNetworkErrors && !hasConsoleErrors;
    console.log(`\n🎯 OVERALL: ${allGood ? 'ALL TESTS PASSED! 🎉' : 'SOME ISSUES FOUND ⚠️'}`);

    return allGood;

  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: path.join(screenshotsDir, 'error.png') });
    return false;
  } finally {
    await browser.close();
  }
}

testMemoryCreationComplete().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});