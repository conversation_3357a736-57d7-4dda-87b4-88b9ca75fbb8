import { test, expect } from '@playwright/test';

test.describe('Evolution Intelligence System - E2E Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Switch to Aung (user with data for evolution analysis)
    await page.locator('button:has-text("Aung Hein Aye")').click();
    await page.waitForTimeout(1000);
  });

  test.describe('Evolution Analytics Dashboard', () => {
    test('should display evolution analytics and operation tracking', async ({ page }) => {
      // Navigate to evolution analytics page
      await page.goto('/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check for evolution intelligence components
      const evolutionElements = [
        'text="Evolution Intelligence"',
        'text="Operation Analytics"',
        'text="Memory Operations"',
        'text="Confidence Scores"',
        'text="System Performance"'
      ];
      
      let foundElements = 0;
      for (const selector of evolutionElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          foundElements++;
          console.log(`✓ Found evolution element: ${selector}`);
        }
      }
      
      // Take screenshot of evolution dashboard
      await page.screenshot({ 
        path: 'test-results/screenshots/evolution-dashboard.png',
        fullPage: true 
      });
      
      console.log(`✓ Evolution dashboard loaded with ${foundElements} elements`);
    });

    test('should track memory operations with analytics', async ({ page }) => {
      // Navigate to memories page first
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Create a memory to trigger evolution tracking
      await page.locator('button:has-text("Create Memory")').click();
      await page.waitForTimeout(1000);
      
      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
      const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
      
      if (await titleInput.isVisible()) {
        await titleInput.fill('Evolution Test Memory');
        await contentInput.fill('This memory is created to test the evolution intelligence system tracking capabilities. It should generate analytics data and confidence scores.');
        
        const submitButton = page.locator('button:has-text("Create"), button:has-text("Save")').first();
        await submitButton.click();
        await page.waitForTimeout(3000);
        
        // Navigate to evolution analytics to see if operation was tracked
        await page.goto('/evolution');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Look for operation tracking indicators
        const operationElements = [
          'text="ADD"',
          'text="Operation"',
          'text="Confidence"',
          'text="Recent Operations"'
        ];
        
        let operationTracking = false;
        for (const selector of operationElements) {
          const element = page.locator(selector);
          if (await element.isVisible()) {
            operationTracking = true;
            console.log(`✓ Operation tracking found: ${selector}`);
          }
        }
        
        if (operationTracking) {
          console.log('✓ Evolution intelligence successfully tracking operations');
        }
      }
    });
  });

  test.describe('Evolution Configuration', () => {
    test('should access and display evolution configuration settings', async ({ page }) => {
      // Navigate to evolution settings
      await page.goto('/settings/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check for configuration elements
      const configElements = [
        'text="Evolution Configuration"',
        'text="Domain Configuration"',
        'text="Prompts"',
        'text="Settings"'
      ];
      
      let configFound = false;
      for (const selector of configElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          configFound = true;
          console.log(`✓ Configuration element found: ${selector}`);
        }
      }
      
      if (configFound) {
        // Take screenshot of configuration page
        await page.screenshot({ 
          path: 'test-results/screenshots/evolution-config.png',
          fullPage: true 
        });
        
        console.log('✓ Evolution configuration page accessible');
      } else {
        console.log('! Evolution configuration page not found');
      }
    });

    test('should handle evolution configuration updates', async ({ page }) => {
      await page.goto('/settings/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Look for configuration forms or inputs
      const configInputs = [
        'input[name*="domain"]',
        'textarea[name*="prompt"]',
        'select[name*="config"]',
        'button:has-text("Save")',
        'button:has-text("Update")'
      ];
      
      let configInputsFound = 0;
      for (const selector of configInputs) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          configInputsFound++;
          console.log(`✓ Configuration input found: ${selector}`);
        }
      }
      
      if (configInputsFound > 0) {
        console.log(`✓ Evolution configuration interface active with ${configInputsFound} inputs`);
      }
    });
  });

  test.describe('Evolution Performance Metrics', () => {
    test('should display performance metrics and confidence scores', async ({ page }) => {
      await page.goto('/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check for performance metrics
      const metricElements = [
        'text="Confidence Score"',
        'text="Response Time"',
        'text="Success Rate"',
        'text="Operation Count"',
        'text="Average"',
        'text="Performance"'
      ];
      
      let metricsFound = 0;
      for (const selector of metricElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          metricsFound++;
          console.log(`✓ Performance metric found: ${selector}`);
        }
      }
      
      // Look for charts or graphs
      const chartElements = [
        'canvas',
        'svg',
        '[data-testid*="chart"]',
        '.recharts-wrapper'
      ];
      
      let chartsFound = 0;
      for (const selector of chartElements) {
        const element = page.locator(selector);
        const count = await element.count();
        if (count > 0) {
          chartsFound += count;
          console.log(`✓ Chart element found: ${selector} (${count} instances)`);
        }
      }
      
      if (metricsFound > 0 || chartsFound > 0) {
        console.log(`✓ Evolution performance metrics displayed: ${metricsFound} metrics, ${chartsFound} charts`);
        
        // Take screenshot of metrics
        await page.screenshot({ 
          path: 'test-results/screenshots/evolution-metrics.png',
          fullPage: true 
        });
      }
    });

    test('should handle real-time evolution analytics updates', async ({ page }) => {
      await page.goto('/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Capture initial state
      const initialMetrics = await page.locator('text="Operation Count"').locator('..').locator('p, span').textContent();
      
      // Perform a memory operation that should trigger evolution tracking
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Create a memory to trigger evolution
      await page.locator('button:has-text("Create Memory")').click();
      await page.waitForTimeout(1000);
      
      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
      const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
      
      if (await titleInput.isVisible()) {
        await titleInput.fill('Real-time Evolution Test');
        await contentInput.fill('Testing real-time evolution analytics updates.');
        
        const submitButton = page.locator('button:has-text("Create"), button:has-text("Save")').first();
        await submitButton.click();
        await page.waitForTimeout(3000);
        
        // Return to evolution analytics
        await page.goto('/evolution');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check if metrics updated
        const updatedMetrics = await page.locator('text="Operation Count"').locator('..').locator('p, span').textContent();
        
        if (initialMetrics !== updatedMetrics) {
          console.log('✓ Real-time evolution analytics updates working');
        } else {
          console.log('! Evolution analytics may not be updating in real-time');
        }
      }
    });
  });

  test.describe('Evolution API Integration', () => {
    test('should verify evolution API endpoints are responsive', async ({ page }) => {
      // Test evolution-related API endpoints
      const evolutionEndpoints = [
        '/api/v1/evolution/analytics',
        '/api/v1/evolution/operations',
        '/api/v1/evolution/config',
        '/api/v1/evolution/metrics'
      ];
      
      const workingEndpoints = [];
      
      for (const endpoint of evolutionEndpoints) {
        try {
          const response = await page.request.get(endpoint);
          if (response.status() === 200) {
            workingEndpoints.push(endpoint);
            console.log(`✓ Evolution API endpoint working: ${endpoint}`);
          } else {
            console.log(`! Evolution API endpoint returned ${response.status()}: ${endpoint}`);
          }
        } catch (error) {
          console.log(`! Evolution API endpoint error: ${endpoint}`);
        }
      }
      
      // At least some evolution endpoints should be working
      if (workingEndpoints.length > 0) {
        console.log(`✓ Evolution API integration active: ${workingEndpoints.length}/${evolutionEndpoints.length} endpoints working`);
      }
    });

    test('should handle evolution API error states gracefully', async ({ page }) => {
      // Mock API failure
      await page.route('**/api/v1/evolution/**', route => route.abort());
      
      await page.goto('/evolution');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Check for error handling
      const errorElements = [
        'text="Error"',
        'text="Failed to load"',
        'text="Unable to connect"',
        'text="Try again"',
        'text="Loading"'
      ];
      
      let errorHandlingFound = false;
      for (const selector of errorElements) {
        const element = page.locator(selector);
        if (await element.isVisible()) {
          errorHandlingFound = true;
          console.log(`✓ Evolution error handling found: ${selector}`);
          break;
        }
      }
      
      if (errorHandlingFound) {
        console.log('✓ Evolution system handles API errors gracefully');
      } else {
        console.log('! Evolution system may not handle API errors properly');
      }
    });
  });

  test.describe('Evolution Intelligence Data Flow', () => {
    test('should track complete memory operation lifecycle', async ({ page }) => {
      // Navigate to memories
      await page.goto('/memories');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Create memory
      await page.locator('button:has-text("Create Memory")').click();
      await page.waitForTimeout(1000);
      
      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
      const contentInput = page.locator('textarea[name="content"], textarea[placeholder*="content" i]').first();
      
      if (await titleInput.isVisible()) {
        const testTitle = `Lifecycle Test - ${Date.now()}`;
        await titleInput.fill(testTitle);
        await contentInput.fill('Testing complete memory operation lifecycle tracking in evolution intelligence system.');
        
        const submitButton = page.locator('button:has-text("Create"), button:has-text("Save")').first();
        await submitButton.click();
        await page.waitForTimeout(3000);
        
        // Update memory (if edit is available)
        const memoryItem = page.locator(`text="${testTitle}"`).first();
        if (await memoryItem.isVisible()) {
          const editButton = memoryItem.locator('..').locator('button:has-text("Edit")').first();
          
          if (await editButton.isVisible()) {
            await editButton.click();
            await page.waitForTimeout(1000);
            
            const editContentInput = page.locator('textarea[name="content"]').first();
            if (await editContentInput.isVisible()) {
              await editContentInput.fill('Updated content - testing evolution UPDATE operation tracking.');
              
              const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")').first();
              await saveButton.click();
              await page.waitForTimeout(3000);
              
              console.log('✓ Memory update operation completed');
            }
          }
        }
        
        // Check evolution analytics for tracked operations
        await page.goto('/evolution');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Look for operation tracking
        const operationTypes = ['ADD', 'UPDATE', 'Operation'];
        let operationsTracked = 0;
        
        for (const opType of operationTypes) {
          const element = page.locator(`text="${opType}"`);
          if (await element.isVisible()) {
            operationsTracked++;
            console.log(`✓ Evolution tracked operation: ${opType}`);
          }
        }
        
        if (operationsTracked > 0) {
          console.log(`✓ Evolution intelligence tracked ${operationsTracked} operation types`);
        }
      }
    });
  });

  test.afterEach(async ({ page }) => {
    // Take screenshot of final state
    await page.screenshot({ 
      path: `test-results/screenshots/evolution-final-${Date.now()}.png`,
      fullPage: true 
    });
  });
});