"""
Tests for Configuration Change Listeners

This module contains tests for configuration change listeners
and their integration with the ConfigManager.
"""

import pytest
import logging
from unittest.mock import Mock, patch, call
from typing import Dict, Any

from app.utils.config_manager import Config<PERSON>hangeEvent, ConfigChangeType
from app.utils.config_listeners import (
    memory_client_config_listener,
    logging_config_listener,
    system_config_listener,
    performance_config_listener,
    register_default_listeners,
    _has_memory_config_changes,
    _get_nested_config_value
)


class TestConfigListeners:
    """Test suite for configuration change listeners."""
    
    def test_get_nested_config_value(self):
        """Test the nested configuration value getter utility."""
        config = {
            "level1": {
                "level2": {
                    "level3": "value"
                }
            },
            "simple": "simple_value"
        }
        
        # Test successful nested access
        assert _get_nested_config_value(config, "level1.level2.level3") == "value"
        assert _get_nested_config_value(config, "simple") == "simple_value"
        
        # Test non-existent paths
        assert _get_nested_config_value(config, "nonexistent") is None
        assert _get_nested_config_value(config, "level1.nonexistent") is None
        assert _get_nested_config_value(config, "level1.level2.nonexistent") is None
    
    def test_has_memory_config_changes(self):
        """Test detection of memory-related configuration changes."""
        old_config = {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {"model": "gpt-4"}
                },
                "embedder": {
                    "provider": "openai"
                }
            },
            "openmemory": {
                "custom_instructions": "old instructions"
            }
        }
        
        # Test no changes
        new_config = old_config.copy()
        assert _has_memory_config_changes(old_config, new_config) is False
        
        # Test LLM provider change
        new_config_llm = old_config.copy()
        new_config_llm["mem0"]["llm"]["provider"] = "anthropic"
        assert _has_memory_config_changes(old_config, new_config_llm) is True
        
        # Test custom instructions change
        new_config_instructions = old_config.copy()
        new_config_instructions["openmemory"]["custom_instructions"] = "new instructions"
        assert _has_memory_config_changes(old_config, new_config_instructions) is True
        
        # Test non-memory related change
        new_config_other = old_config.copy()
        new_config_other["other_setting"] = "value"
        assert _has_memory_config_changes(old_config, new_config_other) is False
    
    @patch('app.utils.config_listeners.reset_memory_client')
    def test_memory_client_config_listener(self, mock_reset_memory_client):
        """Test the memory client configuration change listener."""
        # Test with memory-related changes
        old_config = {
            "mem0": {
                "llm": {"provider": "openai"}
            }
        }
        
        new_config = {
            "mem0": {
                "llm": {"provider": "anthropic"}
            }
        }
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.CRITICAL,
            requires_restart=True,
            config_version=2,
            timestamp=**********.0
        )
        
        # Call listener
        memory_client_config_listener(change_event)
        
        # Verify memory client was reset
        mock_reset_memory_client.assert_called_once()
    
    @patch('app.utils.config_listeners.reset_memory_client')
    def test_memory_client_config_listener_no_changes(self, mock_reset_memory_client):
        """Test memory client listener with no memory-related changes."""
        old_config = {"other_setting": "value1"}
        new_config = {"other_setting": "value2"}
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.NON_CRITICAL,
            requires_restart=False,
            config_version=2,
            timestamp=**********.0
        )
        
        # Call listener
        memory_client_config_listener(change_event)
        
        # Verify memory client was not reset
        mock_reset_memory_client.assert_not_called()
    
    @patch('app.utils.config_listeners.logging')
    def test_logging_config_listener(self, mock_logging):
        """Test the logging configuration change listener."""
        old_config = {"logging": {"level": "INFO"}}
        new_config = {"logging": {"level": "DEBUG"}}
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.NON_CRITICAL,
            requires_restart=False,
            config_version=2,
            timestamp=**********.0
        )
        
        # Mock logging.getLogger().setLevel
        mock_logger = Mock()
        mock_logging.getLogger.return_value = mock_logger
        mock_logging.DEBUG = 10  # Standard logging level
        
        # Call listener
        logging_config_listener(change_event)
        
        # Verify logging level was updated
        mock_logger.setLevel.assert_called_once_with(10)
    
    @patch('app.utils.config_listeners.logging')
    def test_logging_config_listener_invalid_level(self, mock_logging):
        """Test logging listener with invalid logging level."""
        old_config = {"logging": {"level": "INFO"}}
        new_config = {"logging": {"level": "INVALID_LEVEL"}}
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.NON_CRITICAL,
            requires_restart=False,
            config_version=2,
            timestamp=**********.0
        )
        
        # Mock logging.getLogger().setLevel
        mock_logger = Mock()
        mock_logging.getLogger.return_value = mock_logger
        
        # Call listener
        logging_config_listener(change_event)
        
        # Verify logging level was not updated due to invalid level
        mock_logger.setLevel.assert_not_called()
    
    @patch('app.utils.config_listeners.logging')
    def test_system_config_listener(self, mock_logging):
        """Test the system configuration change listener."""
        old_config = {"setting": "old_value"}
        new_config = {"setting": "new_value"}
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.CRITICAL,
            requires_restart=True,
            config_version=5,
            timestamp=**********.0
        )
        
        # Call listener
        system_config_listener(change_event)
        
        # Verify appropriate log messages were generated
        mock_logging.info.assert_called()
        mock_logging.warning.assert_called()
        
        # Check that the log messages contain expected information
        info_calls = [call[0][0] for call in mock_logging.info.call_args_list]
        warning_calls = [call[0][0] for call in mock_logging.warning.call_args_list]
        
        assert any("version=5" in msg for msg in info_calls)
        assert any("type=critical" in msg for msg in info_calls)
        assert any("requires_restart=True" in msg for msg in info_calls)
        assert any("restart" in msg for msg in warning_calls)
    
    @patch('app.utils.config_listeners.logging')
    def test_performance_config_listener(self, mock_logging):
        """Test the performance configuration change listener."""
        old_config = {
            "openmemory": {"max_text_length": 1000},
            "mem0": {"llm": {"config": {"max_tokens": 2000}}}
        }
        
        new_config = {
            "openmemory": {"max_text_length": 8000},  # Large value should trigger warning
            "mem0": {"llm": {"config": {"max_tokens": 5000}}}  # Large value should trigger info
        }
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.NON_CRITICAL,
            requires_restart=False,
            config_version=3,
            timestamp=**********.0
        )
        
        # Call listener
        performance_config_listener(change_event)
        
        # Verify performance-related log messages
        mock_logging.info.assert_called()
        mock_logging.warning.assert_called()
        
        # Check for specific performance warnings
        warning_calls = [call[0][0] for call in mock_logging.warning.call_args_list]
        info_calls = [call[0][0] for call in mock_logging.info.call_args_list]
        
        assert any("8000" in msg and "performance" in msg for msg in warning_calls)
        assert any("5000" in msg for msg in info_calls)
    
    def test_register_default_listeners(self):
        """Test registration of default configuration listeners."""
        # Mock ConfigManager
        mock_config_manager = Mock()
        
        # Register default listeners
        register_default_listeners(mock_config_manager)
        
        # Verify that listeners were added
        assert mock_config_manager.add_change_listener.call_count == 4
        
        # Verify specific listeners were registered
        registered_listeners = [
            call[0][0] for call in mock_config_manager.add_change_listener.call_args_list
        ]
        
        listener_names = [listener.__name__ for listener in registered_listeners]
        
        assert "memory_client_config_listener" in listener_names
        assert "logging_config_listener" in listener_names
        assert "system_config_listener" in listener_names
        assert "performance_config_listener" in listener_names
    
    def test_listener_error_handling(self):
        """Test that listeners handle errors gracefully."""
        # Create a listener that raises an exception
        def failing_listener(event: ConfigChangeEvent):
            raise Exception("Test error")
        
        old_config = {"test": "old"}
        new_config = {"test": "new"}
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.NON_CRITICAL,
            requires_restart=False,
            config_version=1,
            timestamp=**********.0
        )
        
        # The listener should not raise an exception when called
        # (error handling should be done by the ConfigManager)
        try:
            failing_listener(change_event)
            assert False, "Expected exception was not raised"
        except Exception as e:
            assert str(e) == "Test error"
    
    @patch('app.utils.config_listeners.logging')
    def test_sensitive_data_redaction(self, mock_logging):
        """Test that sensitive configuration data is redacted in logs."""
        old_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "api_key": "old-secret-key",
                        "model": "gpt-4"
                    }
                }
            }
        }
        
        new_config = {
            "mem0": {
                "llm": {
                    "config": {
                        "api_key": "new-secret-key",
                        "model": "gpt-4-turbo"
                    }
                }
            }
        }
        
        change_event = ConfigChangeEvent(
            old_config=old_config,
            new_config=new_config,
            change_type=ConfigChangeType.CRITICAL,
            requires_restart=True,
            config_version=2,
            timestamp=**********.0
        )
        
        # Mock debug logging to be enabled
        mock_logger = Mock()
        mock_logger.isEnabledFor.return_value = True
        mock_logging.getLogger.return_value = mock_logger
        mock_logging.DEBUG = 10
        
        # Call system listener (which logs config differences in debug mode)
        system_config_listener(change_event)
        
        # Verify that sensitive data was redacted
        debug_calls = [call[0][0] for call in mock_logging.debug.call_args_list]
        
        # API key should be redacted
        api_key_logs = [msg for msg in debug_calls if "api_key" in msg]
        for log_msg in api_key_logs:
            assert "[REDACTED]" in log_msg
            assert "old-secret-key" not in log_msg
            assert "new-secret-key" not in log_msg
        
        # Non-sensitive data should not be redacted
        model_logs = [msg for msg in debug_calls if "model" in msg and "api_key" not in msg]
        for log_msg in model_logs:
            assert "gpt-4" in log_msg or "gpt-4-turbo" in log_msg
