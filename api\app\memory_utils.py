"""
Memory Utilities for OpenMemory

This module contains utility functions for memory operations including:
- Response validation functions
- Retry decorators for memory operations
- Memory verification functions
- Helper functions for memory processing
- Memory client access functions
- Configuration utilities

Separated from MCP server to provide reusable utility functions.
"""

import logging
import time
import functools
import uuid
from typing import Tuple, Any, Dict, List
from app.enhanced_logging import log_memory_operation
from app.database import SessionLocal


def get_memory_client_safe():
    """Get memory client with error handling. Returns None if client cannot be initialized."""
    try:
        from app.utils.memory import get_memory_client
        return get_memory_client()
    except Exception as e:
        logging.warning(f"Failed to get memory client: {e}")
        return None


def get_memory_singleton_safe():
    """Get memory client singleton with error handling. Returns None if client cannot be initialized."""
    try:
        from app.utils.memory import MemoryClientSingleton
        return MemoryClientSingleton()
    except Exception as e:
        logging.warning(f"Failed to get memory client singleton: {e}")
        return None


def get_max_text_length_from_config() -> int:
    """Get max_text_length from configuration, fallback to default if not available."""
    try:
        from app.models import Config as ConfigModel

        db = SessionLocal()
        try:
            config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
            if config and "openmemory" in config.value and "max_text_length" in config.value["openmemory"]:
                return config.value["openmemory"]["max_text_length"]
        finally:
            db.close()
    except Exception as e:
        logging.warning(f"Error getting max_text_length from config: {e}")

    # Fallback to default
    return 2000


def validate_text_length(text: str) -> tuple[bool, str]:
    """Validate text length for memory storage."""
    max_length = get_max_text_length_from_config()
    if len(text) > max_length:
        return False, f"Warning: Text is {len(text)} characters (over {max_length} limit). Will be automatically chunked for processing."
    return True, "Text length valid"


def chunk_text(text: str, max_length: int = None) -> list[str]:
    """
    Chunk text into smaller pieces for reliable memory storage.
    Implements smart chunking that preserves sentence boundaries when possible.
    """
    if max_length is None:
        max_length = get_max_text_length_from_config()

    # If text is already short enough, return as-is
    if len(text) <= max_length:
        return [text]

    logging.info(f"CHUNKING: Text length {len(text)} exceeds limit {max_length}, chunking...")

    chunks = []
    current_pos = 0

    while current_pos < len(text):
        # Calculate chunk end position
        chunk_end = min(current_pos + max_length, len(text))

        # If this is not the last chunk, try to break at sentence boundary
        if chunk_end < len(text):
            # Look for sentence endings within the last 200 chars of the chunk
            search_start = max(current_pos, chunk_end - 200)
            sentence_endings = []

            for i in range(search_start, chunk_end):
                if text[i] in '.!?':
                    # Check if this is likely a sentence ending (not abbreviation)
                    if i + 1 < len(text) and text[i + 1] in ' \n\t':
                        sentence_endings.append(i + 1)

            # Use the last sentence ending found, or fall back to word boundary
            if sentence_endings:
                chunk_end = sentence_endings[-1]
            else:
                # Look for word boundary within last 100 chars
                search_start = max(current_pos, chunk_end - 100)
                word_boundaries = []

                for i in range(search_start, chunk_end):
                    if text[i] in ' \n\t':
                        word_boundaries.append(i)

                if word_boundaries:
                    chunk_end = word_boundaries[-1]

        # Extract the chunk
        chunk = text[current_pos:chunk_end].strip()
        if chunk:  # Only add non-empty chunks
            chunks.append(chunk)
            logging.info(f"CHUNKING: Created chunk {len(chunks)} with length {len(chunk)}")

        current_pos = chunk_end

    logging.info(f"CHUNKING: Split {len(text)} chars into {len(chunks)} chunks")
    return chunks


def validate_mem0_response(response, operation_type="add_memory") -> Tuple[bool, str]:
    """Validate response from mem0 operations with improved error handling."""
    if not response:
        return False, f"{operation_type} failed: Empty response"
        
    if isinstance(response, dict) and response.get('error'):
        return False, f"{operation_type} failed: {response.get('error')}"
    
    if not isinstance(response, dict):
        return False, f"{operation_type} failed: Invalid response format (expected dict, got {type(response).__name__})"
    
    # Operation-specific validation
    if operation_type in ['add_memory', 'update_memory']:
        return _validate_add_update_response(response, operation_type)
    elif operation_type == 'get_memories':
        return _validate_get_response(response, operation_type)
    elif operation_type == 'search_memories':
        return _validate_search_response(response, operation_type)
    elif operation_type == 'delete_memory':
        return _validate_delete_response(response, operation_type)
    else:
        # Fallback to generic validation for unknown operation types
        return _validate_generic_response(response, operation_type)


def _validate_add_update_response(response, operation_type):
    """Validate add/update memory operation responses."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    if len(results) == 0:
        # Empty results can be valid (e.g., due to deduplication or LLM selectivity)
        return True, f"{operation_type} completed: No new memories stored (possibly due to deduplication)"
    
    # Validate each result has required fields
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'id' field"
        
        if 'memory' not in result and 'event' not in result:
            return False, f"{operation_type} failed: Result {i} missing 'memory' or 'event' field"
        
        # Validate memory ID format
        try:
            uuid.UUID(result['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Result {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) processed"


def _validate_get_response(response, operation_type):
    """Validate get memories operation responses."""
    if 'results' not in response:
        # For get operations, empty results might be valid
        return True, f"{operation_type} successful: No memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # Validate each memory in results
    for i, memory in enumerate(results):
        if not isinstance(memory, dict):
            return False, f"{operation_type} failed: Memory {i} is not a dict"
        
        if 'id' not in memory:
            return False, f"{operation_type} failed: Memory {i} missing 'id' field"
        
        # Validate memory ID format
        try:
            uuid.UUID(memory['id'])
        except (ValueError, TypeError):
            return False, f"{operation_type} failed: Memory {i} has invalid UUID format for 'id'"
    
    return True, f"{operation_type} successful: {len(results)} memory(ies) retrieved"


def _validate_search_response(response, operation_type):
    """Validate search memories operation responses."""
    if 'results' not in response:
        return True, f"{operation_type} successful: No matching memories found"
    
    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"
    
    # For search, empty results are valid
    if len(results) == 0:
        return True, f"{operation_type} successful: No matching memories found"
    
    # Validate each search result
    for i, result in enumerate(results):
        if not isinstance(result, dict):
            return False, f"{operation_type} failed: Search result {i} is not a dict"
        
        if 'id' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'id' field"
        
        # Search results should have memory content or score
        if 'memory' not in result and 'score' not in result:
            return False, f"{operation_type} failed: Search result {i} missing 'memory' or 'score' field"
    
    return True, f"{operation_type} successful: {len(results)} matching memory(ies) found"


def _validate_delete_response(response, operation_type):
    """Validate delete memory operation responses."""
    # Delete operations might have different response formats
    if isinstance(response, dict):
        if 'success' in response:
            return response['success'], f"{operation_type} {'successful' if response['success'] else 'failed'}"
        
        if 'results' in response:
            results = response['results']
            if isinstance(results, list) and len(results) > 0:
                return True, f"{operation_type} successful: {len(results)} memory(ies) deleted"
    
    # If we can't determine success/failure, assume success if no error
    return True, f"{operation_type} completed"


def _validate_generic_response(response, operation_type):
    """Generic validation for unknown operation types."""
    if 'results' not in response:
        return False, f"{operation_type} failed: No results in response"

    results = response['results']
    if not isinstance(results, list):
        return False, f"{operation_type} failed: Results is not a list"

    return True, f"{operation_type} successful"


def retry_operation(max_attempts=3, backoff_factor=1.5, retry_on_validation_failure=True):
    """
    Decorator to retry operations with exponential backoff.

    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Multiplier for exponential backoff
        retry_on_validation_failure: Whether to retry when validation fails
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            operation_name = getattr(func, '__name__', 'unknown_operation')

            for attempt in range(1, max_attempts + 1):
                try:
                    # Execute the operation
                    result = func(*args, **kwargs)

                    # If operation succeeded, validate the response if requested
                    if retry_on_validation_failure:
                        success, message = validate_mem0_response(result, operation_name)
                        if success:
                            if attempt > 1:
                                logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                            return result
                        else:
                            # Validation failed, treat as retriable error
                            last_exception = Exception(f"Validation failed: {message}")
                            logging.warning(f"Attempt {attempt}/{max_attempts} failed validation for {operation_name}: {message}")
                    else:
                        # No validation, return result directly
                        if attempt > 1:
                            logging.info(f"Operation {operation_name} succeeded on attempt {attempt}")
                        return result

                except Exception as e:
                    last_exception = e
                    logging.warning(f"Attempt {attempt}/{max_attempts} failed for {operation_name}: {str(e)}")

                # Don't sleep on the last attempt
                if attempt < max_attempts:
                    sleep_time = backoff_factor ** (attempt - 1)
                    logging.info(f"Retrying {operation_name} in {sleep_time:.2f} seconds...")
                    time.sleep(sleep_time)

            # If we get here, all attempts failed
            logging.error(f"Operation {operation_name} failed after {max_attempts} attempts. Last error: {str(last_exception)}")
            raise last_exception
        return wrapper
    return decorator


def retry_memory_operation(max_attempts=3):
    """Specialized retry decorator for memory operations with appropriate defaults."""
    return retry_operation(max_attempts=max_attempts, backoff_factor=1.5, retry_on_validation_failure=True)


# Retry-enabled memory operations with enhanced logging
@log_memory_operation("add_memory")
@retry_memory_operation(max_attempts=3)
def add_memory_with_retry(memory_client, text, user_id, metadata):
    """Add memory with retry logic and validation."""
    return memory_client.add(text, user_id=user_id, metadata=metadata)


@log_memory_operation("get_memories")
@retry_memory_operation(max_attempts=3)
def get_all_memories_with_retry(memory_client, user_id):
    """Get all memories with retry logic."""
    return memory_client.get_all(user_id=user_id)


@log_memory_operation("search_memories")
@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def search_memories_with_retry(memory_client, query, user_id, limit=10):
    """Search memories with retry logic (no validation since search has different response format)."""
    return memory_client.search(query, user_id=user_id, limit=limit)


@retry_operation(max_attempts=3, backoff_factor=1.2, retry_on_validation_failure=False)
def query_vector_store_with_retry(vector_store_client, collection_name, query, query_filter, limit):
    """Query vector store with retry logic."""
    return vector_store_client.query_points(
        collection_name=collection_name,
        query=query,
        query_filter=query_filter,
        limit=limit
    )


def verify_memory_storage(response, memory_client, request_id=None, user_id=None) -> Tuple[bool, str]:
    """Verify that memories were actually stored by attempting to retrieve them."""
    if not isinstance(response, dict) or 'results' not in response:
        return False, "Invalid response format for verification"
    
    results = response['results']
    if not isinstance(results, list):
        return False, "Invalid results format for verification"
    
    # Empty results might be valid (e.g., due to deduplication)
    if len(results) == 0:
        return True, "No new memories to verify (possibly due to deduplication)"
    
    stored_memory_ids = []
    for result in results:
        if isinstance(result, dict) and 'id' in result:
            stored_memory_ids.append(result['id'])
    
    if not stored_memory_ids:
        return False, "No memory IDs found in response for verification"
    
    # Wait for eventual consistency (increased for better reliability)
    time.sleep(0.5)
    
    # Attempt to retrieve stored memories to verify they exist (with retry)
    max_retries = 3
    for attempt in range(max_retries):
        try:
            verification_start = time.time()
            
            # Try to get all memories to see if our stored memories are present
            all_memories = memory_client.get_all(user_id=user_id)
            
            verification_duration = time.time() - verification_start
            
            if request_id:
                logging.info(f"[REQ_{request_id}] Memory verification attempt {attempt + 1} completed in {verification_duration:.3f}s")
            
            if not isinstance(all_memories, dict) or 'results' not in all_memories:
                if attempt < max_retries - 1:
                    time.sleep(0.2)  # Brief wait before retry
                    continue
                return False, "Unable to retrieve memories for verification"
            
            existing_memory_ids = set()
            for memory in all_memories['results']:
                if isinstance(memory, dict) and 'id' in memory:
                    existing_memory_ids.add(memory['id'])
            
            # Check if all stored memories are present
            missing_memories = []
            for memory_id in stored_memory_ids:
                if memory_id not in existing_memory_ids:
                    missing_memories.append(memory_id)
            
            if missing_memories:
                if attempt < max_retries - 1:
                    if request_id:
                        logging.info(f"[REQ_{request_id}] Verification attempt {attempt + 1}: {len(missing_memories)} memories not yet visible, retrying...")
                    time.sleep(0.3)  # Wait before retry
                    continue
                return False, f"Memory verification failed: {len(missing_memories)} memories not found after storage"
            
            if request_id:
                logging.info(f"[REQ_{request_id}] Memory verification successful: {len(stored_memory_ids)} memories confirmed stored")
            
            return True, f"Memory verification successful: {len(stored_memory_ids)} memories confirmed stored"
        
        except Exception as e:
            error_msg = f"Memory verification failed due to error: {str(e)}"
            if request_id:
                logging.error(f"[REQ_{request_id}] {error_msg}")
            return False, error_msg
