#!/bin/bash

# Script to start Memory Master v2 with correct ports for testing
# API on port 5678, Mock-UI on port 3001

echo "🚀 Starting Memory Master v2 for local testing..."

# Change to project root
cd "$(dirname "$0")"

# Check if we're in the right directory
if [ ! -d "api" ] || [ ! -d "mock-ui" ]; then
    echo "❌ Error: Please run this script from the memory-master-v2 root directory"
    exit 1
fi

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker stop memory-api-local memory-ui-local 2>/dev/null || true
docker rm memory-api-local memory-ui-local 2>/dev/null || true

# Kill any processes on our target ports
echo "🧹 Freeing up ports 5678 and 3001..."
lsof -ti :5678 | xargs kill -9 2>/dev/null || true
lsof -ti :3001 | xargs kill -9 2>/dev/null || true

sleep 2

# Start API using Docker
echo "🔧 Building and starting API server on port 5678..."
cd api
docker build -t memory-api-local . 
if [ $? -ne 0 ]; then
    echo "❌ Failed to build API Docker image"
    exit 1
fi

docker run -d \
  --name memory-api-local \
  -p 5678:8765 \
  --env-file .env \
  memory-api-local

if [ $? -ne 0 ]; then
    echo "❌ Failed to start API container"
    exit 1
fi

echo "✅ API server started successfully"

# Wait for API to be ready
echo "⏳ Waiting for API to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:5678/health >/dev/null 2>&1; then
        echo "✅ API is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ API failed to start within 30 seconds"
        docker logs memory-api-local
        exit 1
    fi
    sleep 1
done

# Start Mock-UI
echo "🔧 Starting Mock-UI on port 3001..."
cd ../mock-ui

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the UI
npm run dev &
UI_PID=$!

echo "✅ Started processes:"
echo "   📡 API Server: http://localhost:5678 (Docker: memory-api-local)"
echo "   🖥️  UI Server: http://localhost:3001 (PID: $UI_PID)"
echo ""
echo "🧪 Ready for testing! Check the TESTING_GUIDE.md for test scenarios."
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    docker stop memory-api-local >/dev/null 2>&1
    docker rm memory-api-local >/dev/null 2>&1
    kill $UI_PID 2>/dev/null || true
    echo "✅ Cleanup complete"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Wait for user interrupt
wait