'use client'

import { LoginForm } from '@/components/auth/LoginForm'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function LoginPage() {
  const router = useRouter()
  const supabase = createClient()

  const handleLogin = async (data: { email: string; password: string }) => {
    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })
      
      if (error) {
        return { error: error.message }
      }
      
      if (authData.user) {
        router.push('/')
        return {}
      }
      
      return { error: 'Lo<PERSON> failed - no user returned' }
    } catch (error) {
      return { error: '<PERSON><PERSON> failed. Please try again.' }
    }
  }

  return <LoginForm onSubmit={handleLogin} />
}