"""
Test suite for retry logic implementation in MCP server.

This module tests the retry functionality added in Task 5,
including exponential backoff, validation-based retries, and error handling.
"""

import unittest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.mcp_server import (
    retry_operation,
    retry_memory_operation,
    add_memory_with_retry,
    get_all_memories_with_retry,
    search_memories_with_retry,
    query_vector_store_with_retry
)


class TestRetryLogic(unittest.TestCase):
    """Test retry logic functionality."""
    
    def setUp(self):
        """Set up test environment before each test."""
        pass
    
    def tearDown(self):
        """Clean up after each test."""
        pass
    
    def test_retry_decorator_success_first_attempt(self):
        """Test that retry decorator works when operation succeeds on first attempt."""
        
        @retry_operation(max_attempts=3)
        def successful_operation():
            return {"success": True, "data": "test"}
        
        start_time = time.time()
        result = successful_operation()
        duration = time.time() - start_time
        
        self.assertEqual(result, {"success": True, "data": "test"})
        self.assertLess(duration, 0.1, "Should complete quickly on first attempt")
    
    def test_retry_decorator_success_after_retries(self):
        """Test that retry decorator retries and eventually succeeds."""
        
        call_count = 0
        
        @retry_operation(max_attempts=3, backoff_factor=1.1, retry_on_validation_failure=False)
        def flaky_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception(f"Attempt {call_count} failed")
            return {"success": True, "attempt": call_count}
        
        start_time = time.time()
        result = flaky_operation()
        duration = time.time() - start_time
        
        self.assertEqual(result, {"success": True, "attempt": 3})
        self.assertEqual(call_count, 3)
        self.assertGreater(duration, 1.0, "Should take time due to backoff")
    
    def test_retry_decorator_max_attempts_exceeded(self):
        """Test that retry decorator fails after max attempts."""
        
        call_count = 0
        
        @retry_operation(max_attempts=2, backoff_factor=1.1)
        def always_failing_operation():
            nonlocal call_count
            call_count += 1
            raise Exception(f"Attempt {call_count} failed")
        
        with self.assertRaises(Exception) as context:
            always_failing_operation()
        
        self.assertEqual(call_count, 2)
        self.assertIn("Attempt 2 failed", str(context.exception))
    
    def test_retry_decorator_exponential_backoff(self):
        """Test that retry decorator uses exponential backoff."""
        
        call_times = []
        
        @retry_operation(max_attempts=3, backoff_factor=2.0, retry_on_validation_failure=False)
        def timing_test_operation():
            call_times.append(time.time())
            if len(call_times) < 3:
                raise Exception("Not ready yet")
            return {"success": True}
        
        start_time = time.time()
        result = timing_test_operation()
        
        self.assertEqual(result, {"success": True})
        self.assertEqual(len(call_times), 3)
        
        # Check backoff timing (approximately)
        first_gap = call_times[1] - call_times[0]
        second_gap = call_times[2] - call_times[1]
        
        # Second gap should be roughly twice the first gap (2.0 backoff factor)
        self.assertGreater(second_gap, first_gap * 1.5)
    
    @patch('app.mcp_server.validate_mem0_response')
    def test_retry_with_validation_failure(self, mock_validate):
        """Test retry behavior when validation fails."""
        
        call_count = 0
        
        def mock_operation():
            nonlocal call_count
            call_count += 1
            return {"results": [{"id": f"test-{call_count}"}]}
        
        # First two calls fail validation, third succeeds
        mock_validate.side_effect = [
            (False, "Validation failed attempt 1"),
            (False, "Validation failed attempt 2"),
            (True, "Validation successful")
        ]
        
        @retry_operation(max_attempts=3, backoff_factor=1.1, retry_on_validation_failure=True)
        def operation_with_validation():
            return mock_operation()
        
        result = operation_with_validation()
        
        self.assertEqual(call_count, 3)
        self.assertEqual(mock_validate.call_count, 3)
        self.assertEqual(result, {"results": [{"id": "test-3"}]})
    
    @patch('app.mcp_server.validate_mem0_response')
    def test_retry_memory_operation_decorator(self, mock_validate):
        """Test the specialized retry_memory_operation decorator."""
        
        mock_validate.return_value = (True, "Success")
        
        @retry_memory_operation(max_attempts=2)
        def test_memory_operation():
            return {"results": [{"id": "memory-test"}]}
        
        result = test_memory_operation()
        
        self.assertEqual(result, {"results": [{"id": "memory-test"}]})
        mock_validate.assert_called_once()
    
    def test_add_memory_with_retry(self):
        """Test add_memory_with_retry function."""
        
        mock_client = Mock()
        mock_client.add.return_value = {"results": [{"id": "test-memory", "event": "ADD"}]}
        
        with patch('app.mcp_server.validate_mem0_response', return_value=(True, "Success")):
            result = add_memory_with_retry(
                mock_client,
                "test content",
                user_id="test-user",
                metadata={"test": "metadata"}
            )
        
        self.assertEqual(result, {"results": [{"id": "test-memory", "event": "ADD"}]})
        mock_client.add.assert_called_once_with(
            "test content",
            user_id="test-user",
            metadata={"test": "metadata"}
        )
    
    def test_get_all_memories_with_retry(self):
        """Test get_all_memories_with_retry function."""
        
        mock_client = Mock()
        mock_client.get_all.return_value = {"results": [{"id": "memory-1"}, {"id": "memory-2"}]}
        
        with patch('app.mcp_server.validate_mem0_response', return_value=(True, "Success")):
            result = get_all_memories_with_retry(mock_client, user_id="test-user")
        
        self.assertEqual(result, {"results": [{"id": "memory-1"}, {"id": "memory-2"}]})
        mock_client.get_all.assert_called_once_with(user_id="test-user")
    
    def test_search_memories_with_retry(self):
        """Test search_memories_with_retry function."""
        
        mock_client = Mock()
        mock_client.search.return_value = [{"id": "search-result-1"}, {"id": "search-result-2"}]
        
        result = search_memories_with_retry(
            mock_client,
            "test query",
            user_id="test-user",
            limit=5
        )
        
        self.assertEqual(result, [{"id": "search-result-1"}, {"id": "search-result-2"}])
        mock_client.search.assert_called_once_with(
            "test query",
            user_id="test-user",
            limit=5
        )
    
    def test_query_vector_store_with_retry(self):
        """Test query_vector_store_with_retry function."""
        
        mock_client = Mock()
        mock_client.query_points.return_value = Mock(points=[
            Mock(id="point-1", score=0.9),
            Mock(id="point-2", score=0.8)
        ])
        
        result = query_vector_store_with_retry(
            mock_client,
            "test-collection",
            [0.1, 0.2, 0.3],  # query vector
            {"must": []},     # filter
            10                # limit
        )
        
        mock_client.query_points.assert_called_once_with(
            collection_name="test-collection",
            query=[0.1, 0.2, 0.3],
            query_filter={"must": []},
            limit=10
        )
    
    def test_retry_with_different_exception_types(self):
        """Test retry behavior with different types of exceptions."""
        
        call_count = 0
        
        @retry_operation(max_attempts=4, backoff_factor=1.1, retry_on_validation_failure=False)
        def multi_exception_operation():
            nonlocal call_count
            call_count += 1
            
            if call_count == 1:
                raise ConnectionError("Network error")
            elif call_count == 2:
                raise TimeoutError("Request timeout")
            elif call_count == 3:
                raise ValueError("Invalid data")
            else:
                return {"success": True, "attempts": call_count}
        
        result = multi_exception_operation()
        
        self.assertEqual(result, {"success": True, "attempts": 4})
        self.assertEqual(call_count, 4)
    
    def test_retry_preserves_function_metadata(self):
        """Test that retry decorator preserves function metadata."""
        
        @retry_operation(max_attempts=2)
        def documented_function():
            """This function has documentation."""
            return "success"
        
        self.assertEqual(documented_function.__name__, "documented_function")
        self.assertEqual(documented_function.__doc__, "This function has documentation.")
    
    def test_concurrent_retry_operations(self):
        """Test that retry operations work correctly when called concurrently."""
        
        import threading
        import queue
        
        results = queue.Queue()
        errors = queue.Queue()
        
        call_counts = {}
        
        @retry_operation(max_attempts=3, backoff_factor=1.1, retry_on_validation_failure=False)
        def concurrent_operation(thread_id):
            if thread_id not in call_counts:
                call_counts[thread_id] = 0
            call_counts[thread_id] += 1
            
            # Fail first attempt, succeed on second
            if call_counts[thread_id] == 1:
                raise Exception(f"Thread {thread_id} first attempt failed")
            
            return {"thread_id": thread_id, "attempts": call_counts[thread_id]}
        
        def worker(thread_id):
            try:
                result = concurrent_operation(thread_id)
                results.put(result)
            except Exception as e:
                errors.put((thread_id, str(e)))
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(errors.qsize(), 0, "No errors should occur")
        self.assertEqual(results.qsize(), 5, "All threads should succeed")
        
        # Verify each thread made exactly 2 attempts
        for thread_id in range(5):
            self.assertEqual(call_counts[thread_id], 2)


if __name__ == '__main__':
    unittest.main()
