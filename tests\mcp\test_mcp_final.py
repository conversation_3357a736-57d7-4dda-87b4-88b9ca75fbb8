#!/usr/bin/env python3
"""
Final comprehensive test for MCP server via supergateway
"""
import subprocess
import json
import sys
import logging
import time
import requests
from threading import Thread
import signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_api_health():
    """Test API server health"""
    try:
        response = requests.get("http://localhost:8765/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ API Health: {data}")
            return True
        else:
            logger.error(f"❌ API Health failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ API Health error: {e}")
        return False

def test_mcp_health():
    """Test MCP server health"""
    try:
        response = requests.get("http://localhost:8765/mcp/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ MCP Health: {data}")
            return True
        else:
            logger.error(f"❌ MCP Health failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ MCP Health error: {e}")
        return False

def test_sse_endpoint():
    """Test SSE endpoint connectivity"""
    try:
        url = "http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY"
        response = requests.get(url, timeout=3, stream=True)
        if response.status_code == 200:
            # Read first chunk to verify SSE is working
            chunk = next(response.iter_content(chunk_size=1024))
            if chunk:
                logger.info(f"✅ SSE Endpoint: Connected and receiving data")
                return True
        logger.error(f"❌ SSE Endpoint failed: {response.status_code}")
        return False
    except Exception as e:
        logger.error(f"❌ SSE Endpoint error: {e}")
        return False

def test_supergateway_connection():
    """Test supergateway connection"""
    try:
        cmd = [
            "npx", "-y", "supergateway@latest",
            "--sse", 
            "http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY",
            "--timeout", "120000",
            "--logLevel", "info"
        ]
        
        logger.info("🔗 Testing supergateway connection...")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it time to connect
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            logger.info("✅ Supergateway: Connection established and running")
            success = True
        else:
            stdout, stderr = process.communicate()
            logger.error(f"❌ Supergateway failed to connect")
            logger.error(f"STDERR: {stderr}")
            success = False
        
        # Clean up
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
            
        return success
        
    except Exception as e:
        logger.error(f"❌ Supergateway connection error: {e}")
        return False

def test_mcp_configuration():
    """Test MCP configuration file"""
    try:
        with open('/home/<USER>/wsl_dev/memory-master/.mcp.json', 'r') as f:
            config = json.load(f)
        
        if 'memory-mcp' in config.get('mcpServers', {}):
            mcp_config = config['mcpServers']['memory-mcp']
            
            # Check required fields
            required_fields = ['command', 'args', 'disabled']
            missing_fields = [field for field in required_fields if field not in mcp_config]
            
            if missing_fields:
                logger.error(f"❌ MCP Config: Missing fields: {missing_fields}")
                return False
            
            # Check if it's enabled
            if mcp_config.get('disabled', False):
                logger.error("❌ MCP Config: Server is disabled")
                return False
            
            # Check command and args
            if mcp_config['command'] != 'npx':
                logger.error("❌ MCP Config: Command should be 'npx'")
                return False
            
            expected_args = [
                '-y', 'supergateway@latest', '--sse',
                'http://localhost:8765/mcp/claude/sse/aungheinaye?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY',
                '--timeout', '120000'
            ]
            
            if mcp_config['args'] != expected_args:
                logger.error("❌ MCP Config: Args don't match expected values")
                return False
            
            logger.info("✅ MCP Config: Configuration is correct")
            return True
        else:
            logger.error("❌ MCP Config: 'memory-mcp' server not found in configuration")
            return False
            
    except Exception as e:
        logger.error(f"❌ MCP Config error: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Starting final MCP server tests")
    
    tests = [
        ("API Health", test_api_health),
        ("MCP Health", test_mcp_health),
        ("SSE Endpoint", test_sse_endpoint),
        ("MCP Configuration", test_mcp_configuration),
        ("Supergateway Connection", test_supergateway_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"🧪 Running test: {test_name}")
        result = test_func()
        results.append((test_name, result))
        logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        print()  # Add spacing between tests
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    logger.info(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! MCP server is fully operational with supergateway.")
        logger.info("✅ Claude Desktop should now be able to connect to the memory-mcp server.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)