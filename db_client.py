#!/usr/bin/env python3
"""
Simple database client for executing queries
Requires: pip install psycopg2-binary
"""

import psycopg2
from psycopg2 import sql

# Database connection parameters
DB_CONFIG = {
    'host': '*************',
    'port': 54322,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'yeGeIy8eFJgfuPpFh1dtAzBXz8YL7dJT'
}

def execute_query(query, params=None):
    """Execute a query and return results"""
    conn = None
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        if params:
            cur.execute(query, params)
        else:
            cur.execute(query)
        
        # Try to fetch results
        try:
            results = cur.fetchall()
            columns = [desc[0] for desc in cur.description] if cur.description else []
            return columns, results
        except:
            # For queries that don't return results (INSERT, UPDATE, etc.)
            conn.commit()
            return [], []
            
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
        return [], []
    finally:
        if conn:
            conn.close()

def print_results(columns, results):
    """Pretty print query results"""
    if not columns:
        print("Query executed successfully")
        return
        
    # Calculate column widths
    widths = []
    for i, col in enumerate(columns):
        max_width = len(str(col))
        for row in results:
            val = str(row[i]) if row[i] is not None else "NULL"
            max_width = max(max_width, len(val))
        widths.append(min(max_width, 50))
    
    # Print header
    header = " | ".join(str(col).ljust(widths[i])[:widths[i]] for i, col in enumerate(columns))
    print(header)
    print("-" * len(header))
    
    # Print rows
    for row in results:
        row_str = " | ".join(
            (str(val) if val is not None else "NULL").ljust(widths[i])[:widths[i]] 
            for i, val in enumerate(row)
        )
        print(row_str)
    
    print(f"\nTotal rows: {len(results)}")

# Example usage
if __name__ == "__main__":
    # Example: Get all users
    query = """
    SELECT user_id, name, email, created_at 
    FROM memory_master.users 
    ORDER BY created_at DESC
    """
    
    print("Fetching users...")
    columns, results = execute_query(query)
    print_results(columns, results)