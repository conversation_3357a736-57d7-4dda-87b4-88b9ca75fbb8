#!/usr/bin/env python3
"""
Performance Monitoring Script for Memory Master v2
Run this script to check performance metrics and identify bottlenecks.
"""

import asyncio
import time
import psutil
import requests
import json
from typing import Dict, List, Optional
from datetime import datetime
import os


class PerformanceMonitor:
    def __init__(self):
        self.api_base_url = os.getenv('NEXT_PUBLIC_API_URL', 'http://localhost:8765')
        self.ui_url = os.getenv('NEXT_PUBLIC_UI_URL', 'http://localhost:3210')
        
    async def check_api_performance(self) -> Dict:
        """Check API endpoint performance"""
        results = {}
        
        endpoints = [
            '/health',
            '/api/v1/memories',
            '/api/v1/sync/status',
            '/api/v1/evolution/operations',
        ]
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.api_base_url}{endpoint}", timeout=30)
                end_time = time.time()
                
                results[endpoint] = {
                    'status_code': response.status_code,
                    'response_time_ms': round((end_time - start_time) * 1000, 2),
                    'size_bytes': len(response.content),
                    'success': response.status_code == 200
                }
                
                if not response.ok:
                    results[endpoint]['error'] = response.text[:200]
                    
            except Exception as e:
                results[endpoint] = {
                    'error': str(e),
                    'success': False
                }
                
        return results
    
    def check_system_resources(self) -> Dict:
        """Check system resource usage"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
    
    def check_database_performance(self) -> Dict:
        """Check database performance metrics"""
        try:
            # This would need actual database connection
            # For now, return placeholder
            return {
                'active_connections': 'N/A - Need DB connection',
                'slow_queries': 'N/A - Need DB connection',
                'cache_hit_ratio': 'N/A - Need DB connection'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def check_frontend_performance(self) -> Dict:
        """Check frontend performance"""
        try:
            start_time = time.time()
            response = requests.get(self.ui_url, timeout=30)
            end_time = time.time()
            
            return {
                'status_code': response.status_code,
                'response_time_ms': round((end_time - start_time) * 1000, 2),
                'size_bytes': len(response.content),
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    async def run_performance_check(self) -> Dict:
        """Run comprehensive performance check"""
        print("🔍 Running Memory Master Performance Check...")
        print("=" * 50)
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'system_resources': self.check_system_resources(),
            'api_performance': await self.check_api_performance(),
            'database_performance': self.check_database_performance(),
            'frontend_performance': self.check_frontend_performance()
        }
        
        # Print results
        self.print_results(results)
        
        return results
    
    def print_results(self, results: Dict):
        """Print performance results in a readable format"""
        print("\n📊 PERFORMANCE RESULTS")
        print("=" * 50)
        
        # System Resources
        print("\n🖥️  SYSTEM RESOURCES:")
        sys_res = results['system_resources']
        print(f"  CPU Usage: {sys_res['cpu_percent']}%")
        print(f"  Memory Usage: {sys_res['memory_percent']}%")
        print(f"  Disk Usage: {sys_res['disk_percent']}%")
        if sys_res['load_average']:
            print(f"  Load Average: {sys_res['load_average']}")
        
        # API Performance
        print("\n🔗 API PERFORMANCE:")
        api_perf = results['api_performance']
        for endpoint, metrics in api_perf.items():
            status = "✅" if metrics.get('success') else "❌"
            response_time = metrics.get('response_time_ms', 'N/A')
            print(f"  {status} {endpoint}: {response_time}ms")
            if 'error' in metrics:
                print(f"    Error: {metrics['error']}")
        
        # Frontend Performance
        print("\n🖼️  FRONTEND PERFORMANCE:")
        frontend = results['frontend_performance']
        if frontend.get('success'):
            print(f"  ✅ Load Time: {frontend['response_time_ms']}ms")
            print(f"  📦 Size: {frontend['size_bytes']} bytes")
        else:
            print(f"  ❌ Error: {frontend.get('error', 'Unknown error')}")
        
        # Performance Recommendations
        print("\n💡 PERFORMANCE RECOMMENDATIONS:")
        self.print_recommendations(results)
    
    def print_recommendations(self, results: Dict):
        """Print performance recommendations"""
        recommendations = []
        
        # Check API response times
        api_perf = results['api_performance']
        slow_endpoints = [
            endpoint for endpoint, metrics in api_perf.items()
            if metrics.get('response_time_ms', 0) > 500
        ]
        
        if slow_endpoints:
            recommendations.append(f"🐌 Slow API endpoints detected: {', '.join(slow_endpoints)}")
            recommendations.append("   Consider optimizing database queries and adding caching")
        
        # Check system resources
        sys_res = results['system_resources']
        if sys_res['cpu_percent'] > 80:
            recommendations.append("⚠️  High CPU usage detected")
        if sys_res['memory_percent'] > 80:
            recommendations.append("⚠️  High memory usage detected")
        
        # Check frontend performance
        frontend = results['frontend_performance']
        if frontend.get('response_time_ms', 0) > 2000:
            recommendations.append("🐌 Slow frontend loading detected")
            recommendations.append("   Consider implementing the bundle optimizations")
        
        if not recommendations:
            recommendations.append("✅ No major performance issues detected!")
        
        for rec in recommendations:
            print(f"  {rec}")
        
        print("\n🚀 OPTIMIZATION CHECKLIST:")
        print("  □ Run database performance indexes (performance_indexes.sql)")
        print("  □ Restart services after bundle optimizations")
        print("  □ Monitor memory usage during peak times")
        print("  □ Check Qdrant vector database performance")
        print("  □ Implement Redis caching for frequently accessed data")


async def main():
    """Main function to run performance monitoring"""
    monitor = PerformanceMonitor()
    
    try:
        results = await monitor.run_performance_check()
        
        # Save results to file
        with open('performance_report.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📄 Full report saved to: performance_report.json")
        
    except Exception as e:
        print(f"❌ Error running performance check: {e}")


if __name__ == "__main__":
    asyncio.run(main())