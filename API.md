# Memory Master API Documentation

## Overview

The Memory Master API provides a comprehensive RESTful interface for managing memories, users, apps, and system configuration. The API is built with FastAPI and follows OpenAPI 3.0 specifications with automatic documentation generation.

### Base URL
- **Development**: `http://localhost:8765`
- **Production**: `http://your-domain:8765`

### API Documentation
- **Swagger UI**: `http://localhost:8765/docs`
- **ReDoc**: `http://localhost:8765/redoc`
- **OpenAPI JSON**: `http://localhost:8765/openapi.json`

## Authentication

Memory Master uses a hybrid authentication system:

### Authentication Types
- **Supabase Auth**: Full authentication with user management
- **Default User**: Fallback for development and testing
- **MCP Protocol**: Special authentication for Claude Desktop integration

### Authentication Headers
```http
Authorization: Bearer <supabase_jwt_token>
X-API-Key: <optional_api_key>
```

### User Context
All API endpoints automatically detect the current user context and filter data accordingly.

## Core Endpoints

### 1. Memory Management

#### List Memories
```http
GET /api/v1/memories/
```

**Query Parameters:**
- `app_id` (UUID): Filter by specific app
- `from_date` (ISO timestamp): Filter from date
- `to_date` (ISO timestamp): Filter to date
- `categories` (string): Comma-separated category filter
- `search_query` (string): Full-text search
- `sort_column` (string): Sort field (created_at, updated_at, text)
- `sort_direction` (string): Sort order (asc, desc)
- `page` (int): Page number (default: 1)
- `size` (int): Page size (default: 10)

**Response:**
```json
{
  "items": [
    {
      "id": "uuid",
      "text": "Memory content",
      "metadata": {},
      "categories": ["category1", "category2"],
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "user_id": "user_uuid",
      "app_id": "app_uuid",
      "app_name": "App Name",
      "state": "active",
      "access_count": 5,
      "last_accessed_at": "2023-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "size": 10,
  "pages": 10
}
```

#### Create Memory
```http
POST /api/v1/memories/
```

**Request Body:**
```json
{
  "text": "Memory content to store",
  "metadata": {
    "source": "user_input",
    "priority": "high"
  },
  "infer": true,
  "app": "app_name"
}
```

**Response:**
```json
{
  "id": "uuid",
  "text": "Memory content",
  "metadata": {},
  "categories": ["inferred_category"],
  "created_at": "2023-01-01T00:00:00Z",
  "user_id": "user_uuid",
  "app_id": "app_uuid",
  "chunked": false,
  "chunk_info": null
}
```

**Features:**
- Automatic text chunking for content >2000 words
- Category inference using AI
- App validation and creation
- Metadata preservation

#### Get Memory Details
```http
GET /api/v1/memories/{memory_id}
```

**Response:**
```json
{
  "id": "uuid",
  "text": "Memory content",
  "metadata": {},
  "categories": ["category1"],
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "user_id": "user_uuid",
  "app_id": "app_uuid",
  "app_name": "App Name",
  "state": "active",
  "access_count": 10,
  "last_accessed_at": "2023-01-01T00:00:00Z"
}
```

#### Update Memory
```http
PUT /api/v1/memories/{memory_id}
```

**Request Body:**
```json
{
  "title": "Updated memory title",
  "content": "Updated memory content",
  "metadata": {
    "updated_field": "new_value"
  }
}
```

#### Delete Memory
```http
DELETE /api/v1/memories/{memory_id}
```

**Response:**
```json
{
  "message": "Memory deleted successfully"
}
```

#### Bulk Delete Memories
```http
DELETE /api/v1/memories/
```

**Request Body:**
```json
{
  "memory_ids": ["uuid1", "uuid2", "uuid3"]
}
```

### 2. Memory Categories

#### Get All Categories
```http
GET /api/v1/memories/categories
```

**Response:**
```json
{
  "categories": ["work", "personal", "technical", "business"],
  "total_count": 4
}
```

### 3. Memory Actions

#### Archive Memories
```http
POST /api/v1/memories/actions/archive
```

**Request Body:**
```json
{
  "memory_ids": ["uuid1", "uuid2"],
  "user_id": "user_uuid"
}
```

#### Pause Memory Access
```http
POST /api/v1/memories/actions/pause
```

**Request Body:**
```json
{
  "pause_type": "global",
  "app_id": "uuid",
  "category": "work",
  "memory_ids": ["uuid1", "uuid2"],
  "duration_hours": 24
}
```

**Pause Types:**
- `global`: Pause all memories
- `app`: Pause memories for specific app
- `category`: Pause memories in category
- `selective`: Pause specific memories

### 4. Authentication

#### Get Auth Status
```http
GET /auth/status
```

**Response:**
```json
{
  "authenticated": true,
  "user": {
    "id": "uuid",
    "user_id": "string_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "created_at": "2023-01-01T00:00:00Z"
  },
  "auth_provider": "supabase",
  "session_valid": true
}
```

#### Get User Profile
```http
GET /auth/profile
```

#### Update User Profile
```http
PUT /auth/profile
```

**Request Body:**
```json
{
  "name": "New Name"
}
```

### 5. Configuration Management

#### Get Configuration
```http
GET /api/v1/config/
```

**Response:**
```json
{
  "mem0": {
    "version": "v1.1",
    "llm": {
      "provider": "openai",
      "config": {
        "model": "gpt-4o-mini",
        "temperature": 0.1,
        "max_tokens": 2000
      }
    },
    "embedder": {
      "provider": "openai",
      "config": {
        "model": "text-embedding-3-small"
      }
    }
  },
  "openmemory": {
    "custom_instructions": "Be helpful and accurate",
    "max_text_length": 2000
  }
}
```

#### Update Configuration
```http
PUT /api/v1/config/
```

**Request Body:** Same as GET response structure

**Response:**
```json
{
  "success": true,
  "message": "Configuration updated successfully",
  "hot_reload_applied": true,
  "config_version": "v1.2.3"
}
```

#### Reset Configuration
```http
POST /api/v1/config/reset
```

### 6. Evolution Intelligence

#### Get Evolution Metrics
```http
GET /evolution-config/metrics
```

**Query Parameters:**
- `timeframe`: `day`, `week`, `month`, `year`

**Response:**
```json
{
  "learning_efficiency": 0.85,
  "conflict_resolution_rate": 0.92,
  "memory_quality_score": 0.88,
  "total_operations": 150,
  "successful_operations": 138,
  "timeframe": "week",
  "generated_at": "2023-01-01T00:00:00Z"
}
```

#### Get Evolution Analytics
```http
GET /evolution-config/analytics
```

**Response:**
```json
{
  "items": [
    {
      "id": "uuid",
      "operation_type": "ADD",
      "confidence_score": 0.95,
      "reasoning": "High confidence based on context",
      "created_at": "2023-01-01T00:00:00Z",
      "metadata": {}
    }
  ],
  "total": 50,
  "page": 1,
  "size": 10
}
```

#### Evolution Configuration Management
```http
POST /evolution-config/
GET /evolution-config/
PUT /evolution-config/{config_id}
DELETE /evolution-config/{config_id}
```

### 7. Synchronization

#### Get Sync Status
```http
GET /api/v1/sync/counts
```

**Response:**
```json
{
  "postgresql_count": 150,
  "qdrant_count": 148,
  "difference": 2,
  "sync_status": "out_of_sync",
  "last_sync": "2023-01-01T00:00:00Z",
  "sync_health": "good"
}
```

#### Start Synchronization
```http
POST /api/v1/sync/start
```

**Response:**
```json
{
  "sync_id": "uuid",
  "status": "started",
  "message": "Synchronization started"
}
```

#### Get Sync Status
```http
GET /api/v1/sync/status/{sync_id}
```

**Response:**
```json
{
  "sync_id": "uuid",
  "status": "in_progress",
  "progress": 0.75,
  "processed_records": 112,
  "total_records": 150,
  "errors": [],
  "started_at": "2023-01-01T00:00:00Z",
  "estimated_completion": "2023-01-01T00:05:00Z"
}
```

### 8. App Management

#### List Apps
```http
GET /api/v1/apps/
```

**Query Parameters:**
- `name` (string): Filter by app name
- `is_active` (boolean): Filter by active status
- `sort_by` (string): Sort field
- `page`, `size`: Pagination

**Response:**
```json
{
  "items": [
    {
      "id": "uuid",
      "name": "App Name",
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z",
      "memory_count": 25,
      "last_accessed": "2023-01-01T00:00:00Z",
      "access_count": 100
    }
  ],
  "total": 5,
  "page": 1,
  "size": 10
}
```

#### Get App Details
```http
GET /api/v1/apps/{app_id}
```

#### Update App
```http
PUT /api/v1/apps/{app_id}
```

**Request Body:**
```json
{
  "is_active": false
}
```

#### Get App Memories
```http
GET /api/v1/apps/{app_id}/memories
```

**Response:** Same as memory list format

### 9. Health Monitoring

#### System Health Check
```http
GET /health/
```

**Response:**
```json
{
  "overall_health": "healthy",
  "components": {
    "database": {
      "status": "healthy",
      "response_time_ms": 15,
      "connection_pool": "available"
    },
    "memory_engine": {
      "status": "healthy",
      "client_initialized": true,
      "collections_available": true
    },
    "vector_store": {
      "status": "healthy",
      "qdrant_connection": "active",
      "collections_count": 3
    },
    "evolution_service": {
      "status": "healthy",
      "queue_size": 0,
      "success_rate": 0.95
    }
  },
  "metrics": {
    "uptime_seconds": 86400,
    "memory_usage_mb": 256,
    "active_connections": 5
  },
  "timestamp": "2023-01-01T00:00:00Z"
}
```

#### Component Health Checks
```http
GET /health/database
GET /health/memory-engine
GET /health/evolution-service
GET /health/qdrant
GET /health/qdrant/collections
```

#### Component Recovery
```http
POST /health/recovery/{component}
```

**Components:** `memory_engine`, `vector_store`, `evolution_service`, `prompt_system`

### 10. Statistics

#### Get User Statistics
```http
GET /api/v1/stats
```

**Response:**
```json
{
  "total_memories": 150,
  "total_apps": 5,
  "apps": [
    {
      "id": "uuid",
      "name": "App Name",
      "memory_count": 30
    }
  ]
}
```

## MCP Protocol Integration

### MCP Server Endpoints

#### SSE Connection for Claude Desktop
```http
GET /mcp/claude/sse/{user_id}
```

#### Generic MCP Client Connection
```http
GET /mcp/{client_name}/sse/{user_id}
```

#### MCP Health Check
```http
GET /mcp/health
```

### MCP Tools

The API provides the following MCP tools for integration with Claude Desktop and other MCP clients:

#### add_memories
```json
{
  "name": "add_memories",
  "description": "Add new memories to the system with automatic chunking",
  "inputSchema": {
    "type": "object",
    "properties": {
      "text": {
        "type": "string",
        "description": "Memory content to store"
      }
    },
    "required": ["text"]
  }
}
```

#### search_memory
```json
{
  "name": "search_memory",
  "description": "Search memories using semantic search",
  "inputSchema": {
    "type": "object",
    "properties": {
      "query": {
        "type": "string",
        "description": "Search query"
      }
    },
    "required": ["query"]
  }
}
```

#### list_memories
```json
{
  "name": "list_memories",
  "description": "List all memories with optional filtering",
  "inputSchema": {
    "type": "object",
    "properties": {},
    "required": []
  }
}
```

#### get_system_health
```json
{
  "name": "get_system_health",
  "description": "Get system health status",
  "inputSchema": {
    "type": "object",
    "properties": {},
    "required": []
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "detail": "Error message",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

### Common HTTP Status Codes
- `200 OK`: Successful request
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error

### Error Categories
- **Validation Errors**: Invalid input data
- **Authentication Errors**: Auth token issues
- **Permission Errors**: Access denied
- **Resource Errors**: Not found or unavailable
- **System Errors**: Internal server issues
- **Rate Limit Errors**: Too many requests

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Default Limit**: 100 requests per minute per user
- **Burst Limit**: 200 requests per minute
- **Memory Operations**: 50 operations per minute
- **Search Operations**: 30 searches per minute

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 85
X-RateLimit-Reset: 1609459200
```

## Data Models

### Memory Model
```typescript
interface Memory {
  id: string;
  text: string;
  metadata: Record<string, any>;
  categories: string[];
  created_at: string;
  updated_at: string;
  user_id: string;
  app_id: string;
  app_name: string;
  state: 'active' | 'paused' | 'archived' | 'deleted';
  access_count: number;
  last_accessed_at: string;
}
```

### User Model
```typescript
interface User {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  supabase_user_id?: string;
  email_verified: boolean;
  last_sign_in_at?: string;
}
```

### App Model
```typescript
interface App {
  id: string;
  name: string;
  owner_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  memory_count: number;
  access_count: number;
  last_accessed: string;
}
```

## Configuration Options

### Memory Engine Configuration
```json
{
  "mem0": {
    "version": "v1.1",
    "llm": {
      "provider": "openai",
      "config": {
        "model": "gpt-4o-mini",
        "temperature": 0.1,
        "max_tokens": 2000
      }
    },
    "embedder": {
      "provider": "openai",
      "config": {
        "model": "text-embedding-3-small"
      }
    }
  }
}
```

### Evolution Intelligence Configuration
```json
{
  "evolution": {
    "enabled": true,
    "auto_optimization": true,
    "confidence_threshold": 0.8,
    "domain_type": "technical",
    "custom_prompts": {
      "fact_extraction": "Extract key facts...",
      "update_memory": "Determine memory operations..."
    }
  }
}
```

## Best Practices

### API Usage
1. **Authentication**: Always include proper authentication headers
2. **Rate Limiting**: Implement client-side rate limiting
3. **Error Handling**: Handle all error responses gracefully
4. **Pagination**: Use pagination for large datasets
5. **Caching**: Implement appropriate caching strategies

### Memory Management
1. **Chunking**: Let the API handle large text chunking automatically
2. **Metadata**: Use metadata for rich context information
3. **Categories**: Leverage automatic category inference
4. **Search**: Use semantic search for better results

### Performance Optimization
1. **Batch Operations**: Use bulk operations when possible
2. **Filtering**: Apply filters to reduce data transfer
3. **Pagination**: Don't fetch all data at once
4. **Caching**: Cache frequently accessed data

## Development and Testing

### Local Development
```bash
# Start the API server
cd api
uvicorn main:app --reload --host 0.0.0.0 --port 8765

# Access documentation
open http://localhost:8765/docs
```

### Testing
```bash
# Run API tests
pytest tests/

# Test specific endpoint
pytest tests/test_memories.py -v

# Test with coverage
pytest --cov=app tests/
```

### API Client Examples

#### Python Client
```python
import requests

# Create memory
response = requests.post(
    "http://localhost:8765/api/v1/memories/",
    json={
        "text": "Important business information",
        "metadata": {"source": "meeting"}
    },
    headers={"Authorization": "Bearer your-token"}
)

# Search memories
response = requests.get(
    "http://localhost:8765/api/v1/memories/",
    params={"search_query": "business"},
    headers={"Authorization": "Bearer your-token"}
)
```

#### JavaScript Client
```javascript
// Create memory
const response = await fetch('http://localhost:8765/api/v1/memories/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    text: 'Important business information',
    metadata: { source: 'meeting' }
  })
});

// Search memories
const searchResponse = await fetch(
  'http://localhost:8765/api/v1/memories/?search_query=business',
  {
    headers: {
      'Authorization': 'Bearer your-token'
    }
  }
);
```

---

This API documentation provides comprehensive coverage of all Memory Master endpoints, authentication, error handling, and best practices. For additional details, refer to the interactive documentation at `/docs` when running the API server.