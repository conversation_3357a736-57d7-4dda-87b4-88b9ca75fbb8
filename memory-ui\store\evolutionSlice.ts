import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface EvolutionOperation {
  id: string;
  user_id: string;
  memory_id: string;
  operation: 'ADD' | 'UPDATE' | 'DELETE' | 'NOOP';
  confidence_score: number;
  metadata: Record<string, any>;
  created_at: string;
}

interface EvolutionState {
  operations: EvolutionOperation[];
  loading: boolean;
  error: string | null;
  connectionState: 'connecting' | 'connected' | 'disconnected';
  realtimeEnabled: boolean;
  analytics: {
    totalOperations: number;
    avgConfidence: number;
    operationsByType: Record<string, number>;
    lowConfidenceCount: number;
  };
}

const initialState: EvolutionState = {
  operations: [],
  loading: false,
  error: null,
  connectionState: 'disconnected',
  realtimeEnabled: false,
  analytics: {
    totalOperations: 0,
    avgConfidence: 0,
    operationsByType: {},
    lowConfidenceCount: 0
  }
};

const evolutionSlice = createSlice({
  name: 'evolution',
  initialState,
  reducers: {
    setOperations: (state, action: PayloadAction<EvolutionOperation[]>) => {
      state.operations = action.payload;
      state.loading = false;
      state.error = null;
      // Update analytics
      state.analytics.totalOperations = action.payload.length;
      if (action.payload.length > 0) {
        state.analytics.avgConfidence = action.payload.reduce((sum, op) => sum + op.confidence_score, 0) / action.payload.length;
        state.analytics.lowConfidenceCount = action.payload.filter(op => op.confidence_score < 0.5).length;
        state.analytics.operationsByType = action.payload.reduce((acc, op) => {
          acc[op.operation] = (acc[op.operation] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
      }
    },
    addOperation: (state, action: PayloadAction<EvolutionOperation>) => {
      state.operations.unshift(action.payload);
      // Keep only latest 50 operations
      if (state.operations.length > 50) {
        state.operations = state.operations.slice(0, 50);
      }
      // Update analytics
      state.analytics.totalOperations += 1;
      if (action.payload.confidence_score < 0.5) {
        state.analytics.lowConfidenceCount += 1;
      }
      state.analytics.operationsByType[action.payload.operation] = (state.analytics.operationsByType[action.payload.operation] || 0) + 1;
    },
    updateOperation: (state, action: PayloadAction<EvolutionOperation>) => {
      const index = state.operations.findIndex(op => op.id === action.payload.id);
      if (index !== -1) {
        state.operations[index] = action.payload;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.loading = false;
    },
    setConnectionState: (state, action: PayloadAction<'connecting' | 'connected' | 'disconnected'>) => {
      state.connectionState = action.payload;
    },
    setRealtimeEnabled: (state, action: PayloadAction<boolean>) => {
      state.realtimeEnabled = action.payload;
    },
    clearOperations: (state) => {
      state.operations = [];
      state.analytics = {
        totalOperations: 0,
        avgConfidence: 0,
        operationsByType: {},
        lowConfidenceCount: 0
      };
    }
  }
});

export const {
  setOperations,
  addOperation,
  updateOperation,
  setLoading,
  setError,
  setConnectionState,
  setRealtimeEnabled,
  clearOperations
} = evolutionSlice.actions;

export default evolutionSlice.reducer;