@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 48 100% 50%;  /* Firebase Yellow #FFC400 */
    --primary-foreground: 240 10% 3.9%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 48 100% 50%;  /* Firebase Yellow for focus rings */
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary/20;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  @apply bg-secondary/50 hover:bg-secondary/70;
  border-radius: 4px;
}

/* Glass morphism effects */
.glass {
  @apply bg-card/50 backdrop-blur-md border border-border/50;
}

.glass-hover {
  @apply hover:bg-card/60 transition-all duration-200;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-firebase-yellow to-google-green bg-clip-text text-transparent;
}

/* Glow effects */
.glow {
  box-shadow: 0 0 20px rgba(255, 196, 0, 0.3);
}

.glow-primary {
  box-shadow: 0 0 20px rgba(255, 196, 0, 0.5);
}

/* Animation utilities */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 196, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 196, 0, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Loading dots */
@keyframes dots {
  0%, 80%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

.loading-dots span {
  animation: dots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}