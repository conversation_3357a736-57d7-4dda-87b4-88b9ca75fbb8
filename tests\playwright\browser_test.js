// Browser interaction test using Chrome DevTools Protocol
const { spawn } = require('child_process');
const fs = require('fs');

// Function to run Chrome with debugging enabled
function runChromeWithDebug() {
    return new Promise((resolve, reject) => {
        console.log('Launching Chrome with debugging...');
        
        const chrome = spawn('google-chrome', [
            '--remote-debugging-port=9222',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-dev-shm-usage',
            '--no-sandbox',
            `http://localhost:3001/memories`
        ], { detached: true });

        // Give Chrome time to start
        setTimeout(() => {
            console.log('Chrome should be running now');
            resolve(chrome);
        }, 3000);
    });
}

// Function to take screenshot using CDP
function takeScreenshot(filename) {
    return new Promise((resolve, reject) => {
        const chrome = spawn('google-chrome', [
            '--headless',
            '--disable-gpu',
            '--no-sandbox',
            '--screenshot=' + filename,
            '--window-size=1920,1080',
            '--virtual-time-budget=5000',
            'http://localhost:3001/memories'
        ]);

        chrome.on('close', (code) => {
            if (code === 0) {
                console.log('Screenshot saved:', filename);
                resolve();
            } else {
                reject(new Error('Chrome screenshot failed'));
            }
        });
    });
}

// Function to interact with page using CDP
async function interactWithPage() {
    const CDP = require('chrome-remote-interface');
    
    try {
        const client = await CDP();
        const { Page, Runtime } = client;
        
        await Page.enable();
        await Runtime.enable();
        
        console.log('Navigating to memories page...');
        await Page.navigate({ url: 'http://localhost:3001/memories' });
        
        // Wait for page to load
        await Page.loadEventFired();
        
        // Take initial screenshot
        const screenshot1 = await Page.captureScreenshot({ format: 'png' });
        fs.writeFileSync('/tmp/page_initial.png', screenshot1.data, 'base64');
        console.log('Initial screenshot saved');
        
        // Try to click the user switcher button
        console.log('Attempting to click user switcher button...');
        const clickResult = await Runtime.evaluate({
            expression: `
                // Find the button containing "Aung Hein Aye"
                const buttons = Array.from(document.querySelectorAll('button'));
                const aungButton = buttons.find(b => b.textContent.includes('Aung Hein Aye'));
                if (aungButton) {
                    aungButton.click();
                    'Button clicked successfully';
                } else {
                    'Button not found';
                }
            `
        });
        
        console.log('Click result:', clickResult.result.value);
        
        // Wait a bit for the page to update
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Take another screenshot
        const screenshot2 = await Page.captureScreenshot({ format: 'png' });
        fs.writeFileSync('/tmp/page_after_click.png', screenshot2.data, 'base64');
        console.log('After-click screenshot saved');
        
        // Check the current user state
        const stateCheck = await Runtime.evaluate({
            expression: `
                // Check Redux store state if available
                if (window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) {
                    'Redux DevTools available';
                } else {
                    'Redux DevTools not available';
                }
            `
        });
        
        console.log('State check:', stateCheck.result.value);
        
        await client.close();
        
    } catch (error) {
        console.error('CDP interaction failed:', error);
    }
}

// Main test execution
async function main() {
    try {
        console.log('Starting browser test...');
        
        // Take initial screenshot
        await takeScreenshot('/tmp/memories_test_initial.png');
        
        // Try to interact with the page
        // Note: CDP requires Chrome to be running with debugging enabled
        // For simplicity, we'll just take screenshots and examine the page
        
        console.log('Test completed. Check the screenshots in /tmp/');
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

main();