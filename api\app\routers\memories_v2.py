from datetime import datetime, UTC
from typing import List, Optional
from uuid import UUID, uuid4
import logging
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from sqlalchemy import or_, func

from app.database import get_db
from app.models import Memory, MemoryState, App, User
from app.dependencies import get_current_user, get_current_user_id
from app.config import DEFAULT_APP_ID

router = APIRouter(prefix="/api/v1", tags=["memories-v2"])

class MemoryResponse(BaseModel):
    id: str
    title: str
    content: str
    user_id: str
    metadata: dict
    created_at: str
    updated_at: str

class CreateMemoryRequest(BaseModel):
    title: str
    content: str
    metadata: dict = {}

class UpdateMemoryRequest(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    metadata: Optional[dict] = None

class MemoriesListResponse(BaseModel):
    memories: List[MemoryResponse]
    totalCount: int

@router.get("/memories")
async def list_memories(
    user_id: str = Depends(get_current_user_id),
    user: User = Depends(get_current_user),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db)
) -> MemoriesListResponse:
    """List memories for the authenticated user with filtering and pagination."""
    
    # Build base query
    query = db.query(Memory).filter(
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value,
        Memory.state != MemoryState.archived.value
    )
    
    # Apply search filter
    if search:
        search_filter = or_(
            Memory.content.ilike(f"%{search}%"),
            Memory.metadata_.op('->>')('title').ilike(f"%{search}%") if hasattr(Memory.metadata_, 'op') else Memory.content.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Apply category filter (stored in metadata)
    if category and category != 'all':
        query = query.filter(
            Memory.metadata_.op('->>')('category') == category
        )
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination
    offset = (page - 1) * limit
    memories = query.order_by(Memory.created_at.desc()).offset(offset).limit(limit).all()
    
    # Convert to response format
    memory_responses = []
    for memory in memories:
        memory_responses.append(MemoryResponse(
            id=str(memory.id),
            title=memory.metadata_.get('title', '') if memory.metadata_ else '',
            content=memory.content,
            user_id=str(memory.user_id),
            metadata=memory.metadata_ or {},
            created_at=memory.created_at.isoformat(),
            updated_at=memory.updated_at.isoformat() if memory.updated_at else memory.created_at.isoformat()
        ))
    
    return MemoriesListResponse(
        memories=memory_responses,
        totalCount=total_count
    )

@router.post("/memories")
async def create_memory(
    request: CreateMemoryRequest,
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> MemoryResponse:
    """Create a new memory for the authenticated user."""
    
    # Get or create default app for user
    app = db.query(App).filter(
        App.name == DEFAULT_APP_ID,
        App.owner_id == user.id
    ).first()
    
    if not app:
        app = App(
            id=uuid4(),
            name=DEFAULT_APP_ID,
            owner_id=user.id,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        db.add(app)
        db.commit()
        db.refresh(app)
    
    # Create memory
    memory_id = uuid4()
    metadata = request.metadata.copy()
    metadata['title'] = request.title  # Store title in metadata
    
    memory = Memory(
        id=memory_id,
        user_id=user.id,
        app_id=app.id,
        content=request.content,
        metadata_=metadata,
        state=MemoryState.active.value,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC)
    )
    
    db.add(memory)
    db.commit()
    db.refresh(memory)
    
    return MemoryResponse(
        id=str(memory.id),
        title=request.title,
        content=memory.content,
        user_id=str(memory.user_id),
        metadata=memory.metadata_ or {},
        created_at=memory.created_at.isoformat(),
        updated_at=memory.updated_at.isoformat() if memory.updated_at else memory.created_at.isoformat()
    )

@router.put("/memories/{memory_id}")
async def update_memory(
    memory_id: UUID,
    request: UpdateMemoryRequest,
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> MemoryResponse:
    """Update an existing memory."""
    
    # Get memory and verify ownership
    memory = db.query(Memory).filter(
        Memory.id == memory_id,
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value
    ).first()
    
    if not memory:
        raise HTTPException(status_code=404, detail="Memory not found")
    
    # Update fields
    if request.title is not None:
        if not memory.metadata_:
            memory.metadata_ = {}
        memory.metadata_['title'] = request.title
    
    if request.content is not None:
        memory.content = request.content
    
    if request.metadata is not None:
        if not memory.metadata_:
            memory.metadata_ = {}
        memory.metadata_.update(request.metadata)
    
    memory.updated_at = datetime.now(UTC)
    
    db.commit()
    db.refresh(memory)
    
    return MemoryResponse(
        id=str(memory.id),
        title=memory.metadata_.get('title', '') if memory.metadata_ else '',
        content=memory.content,
        user_id=str(memory.user_id),
        metadata=memory.metadata_ or {},
        created_at=memory.created_at.isoformat(),
        updated_at=memory.updated_at.isoformat()
    )

@router.delete("/memories/{memory_id}")
async def delete_memory(
    memory_id: UUID,
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a memory (soft delete)."""
    
    # Get memory and verify ownership
    memory = db.query(Memory).filter(
        Memory.id == memory_id,
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value
    ).first()
    
    if not memory:
        raise HTTPException(status_code=404, detail="Memory not found")
    
    # Soft delete
    memory.state = MemoryState.deleted.value
    memory.updated_at = datetime.now(UTC)
    
    db.commit()
    
    return {"message": "Memory deleted successfully"}