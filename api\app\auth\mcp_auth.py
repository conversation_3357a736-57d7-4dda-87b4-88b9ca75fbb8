"""
MCP Authentication Module

Provides hybrid authentication for MCP server endpoints supporting:
1. HTTP Headers (Authorization Bearer)
2. Environment Variables (SUPABASE_ANON_KEY + MCP_USER_ID) 
3. Query Parameters (api_key + user_id)
4. Localhost bypass for development
"""

import os
import logging
from typing import Tuple, Optional
from fastapi import Request, HTTPException


class MCPAuthenticationError(Exception):
    """Custom exception for MCP authentication failures"""
    def __init__(self, message: str, supported_methods: list = None):
        self.message = message
        self.supported_methods = supported_methods or [
            "Authorization header", 
            "Environment variables", 
            "Query parameters"
        ]
        super().__init__(self.message)


def validate_supabase_key(provided_key: str) -> bool:
    """
    Validate the provided key against the expected Supabase anon key
    
    Args:
        provided_key: The key to validate
        
    Returns:
        bool: True if key is valid, False otherwise
    """
    if not provided_key:
        return False
        
    expected_key = os.getenv("EXPECTED_SUPABASE_ANON_KEY")
    if not expected_key:
        logging.warning("EXPECTED_SUPABASE_ANON_KEY not configured - authentication will fail")
        return False
    
    return provided_key.strip() == expected_key.strip()


def is_localhost(request: Request) -> bool:
    """
    Check if request is from localhost
    
    Args:
        request: FastAPI request object
        
    Returns:
        bool: True if request is from localhost
    """
    if not hasattr(request, 'client') or not request.client:
        return False
        
    host = request.client.host
    localhost_addresses = ["127.0.0.1", "localhost", "::1", "0.0.0.0"]
    return host in localhost_addresses


def extract_user_from_header(auth_header: str) -> Optional[str]:
    """
    Extract user ID from Authorization header
    For now, we'll use environment MCP_USER_ID since the anon key doesn't contain user info
    
    Args:
        auth_header: Authorization header value
        
    Returns:
        Optional[str]: User ID if found, None otherwise
    """
    if not auth_header.startswith("Bearer "):
        return None
        
    # For Supabase anon key, we can't extract user from token
    # So we still need MCP_USER_ID from environment
    return os.getenv("MCP_USER_ID")


async def authenticate_mcp_request(request: Request, user_id_from_url: Optional[str] = None) -> Tuple[str, bool]:
    """
    Authenticate MCP request using hybrid approach
    
    Authentication priority:
    1. HTTP Headers (Authorization Bearer)
    2. Environment Variables (SUPABASE_ANON_KEY + MCP_USER_ID)
    3. Query Parameters (api_key + user_id)
    4. Localhost bypass (development only)
    
    Args:
        request: FastAPI request object
        user_id_from_url: User ID extracted from URL path (legacy)
        
    Returns:
        Tuple[str, bool]: (user_id, is_authenticated)
        
    Raises:
        MCPAuthenticationError: If no valid authentication found
    """
    
    # 1. Check Authorization Header (future MCP clients)
    auth_header = request.headers.get("Authorization", "")
    if auth_header.startswith("Bearer "):
        anon_key = auth_header.split("Bearer ")[1].strip()
        if validate_supabase_key(anon_key):
            user_id = extract_user_from_header(auth_header)
            if user_id:
                logging.info(f"MCP authenticated via Authorization header for user: {user_id}")
                return user_id, True
            else:
                logging.warning("Valid anon key in header but no MCP_USER_ID found in environment")
    
    # 2. Check Environment Variables (current MCP standard)
    env_key = os.getenv("SUPABASE_ANON_KEY")
    env_user = os.getenv("MCP_USER_ID")
    if env_key and env_user:
        if validate_supabase_key(env_key):
            logging.info(f"MCP authenticated via environment variables for user: {env_user}")
            return env_user, True
        else:
            logging.warning("Invalid SUPABASE_ANON_KEY in environment variables")
    
    # 3. Check Query Parameters (simple fallback)
    api_key = request.query_params.get("api_key")
    user_id = request.query_params.get("user_id", user_id_from_url)
    if api_key and user_id:
        if validate_supabase_key(api_key):
            logging.info(f"MCP authenticated via query parameters for user: {user_id}")
            return user_id, True
        else:
            logging.warning("Invalid api_key in query parameters")
    
    # 4. Development Mode (localhost only)
    if is_localhost(request) and user_id_from_url:
        logging.warning(f"MCP localhost bypass for development - user: {user_id_from_url}")
        return user_id_from_url, False  # Not authenticated but allowed for development
    
    # No valid authentication found
    logging.error("MCP authentication failed - no valid credentials provided")
    raise MCPAuthenticationError(
        "Authentication required. Please provide valid Supabase anon key.",
        ["Authorization: Bearer <anon_key>", "Environment: SUPABASE_ANON_KEY + MCP_USER_ID", "Query: ?api_key=<anon_key>&user_id=<user>"]
    )


def get_mcp_auth_error_response(error: MCPAuthenticationError) -> dict:
    """
    Generate standardized error response for MCP authentication failures
    
    Args:
        error: MCPAuthenticationError instance
        
    Returns:
        dict: Error response data
    """
    return {
        "error": "MCP Authentication Failed",
        "code": "MCP_AUTH_REQUIRED",
        "message": error.message,
        "supported_methods": error.supported_methods,
        "documentation": "See MCP configuration guide for authentication setup"
    }


def validate_user_permissions(user_id: str) -> bool:
    """
    Validate that the user is allowed to access the MCP server
    
    Args:
        user_id: User ID to validate
        
    Returns:
        bool: True if user is allowed
    """
    allowed_users = ["aungheinaye", "yohanna"]
    return user_id in allowed_users