services:
  memory-api:
    build: 
      context: api/
      dockerfile: Dockerfile
    container_name: memory-api-dev
    restart: unless-stopped
    env_file:
      - api/.env
    environment:
      # Override for development
      - QDRANT_HOST=${QDRANT_HOST:-*************}
      - SUPABASE_HOST=${SUPABASE_HOST:-*************}
      - DATABASE_URL=${DATABASE_URL:-**************************************************************************/postgres}
    ports:
      - "8765:8765"
    volumes:
      # Development bind mounts for hot reload
      - ./api:/usr/src/app
      - api_node_modules:/usr/src/app/node_modules
    working_dir: /usr/src/app
    command: >
      sh -c "uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 1"
    networks:
      - memory-network
    # No dependencies needed - using remote Qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  memory-ui:
    build:
      context: memory-ui/
      dockerfile: Dockerfile
    container_name: memory-ui-dev
    restart: unless-stopped
    env_file:
      - memory-ui/.env.local
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8765}
    ports:
      - "3210:3210"
    volumes:
      # Development bind mounts for hot reload
      - ./memory-ui:/usr/src/app
      - ui_node_modules:/usr/src/app/node_modules
      - ui_next_cache:/usr/src/app/.next
    working_dir: /usr/src/app
    depends_on:
      - memory-api
    networks:
      - memory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3210"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Qdrant service removed - using remote instance at *************:6333

volumes:
  api_node_modules:
    driver: local
  ui_node_modules:
    driver: local
  ui_next_cache:
    driver: local

networks:
  memory-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16