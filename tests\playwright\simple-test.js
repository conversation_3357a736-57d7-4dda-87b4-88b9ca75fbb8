const { chromium } = require('playwright');

async function quickTest() {
  const browser = await chromium.launch({ headless: false, slowMo: 2000 });
  const page = await browser.newPage();
  
  try {
    await page.goto('http://localhost:3001');
    await page.click('text=Memories');
    await page.waitForTimeout(2000);
    
    // Take screenshot of memories page
    await page.screenshot({ path: 'quick-test-memories.png' });
    
    // Count how many memories are currently shown
    const memoryCards = await page.locator('.memory-card, [data-testid="memory"], .cursor-pointer').count();
    console.log(`Current memory cards visible: ${memoryCards}`);
    
    // Look specifically for our test memory
    const hasTestMemory = await page.locator('text="Post-Fix Test Memory"').count() > 0;
    console.log(`Our test memory is visible: ${hasTestMemory}`);
    
    // Check total count
    const totalElement = await page.locator('text=/Total Memories/').locator('.. div').filter({ hasText: /^\d+$/ }).first();
    const totalCount = await totalElement.textContent();
    console.log(`Total memories count: ${totalCount}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await browser.close();
  }
}

quickTest();