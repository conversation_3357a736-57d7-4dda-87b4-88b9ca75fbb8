{"dependencies": {"@playwright/test": "^1.53.2", "playwright": "^1.53.2", "task-master-ai": "^0.18.0"}, "name": "memory-master-v2", "description": "**Memory Master v2** is a comprehensive personal memory layer for Large Language Models (LLMs) that provides private, portable, and open-source memory management. It enables AI assistants to remember context across conversations while giving you complete control over your data.", "version": "1.0.0", "main": "browser_test.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://gitea.syncrobit.net/aungheinaye/memory-master-v2.git"}, "keywords": [], "author": "", "license": "ISC"}