name: E2E Tests - Memory Master v2

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: memory_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: memory-ui/package-lock.json

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install frontend dependencies
      working-directory: memory-ui
      run: |
        npm ci
        npx playwright install --with-deps

    - name: Install backend dependencies
      working-directory: api
      run: |
        pip install -r requirements.txt

    - name: Setup test environment
      run: |
        # Create test environment files
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" > api/.env
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/memory_master_test" >> api/.env
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> api/.env
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> api/.env
        echo "QDRANT_URL=http://localhost:6333" >> api/.env
        echo "ENVIRONMENT=test" >> api/.env
        
        echo "NEXT_PUBLIC_API_URL=http://localhost:8765" > memory-ui/.env.local
        echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> memory-ui/.env.local
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> memory-ui/.env.local

    - name: Run database migrations
      working-directory: api
      run: |
        alembic upgrade head

    - name: Start backend server
      working-directory: api
      run: |
        uvicorn main:app --host 0.0.0.0 --port 8765 --workers 1 &
        sleep 10
        curl -f http://localhost:8765/health || exit 1

    - name: Start frontend server
      working-directory: memory-ui
      run: |
        npm run build
        npm run start &
        sleep 15
        curl -f http://localhost:3210 || exit 1

    - name: Run comprehensive E2E tests
      working-directory: memory-ui
      run: |
        npm run test:e2e:chrome
      env:
        CI: true
        CHROME_EXECUTABLE_PATH: /usr/bin/google-chrome-stable

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: memory-ui/test-results/
        retention-days: 30

    - name: Upload screenshots
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: screenshots
        path: memory-ui/test-results/screenshots/
        retention-days: 30

    - name: Comment PR with test results
      uses: actions/github-script@v7
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          
          try {
            const results = JSON.parse(fs.readFileSync('memory-ui/test-results/results.json', 'utf8'));
            const comment = `## 🧪 E2E Test Results
            
            **Total Tests:** ${results.stats.tests}
            **Passed:** ${results.stats.passed}
            **Failed:** ${results.stats.failed}
            **Skipped:** ${results.stats.skipped}
            **Duration:** ${Math.round(results.stats.duration / 1000)}s
            
            ${results.stats.failed > 0 ? '❌ Some tests failed. Check the test results for details.' : '✅ All tests passed!'}
            
            [View detailed results](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.log('Could not read test results:', error);
          }

  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: memory_master_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install dependencies
      working-directory: api
      run: |
        pip install -r requirements.txt

    - name: Setup test environment
      run: |
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" > api/.env
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/memory_master_test" >> api/.env
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> api/.env
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> api/.env
        echo "QDRANT_URL=http://localhost:6333" >> api/.env
        echo "ENVIRONMENT=test" >> api/.env

    - name: Run database migrations
      working-directory: api
      run: |
        alembic upgrade head

    - name: Run backend tests
      working-directory: api
      run: |
        pytest --cov=. --cov-report=xml --cov-report=html -v

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: api/coverage.xml
        flags: backend
        name: codecov-umbrella

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: backend-test-results
        path: api/htmlcov/
        retention-days: 30

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: memory-ui/package-lock.json

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        cd memory-ui && npm ci && npx playwright install --with-deps
        cd ../api && pip install -r requirements.txt

    - name: Setup test environment
      run: |
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" > api/.env
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> api/.env
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> api/.env
        echo "QDRANT_URL=http://localhost:6333" >> api/.env
        echo "ENVIRONMENT=test" >> api/.env
        
        echo "NEXT_PUBLIC_API_URL=http://localhost:8765" > memory-ui/.env.local
        echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> memory-ui/.env.local
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> memory-ui/.env.local

    - name: Start services
      run: |
        cd api && uvicorn main:app --host 0.0.0.0 --port 8765 --workers 1 &
        cd memory-ui && npm run build && npm run start &
        sleep 20

    - name: Run performance tests
      working-directory: memory-ui
      run: |
        npx playwright test --grep "Performance|performance" --reporter=html

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-results
        path: memory-ui/test-results/
        retention-days: 30