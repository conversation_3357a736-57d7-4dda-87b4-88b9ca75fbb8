#!/bin/bash

# Memory Master v2 - Comprehensive Testing Script
# This script runs the complete test suite for the Memory Master system

echo "🚀 Memory Master v2 - Comprehensive Testing Suite"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
print_status "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi
print_success "Docker is running"

# Start services
print_status "Starting Memory Master services..."
docker-compose up -d

# Wait for services to be ready
print_status "Waiting for services to start up..."
sleep 30

# Check backend health
print_status "Checking backend health..."
if curl -f http://localhost:8765/health > /dev/null 2>&1; then
    print_success "Backend service is healthy"
else
    print_warning "Backend service may not be fully ready"
fi

# Check frontend
print_status "Checking frontend..."
if curl -f http://localhost:3210 > /dev/null 2>&1; then
    print_success "Frontend service is running"
else
    print_warning "Frontend service may not be fully ready"
fi

# Change to frontend directory
cd memory-ui

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Install Playwright browsers if needed
print_status "Ensuring Playwright browsers are installed..."
npx playwright install

# Run different test suites based on argument
case "${1:-all}" in
    "quick")
        print_status "Running quick verification tests..."
        npm run test:e2e -- tests/working-e2e-final.spec.ts
        ;;
    "comprehensive")
        print_status "Running comprehensive test suite..."
        npm run test:e2e -- tests/comprehensive-e2e.spec.ts
        ;;
    "evolution")
        print_status "Running evolution intelligence tests..."
        npm run test:e2e -- tests/evolution-intelligence.spec.ts
        ;;
    "chrome")
        print_status "Running Chrome-specific tests..."
        npm run test:e2e:chrome
        ;;
    "mobile")
        print_status "Running mobile responsive tests..."
        npm run test:e2e:mobile
        ;;
    "all")
        print_status "Running all test suites..."
        echo ""
        echo "🧪 Running Backend Tests..."
        cd ../api
        pytest -v --tb=short
        
        echo ""
        echo "🌐 Running Frontend E2E Tests..."
        cd ../memory-ui
        npm run test:e2e -- tests/working-e2e-final.spec.ts
        
        echo ""
        echo "📊 Running Performance Tests..."
        npm run test:e2e -- --grep "performance|Performance"
        ;;
    "report")
        print_status "Opening test report..."
        npm run test:e2e:report
        ;;
    "debug")
        print_status "Running tests in debug mode..."
        npm run test:e2e:debug
        ;;
    *)
        echo "Usage: $0 [quick|comprehensive|evolution|chrome|mobile|all|report|debug]"
        echo ""
        echo "Test Suites:"
        echo "  quick         - Run quick verification tests (recommended for CI)"
        echo "  comprehensive - Run full comprehensive test suite"
        echo "  evolution     - Run evolution intelligence system tests"
        echo "  chrome        - Run Chrome desktop tests only"
        echo "  mobile        - Run mobile responsive tests"
        echo "  all           - Run backend + frontend + performance tests"
        echo "  report        - Open the test report viewer"
        echo "  debug         - Run tests in debug mode"
        echo ""
        echo "Examples:"
        echo "  ./run-tests.sh quick"
        echo "  ./run-tests.sh comprehensive"
        echo "  ./run-tests.sh all"
        exit 1
        ;;
esac

# Show results location
echo ""
print_success "Test execution completed!"
print_status "Test results are available in:"
echo "  📁 HTML Report: memory-ui/playwright-report/index.html"
echo "  📁 Screenshots: memory-ui/test-results/screenshots/"
echo "  📁 Videos: memory-ui/test-results/"
echo "  📁 Detailed Report: memory-ui/test-results/comprehensive-test-report.md"

# Open report if not in CI
if [[ "${CI}" != "true" ]]; then
    echo ""
    print_status "To view the HTML report, run: npm run test:e2e:report"
fi

echo ""
print_success "🎉 Memory Master v2 testing completed successfully!"