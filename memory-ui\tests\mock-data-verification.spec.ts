import { test, expect } from '@playwright/test';

test.describe('Mock Data Verification Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('Dashboard should not contain any mock data', async ({ page }) => {
    // Take a screenshot of the dashboard
    await page.screenshot({ path: 'test-results/dashboard-verification.png', fullPage: true });
    
    // Check for mock data that should NOT be present
    const mockTexts = [
      'Customer asking about return policy',
      'TechGear Solutions',
      'OrderFlow Systems',
      'bulk discount policy',
      'shipping times',
      '3m ago',
      '5m ago',
      '1,848 memories' // This specific number should not be hardcoded
    ];
    
    for (const mockText of mockTexts) {
      await expect(page.getByText(mockText)).toHaveCount(0);
    }
    
    console.log('✅ Dashboard passed mock data verification');
  });

  test('Memory processing pipeline should show real user info', async ({ page }) => {
    // Take screenshot of memory processing pipeline
    await page.screenshot({ path: 'test-results/memory-pipeline-verification.png', fullPage: true });
    
    // Check for proper user information display
    const processingSection = page.locator('[data-testid="memory-processing-pipeline"]');
    
    if (await processingSection.isVisible()) {
      // Should show real user info, not mock data
      await expect(processingSection.getByText('Customer asking about return policy')).toHaveCount(0);
      await expect(processingSection.getByText('TechGear Solutions')).toHaveCount(0);
      
      // Should show system-ready states instead of mock operations
      const hasRealUserInfo = await processingSection.getByText('aungheinaye').isVisible();
      if (hasRealUserInfo) {
        console.log('✅ Memory processing pipeline shows real user info');
      } else {
        console.log('ℹ️  Memory processing pipeline in system-ready state');
      }
    }
  });

  test('User switching functionality verification', async ({ page }) => {
    // Look for user selector
    const userSelector = page.locator('[data-testid="user-selector"]');
    
    if (await userSelector.isVisible()) {
      // Test switching to Aung Hein Aye
      await userSelector.click();
      await page.getByText('Aung Hein Aye').click();
      await page.waitForTimeout(1000);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/user-aung-verification.png', fullPage: true });
      
      // Check for real data (should show 191+ memories based on context)
      const memoryCount = await page.locator('[data-testid="memory-count"]').textContent();
      console.log('Aung memory count:', memoryCount);
      
      // Switch to Yohanna
      await userSelector.click();
      await page.getByText('Yohanna').click();
      await page.waitForTimeout(1000);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/user-yohanna-verification.png', fullPage: true });
      
      // Should show 0 memories for Yohanna
      const yohannaMemoryCount = await page.locator('[data-testid="memory-count"]').textContent();
      console.log('Yohanna memory count:', yohannaMemoryCount);
      
      // Switch to Guest User
      await userSelector.click();
      await page.getByText('Guest User').click();
      await page.waitForTimeout(1000);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/user-guest-verification.png', fullPage: true });
      
      console.log('✅ User switching functionality verified');
    } else {
      console.log('ℹ️  User selector not found - may be using different UI pattern');
    }
  });

  test('Memory list page verification', async ({ page }) => {
    // Navigate to memories page
    await page.goto('/memories');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/memories-page-verification.png', fullPage: true });
    
    // Check for mock data that should NOT be present
    const mockMemoryTexts = [
      'bulk discount policy',
      'shipping times',
      'return policy details',
      'TechGear Solutions customer inquiry'
    ];
    
    for (const mockText of mockMemoryTexts) {
      await expect(page.getByText(mockText)).toHaveCount(0);
    }
    
    console.log('✅ Memory list page passed mock data verification');
  });

  test('System health indicators verification', async ({ page }) => {
    // Take screenshot of system health section
    await page.screenshot({ path: 'test-results/system-health-verification.png', fullPage: true });
    
    // Check for system health indicators
    const healthSection = page.locator('[data-testid="system-health"]');
    
    if (await healthSection.isVisible()) {
      // Should show actual authentication state, not mock data
      await expect(healthSection.getByText('Mock User Session')).toHaveCount(0);
      await expect(healthSection.getByText('Demo Mode')).toHaveCount(0);
      
      console.log('✅ System health indicators show real authentication state');
    } else {
      console.log('ℹ️  System health section not found');
    }
  });

  test('Overall dashboard state verification', async ({ page }) => {
    // Take comprehensive screenshot
    await page.screenshot({ path: 'test-results/complete-dashboard-verification.png', fullPage: true });
    
    // Check page title
    await expect(page).toHaveTitle(/Memory Master/);
    
    // Verify no mock company names anywhere on the page
    const mockCompanies = [
      'TechGear Solutions',
      'OrderFlow Systems',
      'Demo Company',
      'Test Corp',
      'Example Inc'
    ];
    
    for (const company of mockCompanies) {
      await expect(page.getByText(company)).toHaveCount(0);
    }
    
    // Verify no mock timestamps
    const mockTimestamps = [
      '3m ago',
      '5m ago',
      '10m ago',
      '1h ago',
      '2h ago'
    ];
    
    for (const timestamp of mockTimestamps) {
      await expect(page.getByText(timestamp)).toHaveCount(0);
    }
    
    console.log('✅ Overall dashboard state verification passed');
  });
});