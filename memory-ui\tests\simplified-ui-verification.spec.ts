import { test, expect } from '@playwright/test';

test.describe('Memory Master v2 - Simplified UI Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3001');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should verify Memory Processing Pipeline animation is completely removed', async ({ page }) => {
    // Check that the animation section is not present
    const animationSection = page.locator('[data-testid="memory-processing-pipeline"]');
    await expect(animationSection).toHaveCount(0);
    
    // Also check for common animation-related selectors
    const workflowAnimation = page.locator('.workflow-animation');
    await expect(workflowAnimation).toHaveCount(0);
    
    const memoryFlowDiagram = page.locator('[data-testid="memory-flow-diagram"]');
    await expect(memoryFlowDiagram).toHaveCount(0);
    
    // Check for any animated elements that might be pipeline-related
    const animatedElements = page.locator('.animate-pulse, .animate-spin, .animate-bounce').filter({
      has: page.locator(':text-matches("pipeline|processing|flow", "i")')
    });
    await expect(animatedElements).toHaveCount(0);
    
    console.log('✓ Memory Processing Pipeline animation section successfully removed');
  });

  test('should verify sidebar navigation shows only 4 items', async ({ page }) => {
    // Wait for sidebar to be visible
    const sidebar = page.locator('[data-testid="sidebar"], nav, .sidebar');
    await expect(sidebar).toBeVisible();
    
    // Get all navigation links
    const navItems = page.locator('nav a, .sidebar a, [role="navigation"] a').filter({
      hasText: /.+/
    });
    
    // Check that we have exactly 4 navigation items
    await expect(navItems).toHaveCount(4);
    
    // Verify the specific navigation items
    await expect(page.locator('text=Dashboard')).toBeVisible();
    await expect(page.locator('text=Sync Monitor')).toBeVisible();
    await expect(page.locator('text=Maintenance')).toBeVisible();
    await expect(page.locator('text=Settings')).toBeVisible();
    
    // Verify that "Memories" link is NOT present
    const memoriesLink = page.locator('nav a:has-text("Memories"), .sidebar a:has-text("Memories")');
    await expect(memoriesLink).toHaveCount(0);
    
    console.log('✓ Sidebar navigation correctly shows only 4 items (Dashboard, Sync Monitor, Maintenance, Settings)');
  });

  test('should verify memory functionality is still available on dashboard', async ({ page }) => {
    // Ensure we're on the dashboard
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Check for Create Memory button
    const createMemoryButton = page.locator('button:has-text("Create Memory"), [data-testid="create-memory-button"]');
    await expect(createMemoryButton).toBeVisible();
    
    // Check for memory list or memory section
    const memorySection = page.locator('[data-testid="memory-list"], .memory-list, .memories-section');
    const memoryCards = page.locator('.memory-card, [data-testid="memory-card"]');
    const memoryTable = page.locator('table:has(th:text-matches("memory|title|content", "i"))');
    
    // At least one of these should be present
    const hasMemorySection = await memorySection.count() > 0;
    const hasMemoryCards = await memoryCards.count() > 0;
    const hasMemoryTable = await memoryTable.count() > 0;
    
    expect(hasMemorySection || hasMemoryCards || hasMemoryTable).toBeTruthy();
    
    console.log('✓ Memory functionality (Create Memory button and memory display) available on dashboard');
  });

  test('should test Create Memory button functionality', async ({ page }) => {
    // Click the Create Memory button
    const createMemoryButton = page.locator('button:has-text("Create Memory"), [data-testid="create-memory-button"]');
    await createMemoryButton.click();
    
    // Wait for dialog or form to appear
    await page.waitForTimeout(1000);
    
    // Check for dialog or modal
    const dialog = page.locator('[role="dialog"], .dialog, [data-testid="create-memory-dialog"]');
    const modal = page.locator('.modal, .modal-content');
    const form = page.locator('form:has(input[placeholder*="memory" i], textarea[placeholder*="memory" i])');
    
    const hasDialog = await dialog.count() > 0;
    const hasModal = await modal.count() > 0;
    const hasForm = await form.count() > 0;
    
    expect(hasDialog || hasModal || hasForm).toBeTruthy();
    
    console.log('✓ Create Memory button opens dialog/form successfully');
  });

  test('should test navigation to all remaining pages', async ({ page }) => {
    const pages = [
      { name: 'Dashboard', url: '/', selector: 'h1:has-text("Dashboard"), h2:has-text("Dashboard")' },
      { name: 'Sync Monitor', url: '/sync', selector: 'h1:has-text("Sync"), h2:has-text("Sync")' },
      { name: 'Maintenance', url: '/maintenance', selector: 'h1:has-text("Maintenance"), h2:has-text("Maintenance")' },
      { name: 'Settings', url: '/settings', selector: 'h1:has-text("Settings"), h2:has-text("Settings")' }
    ];
    
    for (const pageInfo of pages) {
      console.log(`Testing navigation to ${pageInfo.name}...`);
      
      // Navigate to the page
      await page.goto(`http://localhost:3001${pageInfo.url}`);
      await page.waitForLoadState('networkidle');
      
      // Wait a moment for content to load
      await page.waitForTimeout(1000);
      
      // Check that the page loaded successfully
      const pageContent = page.locator(pageInfo.selector).first();
      const hasContent = await pageContent.count() > 0;
      
      if (!hasContent) {
        // Fallback: check for any heading or main content
        const fallbackContent = page.locator('h1, h2, h3, main, .main-content').first();
        await expect(fallbackContent).toBeVisible();
      } else {
        await expect(pageContent).toBeVisible();
      }
      
      // Verify no 404 or error messages
      const errorMessages = page.locator(':has-text("404"), :has-text("Not Found"), :has-text("Error")');
      await expect(errorMessages).toHaveCount(0);
      
      console.log(`✓ ${pageInfo.name} page loads successfully`);
    }
  });

  test('should verify no broken links or missing components', async ({ page }) => {
    // Check for common error indicators
    const errorElements = page.locator('[class*="error"], .error-message, .not-found');
    await expect(errorElements).toHaveCount(0);
    
    // Check for 404 text
    const notFoundText = page.locator(':has-text("404"), :has-text("Not Found"), :has-text("Page not found")');
    await expect(notFoundText).toHaveCount(0);
    
    // Check for broken images (if any)
    const brokenImages = page.locator('img[src=""], img:not([src])');
    await expect(brokenImages).toHaveCount(0);
    
    // Check for JavaScript errors in console
    const jsErrors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });
    
    // Navigate through all pages to check for errors
    const urls = ['/', '/sync', '/maintenance', '/settings'];
    for (const url of urls) {
      await page.goto(`http://localhost:3001${url}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(500);
    }
    
    // Report any JavaScript errors found
    if (jsErrors.length > 0) {
      console.warn('JavaScript errors found:', jsErrors);
    }
    
    console.log('✓ No broken links or missing components detected');
  });

  test('should take screenshots of the cleaned up interface', async ({ page }) => {
    // Dashboard screenshot
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    await page.screenshot({ 
      path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/simplified-01-dashboard.png',
      fullPage: true 
    });
    
    // Sync Monitor screenshot
    await page.goto('http://localhost:3001/sync');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    await page.screenshot({ 
      path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/simplified-02-sync-monitor.png',
      fullPage: true 
    });
    
    // Maintenance screenshot
    await page.goto('http://localhost:3001/maintenance');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    await page.screenshot({ 
      path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/simplified-03-maintenance.png',
      fullPage: true 
    });
    
    // Settings screenshot
    await page.goto('http://localhost:3001/settings');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    await page.screenshot({ 
      path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/simplified-04-settings.png',
      fullPage: true 
    });
    
    // Test Create Memory dialog
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    const createButton = page.locator('button:has-text("Create Memory"), [data-testid="create-memory-button"]');
    if (await createButton.count() > 0) {
      await createButton.click();
      await page.waitForTimeout(1000);
      await page.screenshot({ 
        path: '/home/<USER>/wsl_dev/memory-master-v2/memory-ui/screenshots/simplified-05-create-memory-dialog.png',
        fullPage: true 
      });
    }
    
    console.log('✓ Screenshots captured successfully');
  });
});