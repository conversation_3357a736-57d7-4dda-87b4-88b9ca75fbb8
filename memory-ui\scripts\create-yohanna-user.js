// <PERSON>ript to create Yohanna's Supabase auth user
const { createClient } = require('@supabase/supabase-js')

const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogInNlcnZpY2Vfcm9sZSIsCiAgImlzcyI6ICJzdXBhYmFzZSIsCiAgImlhdCI6IDE3NDEwOTUwMDAsCiAgImV4cCI6IDE4OTg4NjE0MDAKfQ.4OLZbhrbccD5vfy14HcWgYDgJsSzUXimxgcGA3OgaV8'

const supabase = createClient(
  'http://*************:8000',
  SUPABASE_SERVICE_KEY
)

async function createYohanna() {
  console.log('Creating Yohanna user...')
  
  try {
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true,
      user_metadata: {
        display_name: 'Yohanna'
      }
    })
    
    if (error) {
      console.error('Error:', error)
      return
    }
    
    console.log('Created auth user:', data.user.id)
    
    // Update memory_master.users
    const { error: updateError } = await supabase
      .from('memory_master.users')
      .update({
        supabase_user_id: data.user.id,
        email_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', 'yohanna')
    
    if (updateError) {
      console.error('Update error:', updateError)
    } else {
      console.log('Updated memory_master.users')
    }
    
    console.log('Yohanna can now login with:')
    console.log('Email: <EMAIL>')
    console.log('Password: password123')
    
  } catch (err) {
    console.error('Error:', err.message)
  }
}

createYohanna()