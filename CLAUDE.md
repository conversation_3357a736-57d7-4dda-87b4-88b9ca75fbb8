# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Business Context

### Team and Project Overview
- Project is a dropshipping business operating 3 stores on Amazon to eBay platform
- Team consists of two members:
  - Aung: Software developer responsible for system architecture restructuring and optimization
  - Yohanna: Customer support and operations specialist
- Memory system will be used for day-to-day customer support and eBay store operations
- Access methods:
  - Aung: <PERSON>, <PERSON>, and Trae IDE
  - Yohanna: <PERSON> and upcoming custom Chrome extension

## Development Configuration

### Local Development Ports
- memory-ui project is running locally at port 3210
- api project is running at port 8765
- Qdrant vector store (when enabled) runs on port 6333

## Development Commands

### Docker-based Development (Primary)
```bash
# Start all services (recommended for development)
docker-compose up -d

# View service logs
docker-compose logs memory-mcp  # API server logs
docker-compose logs memory-ui   # UI logs

# Restart services after code changes
docker-compose restart memory-mcp
docker-compose restart memory-ui

# Stop all services
docker-compose down
```

### Local Development
```bash
# Backend API (requires Python 3.12+)
cd api
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 4

# Frontend UI (requires Node.js 18+, uses npm)
cd memory-ui
npm install
npm run dev     # Development server
npm run build   # Production build
npm run lint    # ESLint checking
```

### Testing
```bash
# Backend tests (from api/ directory)
pytest                    # Run all tests
pytest -v                # Verbose output
pytest tests/test_*.py   # Run specific test files
pytest --cov            # Coverage report

# Frontend tests
cd memory-ui && npm test       # If test scripts are added
```

### Database Management
```bash
# Database migrations (from api/ directory)
alembic upgrade head     # Apply latest migrations
alembic revision --autogenerate -m "description"  # Create new migration
alembic downgrade -1     # Rollback one migration

# Health checks
curl http://0.0.0.0:8765/health                    # API health
curl http://0.0.0.0:8765/mcp/claude/sse/health     # MCP server health
curl http://*************:6333/health              # Qdrant health
```

## Architecture Overview

### System Architecture
Memory Master v2 follows a **microservices architecture** with main components:
- **API Server** (FastAPI): RESTful API + MCP server functionality
- **Web UI** (Next.js): React-based dashboard for memory management
- **Database** (Supabase PostgreSQL): Relational data storage at *************:3000
- **Vector Store** (Qdrant): High-performance vector database for embeddings at *************:6333

### Backend Architecture (FastAPI)
- **Layered Architecture**: Clean separation between routers, services, and data layers
- **Router-Based API**: Domain-organized endpoints (`/api/v1/memories`, `/api/v1/apps`, etc.)
- **Service Layer Pattern**: Business logic in dedicated service classes
  - `memory_service.py`: Core memory operations, chunking, validation
  - `evolution_service.py`: Evolution intelligence system
  - `health_service.py`: System monitoring
- **MCP Integration**: `mcp_server.py` provides Model Context Protocol support via FastMCP
- **Database**: SQLAlchemy ORM with Alembic migrations, connection pooling (20 pool size)

### Frontend Architecture (Next.js)
- **App Router Structure**: Modern Next.js 13+ with app directory
- **State Management**: Redux Toolkit with domain-specific slices:
  - `memoriesSlice`: Memory state and operations
  - `authSlice`: Authentication state
  - `configSlice`: System configuration
  - `uiSlice`: UI state management
- **Component Architecture**: Shadcn/UI components with Radix UI primitives
- **Data Fetching**: Custom hooks for API integration (`useMemoriesApi`, `useEvolutionAnalytics`)

### Evolution Intelligence System
A sophisticated system that tracks and analyzes memory operations:
- **Operation Tracking**: Monitors ADD/UPDATE/DELETE/NOOP operations from Mem0
- **Analytics Storage**: Detailed metadata, confidence scores, reasoning
- **Configuration Management**: Domain-specific prompts with version control
- **Real-time Dashboard**: Live operation tracking and performance metrics

## Key Components and File Locations

### Backend Core Files
- `api/main.py`: FastAPI application entry point
- `api/app/models.py`: SQLAlchemy database models
- `api/app/database/base.py`: Database connection and session management
- `api/app/memory_service.py`: Core memory operations and business logic
- `api/app/mcp_server.py`: MCP protocol implementation
- `api/app/routers/`: API endpoints organized by domain
- `api/app/services/evolution_service.py`: Evolution intelligence system
- `api/app/utils/evolution_prompts.py`: Evolution system prompts and configuration

### Frontend Core Files
- `memory-ui/app/layout.tsx`: Root layout with providers
- `memory-ui/store/store.ts`: Redux store configuration
- `memory-ui/hooks/`: Custom hooks for API integration
- `memory-ui/app/memories/`: Memory management interface
- `memory-ui/app/evolution/`: Evolution intelligence dashboard
- `memory-ui/app/settings/evolution/`: Evolution system configuration
- `memory-ui/components/ui/`: Shadcn/UI component library

### Configuration Files
- `docker-compose.yml`: Multi-container orchestration
- `api/.env`: Backend environment variables (requires OpenAI API key)
- `memory-ui/.env.local`: Frontend environment variables
- `api/alembic.ini`: Database migration configuration

## Data Flow Patterns

### Memory Operations Flow
```
User Input → Frontend Component → Custom Hook → API Request → Router → Service Layer → Mem0 Client → Qdrant Vector DB → PostgreSQL
```

### Evolution Intelligence Flow
```
Memory Operation → Mem0 Response → Evolution Service → Analytics Processing → Database Storage → Real-time Updates → Dashboard
```

### MCP Integration Flow
```
Claude Desktop → MCP Protocol → mcp_server.py → Service Layer → Database/Vector Store
```

## Development Guidelines

### Environment Setup
- **Environment Files**: Only 2 .env files required:
  - `api/.env`: Backend configuration (database, API keys, server settings)
  - `memory-ui/.env.local`: Frontend configuration (API URLs, Supabase settings, feature flags)
- Ensure `api/.env` contains valid `OPENAI_API_KEY` and database credentials
- Database runs on external Supabase instance at *************:3000
- Vector store (Qdrant) runs at *************:6333
- API server runs on port 8765, UI on port 3210

### Code Organization
- Backend: Follow service layer pattern, keep routers thin
- Frontend: Use custom hooks for API calls, maintain Redux state
- Database: Use Alembic for migrations, follow SQLAlchemy best practices
- Testing: Write tests in `api/tests/` directory

### Memory System Specifics
- Text chunking is automatic for content >2000 words
- Evolution intelligence tracks all memory operations with confidence scores
- MCP tools provide memory operations for Claude Desktop integration
- System supports concurrent operations with connection pooling

### Performance Considerations
- API server runs with 4 workers for concurrent request handling
- Response times optimized for <200ms with caching
- Connection pool sized for 20 connections with 30 overflow
- External services (Qdrant and Supabase) managed separately