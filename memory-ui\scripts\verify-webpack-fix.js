#!/usr/bin/env node
/**
 * Webpack Cache Fix Verification Script
 * Verifies that the webpack cache performance warning has been resolved
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

let buildOutput = '';
let hasWarning = false;
let buildSuccess = false;

function checkCacheDirectory() {
  const cacheDir = path.join(process.cwd(), '.next', 'cache', 'webpack');
  
  if (fs.existsSync(cacheDir)) {
    const stats = fs.statSync(cacheDir);
    console.log(`📁 Cache directory exists: ${cacheDir}`);
    console.log(`📅 Created: ${stats.birthtime.toISOString()}`);
    
    // Check cache size
    try {
      const { execSync } = require('child_process');
      let size;
      
      if (process.platform === 'win32') {
        // Windows command to get directory size
        size = 'Size calculation not available on Windows';
      } else {
        const output = execSync(`du -sh "${cacheDir}"`, { encoding: 'utf8' });
        size = output.trim().split('\t')[0];
      }
      
      console.log(`📊 Cache size: ${size}`);
    } catch (error) {
      console.log('📊 Cache size: Unable to calculate');
    }
  } else {
    console.log('📁 Cache directory not found (will be created on build)');
  }
}

function runBuild() {
  return new Promise((resolve, reject) => {
    console.log('🔨 Running build to test webpack cache...');
    console.log('=====================================');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });
    
    buildProcess.stdout.on('data', (data) => {
      const output = data.toString();
      buildOutput += output;
      process.stdout.write(output);
      
      // Check for the specific webpack cache warning
      if (output.includes('Serializing big strings') && 
          output.includes('impacts deserialization performance')) {
        hasWarning = true;
      }
    });
    
    buildProcess.stderr.on('data', (data) => {
      const output = data.toString();
      buildOutput += output;
      process.stderr.write(output);
      
      // Check for the warning in stderr as well
      if (output.includes('Serializing big strings') && 
          output.includes('impacts deserialization performance')) {
        hasWarning = true;
      }
    });
    
    buildProcess.on('close', (code) => {
      buildSuccess = code === 0;
      resolve(code);
    });
    
    buildProcess.on('error', (error) => {
      reject(error);
    });
  });
}

function generateReport() {
  console.log('\\n\\n🔍 WEBPACK CACHE FIX VERIFICATION REPORT');
  console.log('==========================================');
  
  // Check for webpack cache warning
  if (hasWarning) {
    console.log('❌ WEBPACK CACHE WARNING STILL PRESENT');
    console.log('   The "Serializing big strings" warning was detected.');
    console.log('   Additional optimization may be needed.');
  } else {
    console.log('✅ WEBPACK CACHE WARNING RESOLVED');
    console.log('   No "Serializing big strings" warning detected.');
  }
  
  // Check build success
  if (buildSuccess) {
    console.log('✅ BUILD COMPLETED SUCCESSFULLY');
  } else {
    console.log('❌ BUILD FAILED');
    console.log('   Check the build output above for errors.');
  }
  
  // Check cache directory
  console.log('\\n📁 CACHE DIRECTORY STATUS:');
  checkCacheDirectory();
  
  // Performance recommendations
  console.log('\\n💡 PERFORMANCE RECOMMENDATIONS:');
  
  if (!hasWarning && buildSuccess) {
    console.log('   ✅ Webpack cache optimization successful!');
    console.log('   ✅ Build performance should be improved');
    console.log('   ✅ UI loading should be faster');
  } else if (hasWarning) {
    console.log('   🔧 Additional steps needed:');
    console.log('   1. Clear cache: npm run clear-cache');
    console.log('   2. Check for large imports or static files');
    console.log('   3. Consider code splitting for large components');
    console.log('   4. Review webpack-fix-guide.md for detailed steps');
  }
  
  if (buildSuccess) {
    console.log('\\n🚀 NEXT STEPS:');
    console.log('   1. Test the application: npm run dev');
    console.log('   2. Monitor for cache warnings during development');
    console.log('   3. Run bundle analysis: npm run build:analyze');
    console.log('   4. Check UI loading speed improvement');
  }
  
  // Save report
  const report = {
    timestamp: new Date().toISOString(),
    hasWebpackWarning: hasWarning,
    buildSuccess: buildSuccess,
    cacheDirectory: fs.existsSync('.next/cache/webpack'),
    recommendations: hasWarning ? 'Additional optimization needed' : 'Cache optimization successful'
  };
  
  fs.writeFileSync('webpack-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\\n📄 Detailed report saved to: webpack-fix-report.json');
}

async function main() {
  try {
    console.log('🚀 Webpack Cache Fix Verification');
    console.log('==================================');
    console.log('Testing for: "Serializing big strings (108kiB) impacts deserialization performance"');
    console.log('');
    
    // Run the build and capture output
    await runBuild();
    
    // Generate verification report
    generateReport();
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}