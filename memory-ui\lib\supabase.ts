// Legacy export for backward compatibility
export { createClient as create<PERSON><PERSON><PERSON>rowser } from './supabase/client'
export { createClient as createClientServer } from './supabase/server'

// For components that still use the old import
import { createClient } from './supabase/client'
export const supabase = createClient()

export type Database = {
  public: {
    Tables: {
      memories: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          content: string;
          metadata: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
      };
      evolution_operations: {
        Row: {
          id: string;
          user_id: string;
          memory_id: string;
          operation: string;
          confidence_score: number;
          metadata: Record<string, any>;
          created_at: string;
        };
      };
    };
  };
};