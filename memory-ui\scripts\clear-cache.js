#!/usr/bin/env node
/**
 * Cache clearing script for Memory Master UI
 * Clears webpack cache, Next.js cache, and node_modules cache
 * Run this if you encounter persistent cache-related issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const cacheDirectories = [
  '.next',
  '.next/cache',
  'node_modules/.cache',
  '.swc',
  '.eslintcache',
];

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    console.log(`🗑️  Clearing ${folderPath}...`);
    try {
      if (process.platform === 'win32') {
        execSync(`rmdir /s /q "${folderPath}"`, { stdio: 'inherit' });
      } else {
        execSync(`rm -rf "${folderPath}"`, { stdio: 'inherit' });
      }
      console.log(`✅ Cleared ${folderPath}`);
    } catch (error) {
      console.error(`❌ Failed to clear ${folderPath}:`, error.message);
    }
  } else {
    console.log(`⏭️  ${folderPath} doesn't exist, skipping...`);
  }
}

function clearPackageManagerCache() {
  console.log('🧹 Clearing package manager caches...');
  
  try {
    console.log('Clearing npm cache...');
    execSync('npm cache clean --force', { stdio: 'inherit' });
  } catch (error) {
    console.log('npm cache clean failed or npm not available');
  }

  try {
    console.log('Clearing yarn cache...');
    execSync('yarn cache clean', { stdio: 'inherit' });
  } catch (error) {
    console.log('yarn cache clean failed or yarn not available');
  }
}

function main() {
  console.log('🚀 Memory Master UI Cache Cleaner');
  console.log('=====================================');
  
  // Clear local cache directories
  cacheDirectories.forEach(dir => {
    const fullPath = path.resolve(process.cwd(), dir);
    deleteFolderRecursive(fullPath);
  });
  
  // Clear package manager caches
  clearPackageManagerCache();
  
  console.log('');
  console.log('✨ Cache clearing complete!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Run "npm install" to reinstall dependencies');
  console.log('2. Run "npm run dev" to start development server');
  console.log('3. The webpack cache warning should be resolved');
  console.log('');
}

if (require.main === module) {
  main();
}

module.exports = { deleteFolderRecursive, clearPackageManagerCache };