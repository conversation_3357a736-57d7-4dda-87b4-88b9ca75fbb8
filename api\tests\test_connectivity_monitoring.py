import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import collections

from app.utils.memory import MemoryClientSingleton


class TestConnectivityMonitoring(unittest.TestCase):
    """Test suite for vector store connectivity monitoring functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Reset singleton instance for each test
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._client = None
        MemoryClientSingleton._monitoring_thread = None
        MemoryClientSingleton._monitoring_running = False
        MemoryClientSingleton._connectivity_history = collections.deque(maxlen=100)
        MemoryClientSingleton._connectivity_alerts_enabled = True
        MemoryClientSingleton._last_connectivity_alert = None
        MemoryClientSingleton._degraded_mode = False
        
    def tearDown(self):
        """Clean up after tests."""
        # Stop monitoring if running
        if hasattr(MemoryClientSingleton, '_instance') and MemoryClientSingleton._instance:
            instance = MemoryClientSingleton._instance
            instance._stop_monitoring()
            # Wait a bit for thread to stop
            time.sleep(0.1)
        
        # Reset singleton
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._client = None
        MemoryClientSingleton._monitoring_thread = None
        MemoryClientSingleton._monitoring_running = False
        MemoryClientSingleton._connectivity_history = collections.deque(maxlen=100)
    
    def test_start_monitoring(self):
        """Test starting the monitoring thread."""
        singleton = MemoryClientSingleton()
        
        # Mock the monitoring method to avoid actual monitoring
        def mock_monitor():
            while singleton._monitoring_running:
                time.sleep(0.1)
        
        with patch.object(singleton, '_monitor_connectivity', side_effect=mock_monitor):
            singleton._start_monitoring()
            
            # Give thread a moment to start
            time.sleep(0.05)
            
            # Check that monitoring thread was started
            self.assertTrue(singleton._monitoring_running)
            self.assertIsNotNone(singleton._monitoring_thread)
            self.assertTrue(singleton._monitoring_thread.is_alive())
            
            # Stop monitoring
            singleton._stop_monitoring()
            time.sleep(0.1)  # Give thread time to stop
    
    def test_stop_monitoring(self):
        """Test stopping the monitoring thread."""
        singleton = MemoryClientSingleton()
        
        # Start monitoring first
        with patch.object(singleton, '_monitor_connectivity'):
            singleton._start_monitoring()
            self.assertTrue(singleton._monitoring_running)
            
            # Stop monitoring
            singleton._stop_monitoring()
            self.assertFalse(singleton._monitoring_running)
    
    @patch('app.utils.memory.time.sleep')  # Mock sleep to speed up test
    def test_monitor_connectivity_loop(self, mock_sleep):
        """Test the monitoring loop functionality."""
        singleton = MemoryClientSingleton()
        
        # Mock connectivity check to return different values
        connectivity_results = [True, True, False, True]
        with patch.object(singleton, '_check_vector_store_connectivity', side_effect=connectivity_results):
            with patch.object(singleton, '_enter_degraded_mode') as mock_enter_degraded:
                with patch.object(singleton, '_exit_degraded_mode') as mock_exit_degraded:
                    # Start monitoring
                    singleton._monitoring_running = True
                    
                    # Run a few iterations of the monitoring loop
                    for i in range(4):
                        # Simulate one iteration of the monitoring loop
                        is_connected = connectivity_results[i]
                        timestamp = time.time()
                        singleton._connectivity_history.append((timestamp, is_connected))
                        
                        # Handle mode transitions
                        if not is_connected and not singleton._degraded_mode:
                            singleton._enter_degraded_mode("Vector store connectivity lost")
                        elif is_connected and singleton._degraded_mode:
                            singleton._exit_degraded_mode()
                    
                    # Check that connectivity history was recorded
                    self.assertEqual(len(singleton._connectivity_history), 4)
                    
                    # Check that degraded mode was entered when connectivity failed
                    mock_enter_degraded.assert_called_once_with("Vector store connectivity lost")
    
    def test_check_vector_store_connectivity_success(self):
        """Test successful vector store connectivity check."""
        singleton = MemoryClientSingleton()
        
        # Mock client with successful search
        mock_client = Mock()
        mock_client.search.return_value = {"results": []}
        singleton._client = mock_client
        
        result = singleton._check_vector_store_connectivity()
        
        self.assertTrue(result)
        mock_client.search.assert_called_once_with(query="connectivity_test", limit=1)
    
    def test_check_vector_store_connectivity_failure(self):
        """Test failed vector store connectivity check."""
        singleton = MemoryClientSingleton()
        
        # Mock client with failing search
        mock_client = Mock()
        mock_client.search.side_effect = Exception("Connection failed")
        singleton._client = mock_client
        
        result = singleton._check_vector_store_connectivity()
        
        self.assertFalse(result)
    
    def test_check_vector_store_connectivity_no_client(self):
        """Test connectivity check when no client exists."""
        singleton = MemoryClientSingleton()
        singleton._client = None
        
        result = singleton._check_vector_store_connectivity()
        
        self.assertFalse(result)
    
    def test_send_connectivity_alert(self):
        """Test sending connectivity alerts."""
        singleton = MemoryClientSingleton()
        
        with patch('app.utils.memory.logging') as mock_logging:
            with patch('builtins.print') as mock_print:
                # Test sending alert
                singleton._send_connectivity_alert("disconnected", "Test alert")
                
                # Check that alert was logged
                mock_logging.warning.assert_called_once()
                mock_print.assert_called_once()
                
                # Check that last alert time was set
                self.assertIsNotNone(singleton._last_connectivity_alert)
    
    def test_send_connectivity_alert_cooldown(self):
        """Test alert cooldown functionality."""
        singleton = MemoryClientSingleton()
        
        with patch('app.utils.memory.logging') as mock_logging:
            with patch('builtins.print') as mock_print:
                # Send first alert
                singleton._send_connectivity_alert("disconnected", "First alert")
                first_call_count = mock_logging.warning.call_count
                
                # Try to send second alert immediately (should be blocked by cooldown)
                singleton._send_connectivity_alert("disconnected", "Second alert")
                
                # Should still be only one call due to cooldown
                self.assertEqual(mock_logging.warning.call_count, first_call_count)
    
    def test_send_connectivity_alert_disabled(self):
        """Test that alerts are not sent when disabled."""
        singleton = MemoryClientSingleton()
        singleton._connectivity_alerts_enabled = False
        
        with patch('app.utils.memory.logging') as mock_logging:
            singleton._send_connectivity_alert("disconnected", "Test alert")
            
            # No alert should be sent
            mock_logging.warning.assert_not_called()
    
    def test_get_connectivity_status(self):
        """Test getting connectivity status."""
        singleton = MemoryClientSingleton()
        
        # Add some connectivity history
        current_time = time.time()
        singleton._connectivity_history.append((current_time - 300, True))
        singleton._connectivity_history.append((current_time - 200, False))
        singleton._connectivity_history.append((current_time - 100, True))
        
        status = singleton.get_connectivity_status()
        
        # Check status structure
        self.assertIn('status', status)
        self.assertIn('degraded_mode', status)
        self.assertIn('uptime_percentage', status)
        self.assertIn('last_check_time', status)
        self.assertIn('monitoring_running', status)
        
        # Check calculated values
        self.assertEqual(status['uptime_percentage'], 2/3)  # 2 out of 3 checks were successful
        self.assertEqual(status['last_check_status'], True)
    
    def test_set_monitoring_interval(self):
        """Test setting monitoring interval."""
        singleton = MemoryClientSingleton()
        
        # Test valid interval
        singleton.set_monitoring_interval(60)
        self.assertEqual(singleton._monitoring_interval, 60)
        
        # Test invalid interval (too short)
        with self.assertRaises(ValueError):
            singleton.set_monitoring_interval(10)
    
    def test_enable_connectivity_alerts(self):
        """Test enabling/disabling connectivity alerts."""
        singleton = MemoryClientSingleton()
        
        # Test enabling alerts
        singleton.enable_connectivity_alerts(True)
        self.assertTrue(singleton._connectivity_alerts_enabled)
        
        # Test disabling alerts
        singleton.enable_connectivity_alerts(False)
        self.assertFalse(singleton._connectivity_alerts_enabled)
    
    def test_get_connectivity_metrics_empty_history(self):
        """Test getting metrics when no history exists."""
        singleton = MemoryClientSingleton()
        
        metrics = singleton.get_connectivity_metrics()
        
        # Check that all metrics are properly initialized
        self.assertEqual(metrics['total_checks'], 0)
        self.assertEqual(metrics['successful_checks'], 0)
        self.assertEqual(metrics['failed_checks'], 0)
        self.assertIsNone(metrics['uptime_percentage'])
        self.assertIsNone(metrics['average_check_interval'])
    
    def test_get_connectivity_metrics_with_history(self):
        """Test getting metrics with connectivity history."""
        singleton = MemoryClientSingleton()
        
        # Add connectivity history with known pattern
        base_time = time.time()
        history_data = [
            (base_time, True),
            (base_time + 60, True),
            (base_time + 120, False),
            (base_time + 180, True),
            (base_time + 240, True)
        ]
        
        for timestamp, connected in history_data:
            singleton._connectivity_history.append((timestamp, connected))
        
        metrics = singleton.get_connectivity_metrics()
        
        # Check calculated metrics
        self.assertEqual(metrics['total_checks'], 5)
        self.assertEqual(metrics['successful_checks'], 4)
        self.assertEqual(metrics['failed_checks'], 1)
        self.assertEqual(metrics['uptime_percentage'], 0.8)  # 4/5
        self.assertEqual(metrics['average_check_interval'], 60.0)  # 60 seconds between checks
    
    def test_connectivity_history_max_size(self):
        """Test that connectivity history respects maximum size."""
        singleton = MemoryClientSingleton()
        
        # Add more than max size (100) entries
        for i in range(150):
            singleton._connectivity_history.append((time.time() + i, True))
        
        # Should be limited to max size
        self.assertEqual(len(singleton._connectivity_history), 100)
    
    def test_monitoring_integration_with_degraded_mode(self):
        """Test that monitoring properly integrates with degraded mode."""
        singleton = MemoryClientSingleton()
        
        # Mock connectivity check to fail
        with patch.object(singleton, '_check_vector_store_connectivity', return_value=False):
            with patch.object(singleton, '_send_connectivity_alert') as mock_alert:
                # Simulate monitoring detecting failure
                is_connected = False
                if not is_connected and not singleton._degraded_mode:
                    singleton._enter_degraded_mode("Vector store connectivity lost")
                
                # Check that degraded mode was entered
                self.assertTrue(singleton._degraded_mode)
                self.assertEqual(singleton._degradation_reason, "Vector store connectivity lost")
                
                # Check that alert was sent
                mock_alert.assert_called_once_with("disconnected", "Vector store connectivity lost")
    
    def test_monitoring_recovery_from_degraded_mode(self):
        """Test that monitoring can recover from degraded mode."""
        singleton = MemoryClientSingleton()
        
        # Start in degraded mode
        singleton._degraded_mode = True
        singleton._degradation_reason = "Test failure"
        
        # Mock connectivity check to succeed
        with patch.object(singleton, '_check_vector_store_connectivity', return_value=True):
            with patch.object(singleton, '_send_connectivity_alert') as mock_alert:
                with patch.object(singleton, '_process_backlog') as mock_backlog:
                    # Simulate monitoring detecting recovery
                    is_connected = True
                    if is_connected and singleton._degraded_mode:
                        singleton._exit_degraded_mode()
                    
                    # Check that degraded mode was exited
                    self.assertFalse(singleton._degraded_mode)
                    self.assertIsNone(singleton._degradation_reason)
                    
                    # Check that recovery alert was sent
                    mock_alert.assert_called_once_with("connected", "Vector store connectivity restored")
                    
                    # Check that backlog was processed
                    mock_backlog.assert_called_once()
    
    def test_uptime_alert_threshold(self):
        """Test that low uptime triggers alerts."""
        singleton = MemoryClientSingleton()
        
        # Add history with low uptime (less than 80%)
        base_time = time.time()
        # 15 checks: 10 failed, 5 successful = 33% uptime
        for i in range(15):
            connected = i < 5  # First 5 are successful, rest fail
            singleton._connectivity_history.append((base_time + i * 60, connected))
        
        with patch.object(singleton, '_send_connectivity_alert') as mock_alert:
            # Simulate monitoring check that calculates uptime
            uptime = sum(1 for _, connected in singleton._connectivity_history if connected) / len(singleton._connectivity_history)
            
            if uptime < 0.8:
                singleton._send_connectivity_alert("low_uptime", f"Vector store uptime is {uptime:.2%}")
            
            # Check that low uptime alert was sent
            mock_alert.assert_called_once_with("low_uptime", "Vector store uptime is 33.33%")


class TestMonitoringIntegration(unittest.TestCase):
    """Integration tests for monitoring with other components."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Reset singleton instance for each test
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._client = None
        MemoryClientSingleton._monitoring_thread = None
        MemoryClientSingleton._monitoring_running = False
        MemoryClientSingleton._connectivity_history = collections.deque(maxlen=100)
    
    def tearDown(self):
        """Clean up after tests."""
        # Stop monitoring if running
        if hasattr(MemoryClientSingleton, '_instance') and MemoryClientSingleton._instance:
            instance = MemoryClientSingleton._instance
            instance._stop_monitoring()
            time.sleep(0.1)
        
        # Reset singleton
        MemoryClientSingleton._instance = None
        MemoryClientSingleton._client = None
        MemoryClientSingleton._monitoring_thread = None
        MemoryClientSingleton._monitoring_running = False
        MemoryClientSingleton._connectivity_history = collections.deque(maxlen=100)
    
    @patch('app.utils.memory.Memory')
    def test_monitoring_starts_with_client_initialization(self, mock_memory_class):
        """Test that monitoring starts when client is successfully initialized."""
        # Mock successful client initialization
        mock_client = Mock()
        mock_memory_class.from_config.return_value = mock_client
        
        singleton = MemoryClientSingleton()
        
        with patch.object(singleton, '_start_monitoring') as mock_start_monitoring:
            # Initialize client
            config_dict = {'vector_store': {'provider': 'qdrant'}}
            client = singleton.get_client(config_dict)
            
            # Check that client was initialized and monitoring started
            self.assertIsNotNone(client)
            mock_start_monitoring.assert_called_once()
    
    def test_monitoring_detects_simulated_connectivity_issues(self):
        """Test monitoring with simulated connectivity issues."""
        singleton = MemoryClientSingleton()
        
        # Mock client
        mock_client = Mock()
        singleton._client = mock_client
        
        # Simulate connectivity issues
        connectivity_sequence = [True, True, False, False, True, True]
        
        with patch.object(singleton, '_send_connectivity_alert') as mock_alert:
            for i, is_connected in enumerate(connectivity_sequence):
                # Mock connectivity check result
                mock_client.search.side_effect = None if is_connected else Exception("Connection failed")
                
                # Perform connectivity check
                result = singleton._check_vector_store_connectivity()
                self.assertEqual(result, is_connected)
                
                # Record in history
                singleton._connectivity_history.append((time.time() + i * 60, is_connected))
                
                # Handle mode transitions
                if not is_connected and not singleton._degraded_mode:
                    singleton._enter_degraded_mode("Simulated connectivity loss")
                elif is_connected and singleton._degraded_mode:
                    singleton._exit_degraded_mode()
            
            # Check that alerts were sent for disconnection and reconnection
            self.assertTrue(mock_alert.called)
            
            # Check final connectivity history
            self.assertEqual(len(singleton._connectivity_history), 6)
    
    def test_automatic_mode_switching(self):
        """Test automatic switching between normal and degraded mode."""
        singleton = MemoryClientSingleton()
        
        # Mock client
        mock_client = Mock()
        singleton._client = mock_client
        
        # Test normal -> degraded transition
        mock_client.search.side_effect = Exception("Vector store unavailable")
        
        with patch.object(singleton, '_process_backlog') as mock_backlog:
            # Simulate connectivity failure
            is_connected = singleton._check_vector_store_connectivity()
            self.assertFalse(is_connected)
            
            if not is_connected and not singleton._degraded_mode:
                singleton._enter_degraded_mode("Vector store unavailable")
            
            self.assertTrue(singleton._degraded_mode)
            
            # Test degraded -> normal transition
            mock_client.search.side_effect = None  # Remove exception
            mock_client.search.return_value = {"results": []}
            
            # Simulate connectivity recovery
            is_connected = singleton._check_vector_store_connectivity()
            self.assertTrue(is_connected)
            
            if is_connected and singleton._degraded_mode:
                singleton._exit_degraded_mode()
            
            self.assertFalse(singleton._degraded_mode)
            mock_backlog.assert_called_once()


if __name__ == '__main__':
    unittest.main()