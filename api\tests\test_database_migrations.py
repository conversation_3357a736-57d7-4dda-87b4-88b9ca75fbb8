"""Test suite for database migration validation and rollback procedures.

This module tests database migrations, including upgrade/downgrade functionality,
data integrity, rollback procedures, and migration consistency.
"""

import pytest
import uuid
import tempfile
import os
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.environment import EnvironmentContext
from alembic.runtime.migration import MigrationContext

from app.database import Base
from app.models import User, App, Memory, EvolutionOperation, EvolutionInsight
from app.schemas import EvolutionOperationType


class TestDatabaseMigrations:
    """Test database migration validation and rollback procedures."""
    
    @pytest.fixture(autouse=True)
    def setup_migration_test_environment(self):
        """Set up migration test environment with temporary database."""
        # Create temporary database file
        self.db_fd, self.db_path = tempfile.mkstemp(suffix='.db')
        os.close(self.db_fd)
        
        try:
            # Create test engine
            self.test_engine = create_engine(
                f'sqlite:///{self.db_path}',
                echo=False
            )
            
            # Set up Alembic configuration
            self.alembic_cfg = Config()
            self.alembic_cfg.set_main_option("script_location", "alembic")
            self.alembic_cfg.set_main_option("sqlalchemy.url", f'sqlite:///{self.db_path}')
            
            # Create session maker
            TestSession = sessionmaker(bind=self.test_engine)
            self.session_maker = TestSession
            
            yield
            
        finally:
            if os.path.exists(self.db_path):
                os.unlink(self.db_path)
    
    def get_current_revision(self):
        """Get current database revision."""
        with self.test_engine.connect() as connection:
            context = MigrationContext.configure(connection)
            return context.get_current_revision()
    
    def get_table_names(self):
        """Get list of table names in the database."""
        inspector = inspect(self.test_engine)
        return inspector.get_table_names()
    
    def get_table_columns(self, table_name):
        """Get column information for a table."""
        inspector = inspect(self.test_engine)
        return inspector.get_columns(table_name)
    
    def get_table_indexes(self, table_name):
        """Get index information for a table."""
        inspector = inspect(self.test_engine)
        return inspector.get_indexes(table_name)
    
    def get_foreign_keys(self, table_name):
        """Get foreign key information for a table."""
        inspector = inspect(self.test_engine)
        return inspector.get_foreign_keys(table_name)
    
    def test_initial_migration_upgrade(self):
        """Test initial migration upgrade creates all required tables."""
        # Run initial migration
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify all expected tables exist
        table_names = self.get_table_names()
        expected_tables = [
            'users', 'apps', 'memories', 'config',
            'evolution_operations', 'evolution_insights',
            'alembic_version'
        ]
        
        for table in expected_tables:
            assert table in table_names, f"Table '{table}' not found after migration"
        
        # Verify current revision is set
        current_revision = self.get_current_revision()
        assert current_revision is not None, "No current revision set after migration"
        
        print(f"Initial migration completed. Current revision: {current_revision}")
    
    def test_evolution_tables_structure(self):
        """Test evolution intelligence tables have correct structure."""
        # Run migrations
        command.upgrade(self.alembic_cfg, "head")
        
        # Test evolution_operations table structure
        evo_ops_columns = self.get_table_columns('evolution_operations')
        evo_ops_column_names = [col['name'] for col in evo_ops_columns]
        
        expected_evo_ops_columns = [
            'id', 'user_id', 'app_id', 'memory_id', 'operation_type',
            'candidate_fact', 'confidence_score', 'reasoning',
            'similarity_score', 'metadata', 'created_at'
        ]
        
        for col in expected_evo_ops_columns:
            assert col in evo_ops_column_names, f"Column '{col}' missing from evolution_operations"
        
        # Test evolution_insights table structure
        evo_insights_columns = self.get_table_columns('evolution_insights')
        evo_insights_column_names = [col['name'] for col in evo_insights_columns]
        
        expected_evo_insights_columns = [
            'id', 'user_id', 'app_id', 'date', 'total_operations',
            'add_operations', 'update_operations', 'delete_operations', 'noop_operations',
            'learning_efficiency', 'conflict_resolution_count',
            'average_confidence', 'average_similarity', 'created_at', 'updated_at'
        ]
        
        for col in expected_evo_insights_columns:
            assert col in evo_insights_column_names, f"Column '{col}' missing from evolution_insights"
        
        print("Evolution tables structure validation passed")
    
    def test_evolution_tables_indexes(self):
        """Test evolution intelligence tables have correct indexes."""
        # Run migrations
        command.upgrade(self.alembic_cfg, "head")
        
        # Test evolution_operations indexes
        evo_ops_indexes = self.get_table_indexes('evolution_operations')
        evo_ops_index_names = [idx['name'] for idx in evo_ops_indexes]
        
        expected_evo_ops_indexes = [
            'ix_evolution_operations_user_id',
            'ix_evolution_operations_app_id',
            'ix_evolution_operations_created_at',
            'ix_evolution_operations_operation_type',
            'ix_evolution_operations_confidence_score'
        ]
        
        for idx in expected_evo_ops_indexes:
            assert idx in evo_ops_index_names, f"Index '{idx}' missing from evolution_operations"
        
        # Test evolution_insights indexes
        evo_insights_indexes = self.get_table_indexes('evolution_insights')
        evo_insights_index_names = [idx['name'] for idx in evo_insights_indexes]
        
        expected_evo_insights_indexes = [
            'ix_evolution_insights_user_id',
            'ix_evolution_insights_app_id',
            'ix_evolution_insights_date',
            'ix_evolution_insights_learning_efficiency'
        ]
        
        for idx in expected_evo_insights_indexes:
            assert idx in evo_insights_index_names, f"Index '{idx}' missing from evolution_insights"
        
        print("Evolution tables indexes validation passed")
    
    def test_evolution_tables_foreign_keys(self):
        """Test evolution intelligence tables have correct foreign key constraints."""
        # Run migrations
        command.upgrade(self.alembic_cfg, "head")
        
        # Test evolution_operations foreign keys
        evo_ops_fks = self.get_foreign_keys('evolution_operations')
        
        # Should have foreign keys to users, apps, and memories
        fk_tables = [fk['referred_table'] for fk in evo_ops_fks]
        expected_fk_tables = ['users', 'apps', 'memories']
        
        for table in expected_fk_tables:
            assert table in fk_tables, f"Foreign key to '{table}' missing from evolution_operations"
        
        # Test evolution_insights foreign keys
        evo_insights_fks = self.get_foreign_keys('evolution_insights')
        
        # Should have foreign keys to users and apps
        insights_fk_tables = [fk['referred_table'] for fk in evo_insights_fks]
        expected_insights_fk_tables = ['users', 'apps']
        
        for table in expected_insights_fk_tables:
            assert table in insights_fk_tables, f"Foreign key to '{table}' missing from evolution_insights"
        
        print("Evolution tables foreign keys validation passed")
    
    def test_migration_with_existing_data(self):
        """Test migration works correctly with existing data."""
        # First, create database with initial schema (before evolution tables)
        command.upgrade(self.alembic_cfg, "f9eeaf6a02e1")  # Before evolution migration
        
        # Create test data in existing tables
        session = self.session_maker()
        try:
            test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Migration Test User"
            )
            session.add(test_user)
            
            test_app = App(
                id=uuid.uuid4(),
                name="Migration Test App",
                owner_id=test_user.id
            )
            session.add(test_app)
            
            test_memory = Memory(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                content="Test memory for migration",
                metadata={"test": "migration"}
            )
            session.add(test_memory)
            
            session.commit()
            
            # Verify data exists before migration
            user_count_before = session.query(User).count()
            app_count_before = session.query(App).count()
            memory_count_before = session.query(Memory).count()
            
            assert user_count_before == 1
            assert app_count_before == 1
            assert memory_count_before == 1
            
        finally:
            session.close()
        
        # Now run the evolution migration
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify existing data is preserved
        session = self.session_maker()
        try:
            user_count_after = session.query(User).count()
            app_count_after = session.query(App).count()
            memory_count_after = session.query(Memory).count()
            
            assert user_count_after == user_count_before
            assert app_count_after == app_count_before
            assert memory_count_after == memory_count_before
            
            # Verify evolution tables exist and are empty
            evo_ops_count = session.query(EvolutionOperation).count()
            evo_insights_count = session.query(EvolutionInsight).count()
            
            assert evo_ops_count == 0
            assert evo_insights_count == 0
            
            # Test that we can create evolution data
            test_operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                memory_id=test_memory.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Migration test fact",
                confidence_score=0.85,
                reasoning="Migration test reasoning",
                similarity_score=0.80
            )
            session.add(test_operation)
            session.commit()
            
            # Verify evolution operation was created
            created_operation = session.query(EvolutionOperation).first()
            assert created_operation is not None
            assert created_operation.candidate_fact == "Migration test fact"
            
        finally:
            session.close()
        
        print("Migration with existing data validation passed")
    
    def test_migration_rollback_procedures(self):
        """Test migration rollback procedures work correctly."""
        # Start with clean database
        initial_revision = self.get_current_revision()
        
        # Run full migration
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify evolution tables exist
        table_names_after_upgrade = self.get_table_names()
        assert 'evolution_operations' in table_names_after_upgrade
        assert 'evolution_insights' in table_names_after_upgrade
        
        # Create test data in evolution tables
        session = self.session_maker()
        try:
            test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Rollback Test User"
            )
            session.add(test_user)
            
            test_app = App(
                id=uuid.uuid4(),
                name="Rollback Test App",
                owner_id=test_user.id
            )
            session.add(test_app)
            
            test_operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Rollback test fact",
                confidence_score=0.90,
                reasoning="Rollback test reasoning",
                similarity_score=0.85
            )
            session.add(test_operation)
            
            test_insight = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                date=datetime.now().date(),
                total_operations=1,
                add_operations=1,
                update_operations=0,
                delete_operations=0,
                noop_operations=0,
                learning_efficiency=0.95,
                conflict_resolution_count=0,
                average_confidence=0.90,
                average_similarity=0.85
            )
            session.add(test_insight)
            
            session.commit()
            
            # Verify data exists
            ops_count_before_rollback = session.query(EvolutionOperation).count()
            insights_count_before_rollback = session.query(EvolutionInsight).count()
            
            assert ops_count_before_rollback == 1
            assert insights_count_before_rollback == 1
            
        finally:
            session.close()
        
        # Now test rollback to before evolution migration
        command.downgrade(self.alembic_cfg, "f9eeaf6a02e1")  # Before evolution migration
        
        # Verify evolution tables are removed
        table_names_after_rollback = self.get_table_names()
        assert 'evolution_operations' not in table_names_after_rollback
        assert 'evolution_insights' not in table_names_after_rollback
        
        # Verify core tables still exist
        assert 'users' in table_names_after_rollback
        assert 'apps' in table_names_after_rollback
        assert 'memories' in table_names_after_rollback
        
        # Verify existing data in core tables is preserved
        session = self.session_maker()
        try:
            user_count_after_rollback = session.query(User).count()
            app_count_after_rollback = session.query(App).count()
            
            assert user_count_after_rollback == 1
            assert app_count_after_rollback == 1
            
            # Verify user data is intact
            user = session.query(User).first()
            assert user.email == "<EMAIL>"
            assert user.name == "Rollback Test User"
            
        finally:
            session.close()
        
        print("Migration rollback procedures validation passed")
    
    def test_migration_idempotency(self):
        """Test that migrations are idempotent (can be run multiple times safely)."""
        # Run migration to head
        command.upgrade(self.alembic_cfg, "head")
        
        # Get initial state
        initial_tables = set(self.get_table_names())
        initial_revision = self.get_current_revision()
        
        # Run migration again (should be no-op)
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify state is unchanged
        final_tables = set(self.get_table_names())
        final_revision = self.get_current_revision()
        
        assert initial_tables == final_tables, "Tables changed after idempotent migration"
        assert initial_revision == final_revision, "Revision changed after idempotent migration"
        
        # Test with data
        session = self.session_maker()
        try:
            test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Idempotent Test User"
            )
            session.add(test_user)
            session.commit()
            
            initial_user_count = session.query(User).count()
            
        finally:
            session.close()
        
        # Run migration again with data
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify data is preserved
        session = self.session_maker()
        try:
            final_user_count = session.query(User).count()
            assert initial_user_count == final_user_count, "User count changed after idempotent migration"
            
            user = session.query(User).filter_by(email="<EMAIL>").first()
            assert user is not None, "User data lost after idempotent migration"
            
        finally:
            session.close()
        
        print("Migration idempotency validation passed")
    
    def test_migration_data_integrity_constraints(self):
        """Test that migration preserves data integrity constraints."""
        # Run migrations
        command.upgrade(self.alembic_cfg, "head")
        
        session = self.session_maker()
        try:
            # Create test entities
            test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Integrity Test User"
            )
            session.add(test_user)
            
            test_app = App(
                id=uuid.uuid4(),
                name="Integrity Test App",
                owner_id=test_user.id
            )
            session.add(test_app)
            
            test_memory = Memory(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                content="Integrity test memory",
                metadata={"test": "integrity"}
            )
            session.add(test_memory)
            
            session.commit()
            
            # Test foreign key constraints for evolution_operations
            valid_operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                memory_id=test_memory.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Valid operation",
                confidence_score=0.85,
                reasoning="Valid reasoning",
                similarity_score=0.80
            )
            session.add(valid_operation)
            session.commit()
            
            # Test that invalid foreign key fails
            invalid_operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=uuid.uuid4(),  # Non-existent user
                app_id=test_app.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Invalid operation",
                confidence_score=0.85,
                reasoning="Invalid reasoning",
                similarity_score=0.80
            )
            session.add(invalid_operation)
            
            with pytest.raises(Exception):  # Should fail due to foreign key constraint
                session.commit()
            
            session.rollback()
            
            # Test unique constraints for evolution_insights
            insight1 = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                date=datetime.now().date(),
                total_operations=1,
                add_operations=1,
                update_operations=0,
                delete_operations=0,
                noop_operations=0,
                learning_efficiency=0.95,
                conflict_resolution_count=0,
                average_confidence=0.90,
                average_similarity=0.85
            )
            session.add(insight1)
            session.commit()
            
            # Try to create duplicate insight (same user, app, date)
            insight2 = EvolutionInsight(
                id=uuid.uuid4(),
                user_id=test_user.id,
                app_id=test_app.id,
                date=datetime.now().date(),  # Same date
                total_operations=2,
                add_operations=2,
                update_operations=0,
                delete_operations=0,
                noop_operations=0,
                learning_efficiency=0.90,
                conflict_resolution_count=0,
                average_confidence=0.85,
                average_similarity=0.80
            )
            session.add(insight2)
            
            with pytest.raises(Exception):  # Should fail due to unique constraint
                session.commit()
            
            session.rollback()
            
        finally:
            session.close()
        
        print("Migration data integrity constraints validation passed")
    
    def test_migration_performance_impact(self):
        """Test that migration completes within reasonable time."""
        import time
        
        # Measure migration time
        start_time = time.perf_counter()
        command.upgrade(self.alembic_cfg, "head")
        end_time = time.perf_counter()
        
        migration_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"Migration completed in: {migration_time:.2f}ms")
        
        # Migration should complete quickly for empty database
        assert migration_time < 5000, f"Migration took {migration_time:.2f}ms, exceeds 5 second threshold"
        
        # Test migration with data
        session = self.session_maker()
        try:
            # Create substantial test data
            users = []
            apps = []
            memories = []
            
            for i in range(100):
                user = User(
                    id=uuid.uuid4(),
                    email=f"perf_user_{i}@example.com",
                    name=f"Performance User {i}"
                )
                users.append(user)
                session.add(user)
                
                app = App(
                    id=uuid.uuid4(),
                    name=f"Performance App {i}",
                    owner_id=user.id
                )
                apps.append(app)
                session.add(app)
                
                for j in range(5):  # 5 memories per app
                    memory = Memory(
                        id=uuid.uuid4(),
                        user_id=user.id,
                        app_id=app.id,
                        content=f"Performance test memory {i}-{j}",
                        metadata={"test": "performance", "index": j}
                    )
                    memories.append(memory)
                    session.add(memory)
            
            session.commit()
            
        finally:
            session.close()
        
        # Test rollback performance
        start_time = time.perf_counter()
        command.downgrade(self.alembic_cfg, "f9eeaf6a02e1")
        end_time = time.perf_counter()
        
        rollback_time = (end_time - start_time) * 1000
        
        print(f"Rollback completed in: {rollback_time:.2f}ms")
        
        # Rollback should also be reasonably fast
        assert rollback_time < 5000, f"Rollback took {rollback_time:.2f}ms, exceeds 5 second threshold"
        
        print("Migration performance impact validation passed")
    
    def test_migration_script_consistency(self):
        """Test that migration scripts are consistent and well-formed."""
        # Get script directory
        script_dir = ScriptDirectory.from_config(self.alembic_cfg)
        
        # Get all revisions
        revisions = list(script_dir.walk_revisions())
        
        assert len(revisions) > 0, "No migration revisions found"
        
        # Test each revision
        for revision in revisions:
            # Verify revision has required attributes
            assert revision.revision is not None, f"Revision {revision} missing revision ID"
            assert revision.down_revision is not None or revision.revision == script_dir.get_base(), \
                f"Revision {revision.revision} missing down_revision"
            
            # Verify upgrade and downgrade functions exist
            module = revision.module
            assert hasattr(module, 'upgrade'), f"Revision {revision.revision} missing upgrade function"
            assert hasattr(module, 'downgrade'), f"Revision {revision.revision} missing downgrade function"
            
            # Verify functions are callable
            assert callable(module.upgrade), f"Revision {revision.revision} upgrade is not callable"
            assert callable(module.downgrade), f"Revision {revision.revision} downgrade is not callable"
        
        # Test that we can get head revision
        head_revision = script_dir.get_current_head()
        assert head_revision is not None, "No head revision found"
        
        print(f"Migration script consistency validation passed. Found {len(revisions)} revisions.")
    
    def test_complete_migration_cycle(self):
        """Test complete migration cycle: upgrade -> populate -> rollback -> re-upgrade."""
        # Start with clean state
        initial_tables = self.get_table_names()
        
        # Step 1: Upgrade to head
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify evolution tables exist
        tables_after_upgrade = self.get_table_names()
        assert 'evolution_operations' in tables_after_upgrade
        assert 'evolution_insights' in tables_after_upgrade
        
        # Step 2: Populate with test data
        session = self.session_maker()
        try:
            test_user = User(
                id=uuid.uuid4(),
                email="<EMAIL>",
                name="Cycle Test User"
            )
            session.add(test_user)
            
            test_app = App(
                id=uuid.uuid4(),
                name="Cycle Test App",
                owner_id=test_user.id
            )
            session.add(test_app)
            
            # Create evolution data
            for i in range(10):
                operation = EvolutionOperation(
                    id=uuid.uuid4(),
                    user_id=test_user.id,
                    app_id=test_app.id,
                    operation_type=EvolutionOperationType((i % 4) + 1),
                    candidate_fact=f"Cycle test fact {i}",
                    confidence_score=0.7 + (i * 0.02),
                    reasoning=f"Cycle test reasoning {i}",
                    similarity_score=0.6 + (i * 0.03)
                )
                session.add(operation)
            
            session.commit()
            
            # Verify data exists
            ops_count = session.query(EvolutionOperation).count()
            assert ops_count == 10
            
        finally:
            session.close()
        
        # Step 3: Rollback
        command.downgrade(self.alembic_cfg, "f9eeaf6a02e1")
        
        # Verify evolution tables are gone
        tables_after_rollback = self.get_table_names()
        assert 'evolution_operations' not in tables_after_rollback
        assert 'evolution_insights' not in tables_after_rollback
        
        # Verify core data is preserved
        session = self.session_maker()
        try:
            user_count = session.query(User).count()
            app_count = session.query(App).count()
            assert user_count == 1
            assert app_count == 1
            
        finally:
            session.close()
        
        # Step 4: Re-upgrade
        command.upgrade(self.alembic_cfg, "head")
        
        # Verify evolution tables are back
        tables_after_reupgrade = self.get_table_names()
        assert 'evolution_operations' in tables_after_reupgrade
        assert 'evolution_insights' in tables_after_reupgrade
        
        # Verify we can create new evolution data
        session = self.session_maker()
        try:
            user = session.query(User).first()
            app = session.query(App).first()
            
            new_operation = EvolutionOperation(
                id=uuid.uuid4(),
                user_id=user.id,
                app_id=app.id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Post-cycle test fact",
                confidence_score=0.95,
                reasoning="Post-cycle test reasoning",
                similarity_score=0.90
            )
            session.add(new_operation)
            session.commit()
            
            # Verify operation was created
            final_ops_count = session.query(EvolutionOperation).count()
            assert final_ops_count == 1
            
        finally:
            session.close()
        
        print("Complete migration cycle validation passed")