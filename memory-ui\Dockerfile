FROM node:18-alpine

WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Create proper symlink for next binary
RUN ln -sf /usr/src/app/node_modules/next/dist/bin/next /usr/src/app/node_modules/.bin/next && \
    chmod +x /usr/src/app/node_modules/.bin/next

# Skip build during Docker build - will build at runtime with env vars available

# Expose port
EXPOSE 3210

# Start the application for development
CMD ["npm", "run", "dev", "--", "--hostname", "0.0.0.0"]