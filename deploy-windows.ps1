# Memory Master Deployment Script for Windows
# Requires: Docker Desktop for Windows, PowerShell 5.1+

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "prod")]
    [string]$Environment = "dev",
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Logs = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Stop = $false
)

# Color output functions
function Write-Success($message) {
    Write-Host "SUCCESS: $message" -ForegroundColor Green
}

function Write-Error($message) {
    Write-Host "ERROR: $message" -ForegroundColor Red
}

function Write-Info($message) {
    Write-Host "INFO: $message" -ForegroundColor Cyan
}

function Write-Warning($message) {
    Write-Host "WARNING: $message" -ForegroundColor Yellow
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        Write-Success "Docker found: $dockerVersion"
    }
    catch {
        Write-Error "Docker not found. Please install Docker Desktop for Windows."
        exit 1
    }
    
    # Check Docker Compose
    try {
        $composeVersion = docker compose version
        Write-Success "Docker Compose found: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose not found. Please ensure Docker Desktop is properly installed."
        exit 1
    }
    
    # Check if Docker is running
    try {
        docker info | Out-Null
        Write-Success "Docker daemon is running"
    }
    catch {
        Write-Error "Docker daemon is not running. Please start Docker Desktop."
        exit 1
    }
}

# Clean up function
function Invoke-Cleanup {
    Write-Info "Cleaning up existing containers and volumes..."
    
    try {
        docker compose -f docker-compose.$Environment.yml down --volumes --remove-orphans
        if ($Clean) {
            docker system prune -f
            Write-Success "System cleanup completed"
        }
        Write-Success "Cleanup completed"
    }
    catch {
        Write-Warning "Cleanup had some issues, but continuing..."
    }
}

# Deploy function
function Invoke-Deploy {
    Write-Info "Deploying Memory Master in $Environment mode..."
    
    # Check environment file
    if ($Environment -eq "prod") {
        if (!(Test-Path ".env.production")) {
            Write-Error "Production environment file not found. Please copy .env.production.template to .env.production and configure it."
            exit 1
        }
        $envFile = ".env.production"
    } else {
        $envFile = ".env"
    }
    
    Write-Info "Using environment file: $envFile"
    
    try {
        # Build and start services
        Write-Info "Building and starting services..."
        docker compose -f docker-compose.$Environment.yml --env-file $envFile up --build -d
        
        # Wait for services to be healthy
        Write-Info "Waiting for services to be healthy..."
        Start-Sleep -Seconds 10
        
        # Check service health
        $apiHealth = docker compose -f docker-compose.$Environment.yml ps --format json | ConvertFrom-Json | Where-Object { $_.Service -eq "memory-api" }
        $uiHealth = docker compose -f docker-compose.$Environment.yml ps --format json | ConvertFrom-Json | Where-Object { $_.Service -eq "memory-ui" }
        
        if ($apiHealth.State -eq "running") {
            Write-Success "API service is running"
        } else {
            Write-Warning "API service might have issues"
        }
        
        if ($uiHealth.State -eq "running") {
            Write-Success "UI service is running"
        } else {
            Write-Warning "UI service might have issues"
        }
        
        Write-Success "Deployment completed!"
        Write-Info "Services available at:"
        Write-Host "  Web UI: http://localhost:3210" -ForegroundColor Yellow
        Write-Host "  API: http://localhost:8765" -ForegroundColor Yellow
        
    }
    catch {
        Write-Error "Deployment failed: $_"
        Write-Info "Checking logs for more information..."
        docker compose -f docker-compose.$Environment.yml logs --tail=50
        exit 1
    }
}

# Show logs function
function Show-Logs {
    Write-Info "Showing recent logs..."
    docker compose -f docker-compose.$Environment.yml logs --tail=100 -f
}

# Stop services function
function Stop-Services {
    Write-Info "Stopping services..."
    docker compose -f docker-compose.$Environment.yml down
    Write-Success "Services stopped"
}

# Main execution
try {
    Write-Info "Memory Master Deployment Script for Windows"
    Write-Info "Environment: $Environment"
    
    if ($Stop) {
        Stop-Services
        exit 0
    }
    
    if ($Logs) {
        Show-Logs
        exit 0
    }
    
    Test-Prerequisites
    
    if ($Clean) {
        Invoke-Cleanup
    }
    
    Invoke-Deploy
    
    Write-Success "Deployment script completed successfully!"
    Write-Info "Use 'docker compose -f docker-compose.$Environment.yml logs -f' to follow logs"
    Write-Info "Use './deploy-windows.ps1 -Stop' to stop services"
    
}
catch {
    Write-Error "Deployment script failed: $_"
    exit 1
}