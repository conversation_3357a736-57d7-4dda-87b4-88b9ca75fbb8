"""
Comprehensive integration tests for the memory system.

This module tests the complete integration of all memory system components:
- MemoryClientSingleton with thread safety
- Configuration management with hot-reload
- Memory operations with degradation support
- Health checks and monitoring
- Database and vector store consistency
"""

import pytest
import threading
import time
import uuid
from unittest.mock import patch, Mock

from app.utils.memory import Memory<PERSON>lient<PERSON><PERSON>leton, reset_memory_client
from app.utils.config_manager import Config<PERSON><PERSON>ger
from app.enhanced_logging import MemoryOperationStatus
from tests.utils.test_helpers import (
    mock_memory_client, wait_for_condition, assert_memory_operation_result,
    temporary_config_change
)


class TestMemorySystemIntegration:
    """Test complete memory system integration."""
    
    def test_singleton_pattern_thread_safety(self, memory_singleton, concurrency_helper):
        """Test that singleton pattern works correctly under concurrent access."""
        instances = []
        
        def get_instance():
            instance = MemoryClientSingleton()
            instances.append(instance)
        
        # Run concurrent singleton access
        args_list = [() for _ in range(20)]
        results = concurrency_helper.run_concurrent_operations(
            get_instance, args_list, max_workers=20
        )
        
        # Verify no errors occurred
        assert results["error_count"] == 0, f"Errors occurred: {results['errors']}"
        assert results["success_count"] == 20, "All operations should complete"
        
        # Verify all instances are identical
        unique_instances = set(id(instance) for instance in instances)
        assert len(unique_instances) == 1, "All instances should be identical"
    
    def test_memory_lifecycle_integration(self, memory_singleton, patched_memory_client, test_config):
        """Test complete memory lifecycle: add → retrieve → update → delete."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        assert client is not None, "Client should be initialized"
        
        # Test memory addition
        content = "Integration test memory"
        metadata = {"test": "integration", "timestamp": time.time()}
        
        add_result = memory_singleton.add_memory_async(content, metadata)
        assert add_result[0], f"Memory addition should succeed: {add_result[1]}"
        
        # Wait for async operation to complete
        assert memory_singleton.wait_for_queue_empty(timeout=5.0), "Queue should be empty"
        
        # Verify memory was stored
        assert len(mock_vector_store.stored_memories) == 1, "Memory should be stored"
        stored_memory = list(mock_vector_store.stored_memories.values())[0]
        assert stored_memory["content"] == content, "Content should match"
        assert stored_memory["metadata"] == metadata, "Metadata should match"
        
        # Test memory retrieval
        memory_id = stored_memory["id"]
        retrieved = mock_vector_store.get(memory_id)
        assert retrieved is not None, "Memory should be retrievable"
        assert retrieved["content"] == content, "Retrieved content should match"
    
    def test_config_hot_reload_integration(self, memory_singleton, test_config, non_critical_config_change):
        """Test configuration hot-reload integration with memory client."""
        with patch('app.utils.memory.Memory') as mock_memory_class:
            mock_client = Mock()
            mock_memory_class.from_config.return_value = mock_client

            # Initialize client with original config
            client1 = memory_singleton.get_client(test_config)
            assert client1 is not None, "Initial client should be initialized"

            # Test that client remains the same for non-critical changes
            # (This test focuses on the memory client behavior rather than config manager internals)
            client2 = memory_singleton.get_client(test_config)
            assert client2 is client1, "Client should remain the same when config hasn't changed"

            # Test that we can get client multiple times without reinitialization
            client3 = memory_singleton.get_client(test_config)
            assert client3 is client1, "Client should be reused"
    
    def test_health_check_integration(self, memory_singleton, patched_memory_client, test_config):
        """Test health check integration with actual operations."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Health check with no client should return False
        assert not memory_singleton.is_healthy(), "Health check should fail with no client"
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        assert client is not None, "Client should be initialized"
        
        # Health check with client should return True
        assert memory_singleton.is_healthy(), "Health check should pass with initialized client"
        
        # Perform memory operation to verify client is working
        success, message = memory_singleton.add_memory_async("Health check test")
        assert success, f"Memory operation should succeed: {message}"
        
        # Wait for operation and verify health is still good
        assert memory_singleton.wait_for_queue_empty(timeout=5.0)
        assert memory_singleton.is_healthy(), "Health should remain good after operations"
    
    def test_concurrent_memory_operations(self, memory_singleton, patched_memory_client, 
                                        test_config, concurrency_helper):
        """Test concurrent memory operations maintain consistency."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        assert client is not None, "Client should be initialized"
        
        # Define concurrent operation
        def add_memory(index):
            content = f"Concurrent memory {index}"
            metadata = {"test": "concurrent", "index": index}
            return memory_singleton.add_memory_async(content, metadata)
        
        # Run concurrent operations
        args_list = [(i,) for i in range(50)]
        results = concurrency_helper.run_concurrent_operations(
            add_memory, args_list, max_workers=20
        )
        
        # Verify all operations succeeded
        assert results["error_count"] == 0, f"Errors occurred: {results['errors']}"
        assert results["success_count"] == 50, "All operations should succeed"
        
        # Wait for all operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=10.0), "All operations should complete"
        
        # Verify all memories were stored
        assert len(mock_vector_store.stored_memories) == 50, "All memories should be stored"
        
        # Verify queue status is clean
        queue_status = memory_singleton.get_operation_queue_status()
        assert queue_status["queue_size"] == 0, "Queue should be empty"
        assert queue_status["worker_running"], "Worker should still be running"
    
    def test_degradation_mode_integration(self, memory_singleton, patched_failing_memory_client, test_config):
        """Test graceful degradation integration."""
        mock_client, failing_vector_store = patched_failing_memory_client
        
        # Mock database-only storage
        with patch.object(memory_singleton, '_store_in_database_only') as mock_db_store:
            mock_db_store.return_value = {"id": "db-only-id", "status": "success", "fallback_mode": True}
            
            # Initialize client (this should work even with failing vector store)
            client = memory_singleton.get_client(test_config)
            
            # Try to add memory - should trigger degradation
            result = memory_singleton.add_memory_with_degradation(
                "Degradation test", {"test": "degradation"}
            )
            
            # Verify degraded mode was used
            assert result is not None, "Operation should complete in degraded mode"
            assert mock_db_store.called, "Database-only storage should be called"
            
            # Verify memory was added to backlog
            assert len(memory_singleton._operation_backlog) > 0, "Operation should be in backlog"
    
    def test_recovery_from_degradation(self, memory_singleton, test_config):
        """Test recovery from degraded mode."""
        # Start with failing vector store
        with patch('app.utils.memory.Memory') as mock_memory_class:
            failing_client = Mock()
            failing_client.add.side_effect = ConnectionError("Vector store unavailable")
            
            working_client = Mock()
            working_client.add.return_value = {"id": "recovered-id", "status": "success"}
            
            # First call fails, second succeeds (simulating recovery)
            mock_memory_class.from_config.side_effect = [failing_client, working_client]
            
            # Initialize with failing client
            client1 = memory_singleton.get_client(test_config)
            
            # Add memory to backlog (degraded mode)
            with patch.object(memory_singleton, '_store_in_database_only') as mock_db_store:
                mock_db_store.return_value = {"id": "db-id", "status": "success", "fallback_mode": True}
                
                result = memory_singleton.add_memory_with_degradation("Recovery test")
                assert len(memory_singleton._operation_backlog) > 0, "Should have backlog items"
            
            # Simulate recovery attempt
            memory_singleton._attempt_vector_store_recovery()
            
            # Verify recovery was attempted
            assert mock_memory_class.from_config.call_count >= 2, "Recovery should be attempted"
    
    def test_operation_queue_management(self, memory_singleton, patched_memory_client, test_config):
        """Test operation queue management and status reporting."""
        mock_client, mock_vector_store = patched_memory_client
        
        # Initialize client
        client = memory_singleton.get_client(test_config)
        assert client is not None, "Client should be initialized"
        
        # Get initial queue status
        initial_status = memory_singleton.get_operation_queue_status()
        assert initial_status["queue_size"] == 0, "Queue should start empty"
        
        # Add multiple operations quickly
        for i in range(10):
            success, message = memory_singleton.add_memory_async(f"Queue test {i}")
            assert success, f"Operation {i} should be queued successfully"
        
        # Check queue status while operations are processing
        status_during = memory_singleton.get_operation_queue_status()
        assert status_during["worker_running"], "Worker should be running"
        
        # Wait for all operations to complete
        assert memory_singleton.wait_for_queue_empty(timeout=10.0), "Queue should empty"
        
        # Check final status
        final_status = memory_singleton.get_operation_queue_status()
        assert final_status["queue_size"] == 0, "Queue should be empty"
        assert final_status["operation_counter"] >= 10, "Operation counter should be updated"
        
        # Verify all memories were processed
        assert len(mock_vector_store.stored_memories) == 10, "All memories should be stored"
    
    def test_configuration_validation_integration(self, memory_singleton, config_manager):
        """Test configuration validation integration."""
        # Test invalid configuration
        invalid_config = {
            "mem0": {
                "llm": {
                    "provider": "invalid_provider"
                    # Missing required config
                }
            }
        }
        
        with pytest.raises(ValueError, match="Invalid configuration"):
            config_manager.update_config(invalid_config)
        
        # Test valid configuration
        valid_config = {
            "mem0": {
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4o-mini",
                        "api_key": "test-key"
                    }
                }
            }
        }
        
        # Should not raise exception
        config_manager.update_config(valid_config, save_to_db=False)
        updated_config = config_manager.get_config()
        assert updated_config["mem0"]["llm"]["provider"] == "openai"
