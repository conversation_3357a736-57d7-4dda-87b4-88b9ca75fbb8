#!/bin/bash
# Container Health Monitor for Memory Master v2
# Monitors both UI and API containers for stability

set -e

echo "🔍 Memory Master Container Health Monitor"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check container status
check_container() {
    local container_name=$1
    local service_name=$2
    local port=$3
    local endpoint=$4
    
    echo -e "\n📊 Checking ${service_name}..."
    
    # Check if container is running
    if docker-compose ps ${container_name} | grep -q "Up"; then
        echo -e "  ✅ Container: ${GREEN}Running${NC}"
        
        # Check HTTP endpoint
        if curl -f http://localhost:${port}${endpoint} >/dev/null 2>&1; then
            echo -e "  ✅ HTTP: ${GREEN}Responding${NC}"
            return 0
        else
            echo -e "  ❌ HTTP: ${RED}Not responding${NC}"
            return 1
        fi
    else
        echo -e "  ❌ Container: ${RED}Not running${NC}"
        return 1
    fi
}

# Function to show recent logs
show_logs() {
    local container_name=$1
    local lines=${2:-10}
    
    echo -e "\n📋 Recent logs for ${container_name}:"
    echo "----------------------------------------"
    docker-compose logs --tail=${lines} ${container_name} | head -20
}

# Function to restart service if needed
restart_service() {
    local container_name=$1
    local service_name=$2
    
    echo -e "\n🔄 Restarting ${service_name}..."
    docker-compose restart ${container_name}
    sleep 10
    
    if docker-compose ps ${container_name} | grep -q "Up"; then
        echo -e "  ✅ ${service_name} restarted successfully"
    else
        echo -e "  ❌ ${service_name} restart failed"
    fi
}

# Main monitoring logic
main() {
    local ui_healthy=false
    local api_healthy=false
    
    # Check UI container
    if check_container "memory-ui" "Memory UI" "3210" "/"; then
        ui_healthy=true
    else
        echo -e "  ⚠️  UI container issues detected"
        show_logs "memory-ui" 5
    fi
    
    # Check API container  
    if check_container "memory-mcp" "Memory API" "8765" "/health"; then
        api_healthy=true
    else
        echo -e "  ⚠️  API container issues detected"
        show_logs "memory-mcp" 5
    fi
    
    # Overall status
    echo -e "\n🎯 OVERALL STATUS"
    echo "================="
    
    if $ui_healthy && $api_healthy; then
        echo -e "✅ ${GREEN}All services healthy${NC}"
        echo "🌐 UI: http://localhost:3210"
        echo "🔗 API: http://localhost:8765"
    else
        echo -e "⚠️  ${YELLOW}Some services need attention${NC}"
        
        # Auto-restart unhealthy services if requested
        if [[ "$1" == "--auto-restart" ]]; then
            echo -e "\n🔧 Auto-restart enabled..."
            
            if ! $ui_healthy; then
                restart_service "memory-ui" "Memory UI"
            fi
            
            if ! $api_healthy; then
                restart_service "memory-mcp" "Memory API"
            fi
            
            # Re-check after restart
            echo -e "\n🔄 Re-checking after restart..."
            sleep 5
            main
        else
            echo -e "\n💡 To auto-restart failed services, run:"
            echo "   $0 --auto-restart"
        fi
    fi
    
    # Container stats
    echo -e "\n📈 CONTAINER STATS"
    echo "=================="
    docker-compose ps
    
    # Performance check
    echo -e "\n⚡ PERFORMANCE CHECK"
    echo "==================="
    
    # UI response time
    ui_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3210/ 2>/dev/null || echo "N/A")
    echo "UI Response Time: ${ui_time}s"
    
    # API response time
    api_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8765/health 2>/dev/null || echo "N/A")
    echo "API Response Time: ${api_time}s"
    
    # Check for webpack warnings in UI logs
    echo -e "\n🔍 WEBPACK WARNING CHECK"
    echo "======================="
    if docker-compose logs memory-ui 2>/dev/null | grep -q "Serializing big strings"; then
        echo -e "⚠️  ${YELLOW}Webpack cache warning detected${NC}"
        echo "   The cache performance fixes may need adjustment"
    else
        echo -e "✅ ${GREEN}No webpack cache warnings${NC}"
    fi
}

# Help function
show_help() {
    echo "Memory Master Container Monitor"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --auto-restart    Automatically restart failed services"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                 # Monitor container health"
    echo "  $0 --auto-restart # Monitor and auto-restart failed services"
}

# Parse arguments
if [[ "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# Run main function
main "$@"