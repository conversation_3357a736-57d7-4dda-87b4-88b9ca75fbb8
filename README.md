# Memory Master

<div align="center">
  <img src="https://img.shields.io/badge/FastAPI-0.104.1-blue.svg" alt="FastAPI">
  <img src="https://img.shields.io/badge/Next.js-15.0-black.svg" alt="Next.js">
  <img src="https://img.shields.io/badge/Python-3.12+-green.svg" alt="Python">
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue.svg" alt="TypeScript">
  <img src="https://img.shields.io/badge/Docker-Compose-2496ED.svg" alt="Docker">
  <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
</div>

**Memory Master v2** is a comprehensive personal memory layer for Large Language Models (LLMs) that provides private, portable, and open-source memory management. It enables AI assistants to remember context across conversations while giving you complete control over your data.

## 🚀 Features

### Core Capabilities
- **🧠 Intelligent Memory Management**: Advanced memory storage and retrieval using vector embeddings
- **🔌 MCP Server Integration**: Compatible with Claude Desktop, VS Code, and other MCP-enabled applications
- **🎯 Evolution Intelligence**: Automatic memory optimization with ADD/UPDATE/DELETE/NOOP operations
- **🌐 Web Dashboard**: Intuitive UI for managing memories, apps, and system configuration
- **🔒 Privacy-First**: All data stored locally with optional Supabase integration

### Advanced Features
- **📊 Analytics & Monitoring**: Comprehensive memory access logs and performance metrics
- **🐳 Docker-Ready**: Containerized deployment with Docker Compose
- **⚡ High Performance**: Optimized for <200ms response times with Qdrant vector store
- **👥 Multi-User Support**: Isolated user environments with shared infrastructure
- **🔄 Real-time Sync**: Live updates across all connected clients
- **🎨 Modern UI**: Clean, responsive interface built with Next.js and Tailwind CSS

## 🏗️ Architecture Overview

Memory Master v2 follows a **microservices architecture** with remote infrastructure components:

### Core Services
- **API Server** (FastAPI): RESTful API and MCP server functionality
- **Remote Vector Store** (Qdrant): High-performance vector database hosted remotely
- **Remote Database** (Supabase PostgreSQL): Cloud-hosted relational data storage
- **Web UI** (Next.js): React-based dashboard for memory management
- **Remote Backup System** (MinIO): Cloud-based automated backup and recovery

### Technology Stack
- **Backend**: Python 3.12, FastAPI, SQLAlchemy, mem0ai, Alembic
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS, Redux Toolkit
- **Database**: PostgreSQL (Supabase), Qdrant Vector DB
- **Infrastructure**: Docker, Docker Compose
- **AI/ML**: OpenAI GPT-4, text-embedding-3-small
- **Authentication**: Supabase Auth (optional)

## 📋 Prerequisites

Before installing Memory Master v2, ensure you have:

### Required
- **Docker & Docker Compose** (v20.10+ recommended)
- **OpenAI API Key** (required for LLM operations)
- **Network Access** to remote infrastructure:
  - Qdrant Vector Store: `*************:6333`
  - Supabase Database: `*************:54322`
  - MinIO Storage: `*************:9000`

### System Requirements
- **2GB+ RAM** (reduced due to remote vector storage)
- **1GB+ Storage** (for local container images only)
- **User Account** on the shared infrastructure

### Optional Requirements
- **Node.js 18+** (for local UI development)
- **Python 3.12+** (for local API development)
- **VPN Access** (if connecting from outside the network)

## ⚡ Quick Start

### 1. Clone and Setup Environment

```bash
# Clone the repository
git clone <repository-url>
cd memory-master

# Create environment files
cp api/.env.example api/.env
cp memory-ui/.env.example memory-ui/.env.local
```

### 2. Configure Environment Variables

#### Backend Configuration (`api/.env`)
```env
# Required
OPENAI_API_KEY=sk-your-openai-api-key-here
USER=aungheinaye  # or yohanna

# Remote Qdrant Vector Database
QDRANT_HOST=*************
QDRANT_PORT=6333

# Remote Supabase Database
DATABASE_URL=***********************************************************************************
MIGRATION_MODE=supabase_only

# Optional
API_KEY=your-optional-api-key
AUTH_ENABLED=false
```

#### Frontend Configuration (`memory-ui/.env.local`)
```env
# Required
NEXT_PUBLIC_API_URL=http://localhost:8765
NEXT_PUBLIC_USER_ID=aungheinaye  # or yohanna

# Optional Supabase Auth
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXT_PUBLIC_AUTH_ENABLED=false
```

### 3. Launch the System

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Access the Applications

- **Web Dashboard**: http://localhost:3210
- **API Documentation**: http://localhost:8765/docs
- **MCP Server**: http://localhost:8765 (for Claude Desktop integration)
- **Remote Qdrant Console**: http://*************:6333/dashboard

## 🔧 Development

### Local Development Commands

```bash
# Backend API (from api/ directory)
cd api
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8765 --reload --workers 4

# Frontend UI (from memory-ui/ directory)
cd memory-ui
npm install
npm run dev     # Development server
npm run build   # Production build
npm run lint    # ESLint checking
```

### Database Management

```bash
# Database migrations (from api/ directory)
alembic upgrade head                              # Apply latest migrations
alembic revision --autogenerate -m "description" # Create new migration
alembic downgrade -1                             # Rollback one migration
```

### Testing

```bash
# Backend tests
cd api && pytest                    # Run all tests
pytest -v                          # Verbose output
pytest --cov                       # Coverage report

# Frontend E2E tests
cd memory-ui && npm run test:e2e    # Run Playwright tests
npm run test:e2e:ui                # Run with UI
```

## 🔌 MCP Integration

### Claude Desktop Setup

1. Add to your Claude Desktop configuration (`claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "memory-master": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765"
      }
    }
  }
}
```

2. Restart Claude Desktop and verify the connection.

### Available MCP Tools

- **`add_memories`**: Store new memories with automatic chunking
- **`search_memory`**: Semantic search across stored memories  
- **`list_memories`**: Browse memories with filtering options
- **`get_system_health`**: Monitor system performance
- **`get_evolution_metrics`**: View memory optimization statistics

## 📁 Project Structure

```
memory-master/
├── api/                      # Backend API & MCP Server
│   ├── app/                  # FastAPI application
│   │   ├── routers/          # API route handlers
│   │   │   ├── memories.py   # Memory CRUD operations
│   │   │   ├── auth.py       # Authentication endpoints
│   │   │   ├── config.py     # Configuration management
│   │   │   └── evolution_config.py # Evolution intelligence
│   │   ├── services/         # Business logic services
│   │   │   ├── evolution_service.py # Evolution analytics
│   │   │   └── evolution_config_service.py # Evolution config
│   │   ├── utils/            # Utility functions
│   │   │   ├── memory.py     # Memory operations
│   │   │   ├── async_memory.py # Async memory management
│   │   │   └── evolution_prompts.py # Evolution prompts
│   │   ├── auth/             # Authentication middleware
│   │   │   ├── middleware.py # Auth middleware
│   │   │   └── supabase.py   # Supabase integration
│   │   ├── database/         # Database layer
│   │   │   └── base.py       # Database connection
│   │   ├── models.py         # SQLAlchemy models
│   │   ├── schemas.py        # Pydantic schemas
│   │   ├── mcp_server.py     # MCP protocol implementation
│   │   └── config.py         # Configuration management
│   ├── alembic/              # Database migrations
│   ├── tests/                # Test suite
│   ├── Dockerfile            # API container configuration
│   └── requirements.txt      # Python dependencies
├── memory-ui/                # Frontend Web Dashboard
│   ├── app/                  # Next.js app directory
│   │   ├── layout.tsx        # Root layout
│   │   ├── page.tsx          # Main dashboard
│   │   ├── login/            # Login page
│   │   ├── settings/         # Settings page
│   │   └── providers.tsx     # Context providers
│   ├── components/           # React components
│   │   ├── ui/               # Shadcn/UI components
│   │   ├── auth/             # Authentication components
│   │   ├── layout/           # Layout components
│   │   └── UserSwitcher.tsx  # User switching component
│   ├── hooks/                # Custom React hooks
│   │   ├── useMemoriesApi.ts # Memory operations
│   │   └── useEvolutionRealtime.ts # Evolution updates
│   ├── lib/                  # Utility libraries
│   │   ├── api.ts            # API client
│   │   ├── auth/             # Auth utilities
│   │   └── supabase.ts       # Supabase client
│   ├── store/                # Redux store
│   │   ├── store.ts          # Store configuration
│   │   ├── memoriesSlice.ts  # Memory state
│   │   └── evolutionSlice.ts # Evolution state
│   ├── tests/                # E2E tests
│   ├── Dockerfile            # UI container configuration
│   └── package.json          # Node.js dependencies
├── docker-compose.yml        # Multi-container orchestration
├── README.md                 # This file
├── API.md                    # API documentation
└── ARCHITECTURE.md           # Architecture documentation
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Remote Services Connection Issues
```bash
# Test all remote services
curl http://*************:6333/health  # Qdrant
curl http://*************:9000/health  # MinIO
nc -zv ************* 54322              # Supabase

# Check network connectivity
ping *************
ping *************
```

#### 2. Docker Container Issues
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs memory-mcp  # API server
docker-compose logs memory-ui   # UI server

# Restart services
docker-compose restart
```

#### 3. User Data Isolation
```bash
# Verify user configuration
docker exec memory-mcp env | grep USER

# Check user-specific data
curl http://localhost:8765/health | jq '.user_id'
```

#### 4. MCP Server Issues
```bash
# Check MCP server health
curl http://localhost:8765/health

# Verify MCP tools are available
curl http://localhost:8765/mcp/claude/tools
```

### Performance Optimization

- **Response Time**: Optimized for <200ms with local caching
- **Concurrent Users**: Supports multiple users with isolated data
- **Memory Usage**: Efficient chunking for large texts (>2000 words)
- **Network Latency**: Remote services may add 10-20ms

## 🤝 Contributing

We welcome contributions! Please see our development guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Development Setup

```bash
# Clone your fork
git clone https://github.com/yourusername/memory-master.git
cd memory-master

# Install dependencies
cd api && pip install -r requirements.txt
cd ../memory-ui && npm install

# Run tests
cd ../api && pytest
cd ../memory-ui && npm run test:e2e
```

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: See `API.md` and `ARCHITECTURE.md`
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join our community discussions
- **Email**: Contact the development team for enterprise support

---

**Memory Master** - Your intelligent memory layer for AI assistants, designed for the AY business operations team.

Built with ❤️ by Aung Hein Aye.