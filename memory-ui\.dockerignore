# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output
.next/
dist/
build/

# Test files and results
test-results/
playwright-report/
coverage/
.nyc_output/

# Screenshots and media
screenshots/
tests/screenshots/
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.webm
*.mp4
*.zip

# Logs
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Git
.git/
.gitignore