import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '@supabase/supabase-js';
import { getCurrentMemoryUserId, getDisplayName } from '../lib/auth/userMapping';

interface ProfileState {
  userId: string;
  userEmail: string | null;
  displayName: string;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const initialState: ProfileState = {
  userId: '',
  userEmail: null,
  displayName: '',
  isAuthenticated: false,
  isLoading: true,
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    setUserFromAuth: (state, action: PayloadAction<User | null>) => {
      const supabaseUser = action.payload;
      if (supabaseUser) {
        const memoryUserId = getCurrentMemoryUserId(supabaseUser);
        const displayName = getDisplayName(memoryUserId);
        state.userId = memoryUserId;
        state.userEmail = supabaseUser.email || null;
        state.displayName = displayName;
        state.isAuthenticated = true;
      } else {
        state.userId = '';
        state.userEmail = null;
        state.displayName = '';
        state.isAuthenticated = false;
      }
      state.isLoading = false;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    clearUser: (state) => {
      state.userId = '';
      state.userEmail = null;
      state.displayName = '';
      state.isAuthenticated = false;
      state.isLoading = false;
    }
  }
});

export const { setUserFromAuth, setLoading, clearUser } = profileSlice.actions;
export default profileSlice.reducer;