#!/bin/bash
# Linux/WSL Setup Script for Memory Master
set -e

echo "🐧 Setting up Memory Master for Linux/WSL environment..."

# Detect environment
if grep -q microsoft /proc/version; then
    echo "📋 Detected WSL environment"
    PLATFORM="wsl"
else
    echo "📋 Detected Linux environment"
    PLATFORM="linux"
fi

# Set environment variables for Linux
export QDRANT_HOST=*************
export SUPABASE_HOST=*************
export API_HOST=localhost

# Create Docker environment file
cat > .env.docker << EOF
# Platform: $PLATFORM
PLATFORM=$PLATFORM
QDRANT_HOST=$QDRANT_HOST
SUPABASE_HOST=$SUPABASE_HOST
API_URL=http://localhost:8765
EXTERNAL_QDRANT_IP=$QDRANT_HOST
EXTERNAL_SUPABASE_IP=$SUPABASE_HOST
COMPOSE_PROJECT_NAME=memory-master
EOF

echo "✅ Created .env.docker for $PLATFORM"

# Generate MCP configuration
sed "s/{{API_HOST}}/$API_HOST/g" .mcp.template.json > .mcp.json
echo "✅ Generated .mcp.json for $PLATFORM"

# Check Docker installation
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Installing..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y docker.io docker-compose
        sudo systemctl start docker
        sudo systemctl enable docker
        sudo usermod -aG docker $USER
        echo "✅ Docker installed. Please logout and login again."
    else
        echo "❌ Cannot install Docker automatically. Please install Docker manually."
        exit 1
    fi
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose not found. Installing..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get install -y docker-compose
    else
        echo "❌ Cannot install Docker Compose automatically. Please install manually."
        exit 1
    fi
fi

echo "✅ Docker and Docker Compose are ready"

# Build and start containers
echo "🚀 Building and starting containers..."
docker-compose --env-file .env.docker up -d --build

echo "🎉 Setup complete for $PLATFORM!"
echo "📡 Services:"
echo "   - API: http://localhost:8765"
echo "   - UI: http://localhost:3210"
echo "   - MCP: Configured in .mcp.json"
echo ""
echo "🔍 To check status: docker-compose ps"
echo "📋 To view logs: docker-compose logs -f"
echo "🛑 To stop: docker-compose down"